<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/widget_common_bg"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/budget_edit_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/budget_add_new"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/budget_edit_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/receipt_关闭"
            android:src="@drawable/ic_close"
            app:tint="@color/grey" />

    </LinearLayout>

    <!-- 预算类型选择 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/budget_category_label"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <RadioGroup
            android:id="@+id/budget_edit_type_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/budget_edit_type_total"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:checked="true"
                android:text="@string/budget_total_type"
                android:textSize="14sp" />

            <RadioButton
                android:id="@+id/budget_edit_type_category"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/budget_category_type"
                android:textSize="14sp" />

        </RadioGroup>

    </LinearLayout>

    <!-- 分类选择（仅在选择分类预算时显示） -->
    <LinearLayout
        android:id="@+id/budget_edit_category_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/budget_选择分类"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <Spinner
            android:id="@+id/budget_edit_category_spinner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp" />

    </LinearLayout>

    <!-- 预算金额 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/budget_amount"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/budget_edit_amount"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/style_tag_management_item_bg"
            android:hint="@string/budget_amount_hint"
            android:inputType="numberDecimal"
            android:paddingHorizontal="12dp"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- 预算周期 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/budget_period_label"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <Spinner
            android:id="@+id/budget_edit_period_spinner"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp" />

    </LinearLayout>

    <!-- 预警阈值 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/budget_alert_threshold_label"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/budget_edit_threshold_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="80%"
                android:textColor="@color/HuaQing"
                android:textSize="14sp"
                android:textStyle="bold" />

        </LinearLayout>

        <SeekBar
            android:id="@+id/budget_edit_threshold_seekbar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:max="100"
            android:progress="80" />

    </LinearLayout>

    <!-- 备注 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/budget_remark"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/budget_edit_remark"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/style_tag_management_item_bg"
            android:hint="@string/budget_remark_hint"
            android:inputType="text"
            android:paddingHorizontal="12dp"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- 启用状态 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/budget_is_active"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <androidx.appcompat.widget.SwitchCompat
            android:id="@+id/budget_edit_active_switch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true" />

    </LinearLayout>

    <!-- 操作按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/budget_edit_cancel"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:text="@string/取消"
            android:textColor="@color/grey" />

        <Button
            android:id="@+id/budget_edit_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:backgroundTint="@color/HuaQing"
            android:text="@string/budget_保存"
            android:textColor="@color/white" />

    </LinearLayout>

</LinearLayout>
