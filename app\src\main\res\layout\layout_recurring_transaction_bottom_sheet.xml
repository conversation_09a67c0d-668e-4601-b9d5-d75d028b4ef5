<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_background">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题栏 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingBottom="16dp">

            <TextView
                android:id="@+id/recurring_transaction_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="新增周期记账"
                android:textColor="@color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <ImageView
                android:id="@+id/recurring_transaction_close"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/receipt_关闭"
                android:src="@drawable/ic_close"
                app:tint="@color/black" />

        </LinearLayout>

        <!-- 基本信息 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="基本信息"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- 名称 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="周期记账名称">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/recurring_transaction_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 金额 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="金额">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/recurring_transaction_amount"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="numberDecimal"
                android:maxLines="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 类型选择 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="类型"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <RadioGroup
            android:id="@+id/recurring_transaction_type_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/recurring_transaction_type_income"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="收入" />

            <RadioButton
                android:id="@+id/recurring_transaction_type_expense"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:checked="true"
                android:text="支出" />

            <RadioButton
                android:id="@+id/recurring_transaction_type_transfer"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="转账" />

        </RadioGroup>

        <!-- 备注 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:hint="备注（可选）">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/recurring_transaction_remark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text"
                android:maxLines="3" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 时间设置 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="时间设置"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- 开始时间 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:text="开始时间"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/recurring_transaction_start_date"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/edit_text_background"
            android:gravity="center_vertical"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:text="选择开始时间"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <!-- 结束时间 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:text="结束时间（可选）"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/recurring_transaction_end_date"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginBottom="24dp"
            android:background="@drawable/edit_text_background"
            android:gravity="center_vertical"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:text="选择结束时间（可选）"
            android:textColor="@color/black"
            android:textSize="16sp" />

        <!-- 重复设置 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="重复设置"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- 重复类型 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="4dp"
            android:text="重复周期"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <Spinner
            android:id="@+id/recurring_transaction_repeat_type"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/edit_text_background" />

        <!-- 重复间隔 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="32dp"
            android:hint="重复间隔（如每2天、每3周等）">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/recurring_transaction_repeat_interval"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="number"
                android:maxLines="1"
                android:text="1" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 保存按钮 -->
        <Button
            android:id="@+id/recurring_transaction_save"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/button_primary"
            android:text="保存"
            android:textColor="@color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

</ScrollView>
