package com.example.likeqianwang.ui.budget_settings;

import android.app.Dialog;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RadioGroup;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.widget.SwitchCompat;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.Budget;
import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.R;
import com.example.likeqianwang.ViewModel.BudgetViewModel;
import com.example.likeqianwang.ViewModel.TransactionCategoryViewModel;
import com.example.likeqianwang.adapters.BudgetCategoryAdapter;
import com.example.likeqianwang.adapters.BudgetCategorySelectionAdapter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

public class BudgetSettingsActivity extends AppCompatActivity implements 
        BudgetCategoryAdapter.OnBudgetCategoryListener,
        BudgetCategorySelectionAdapter.OnCategorySelectionListener {

    private BudgetViewModel budgetViewModel;
    private TransactionCategoryViewModel categoryViewModel;
    
    // UI组件
    private ImageView backButton;
    private TextView saveButton;
    private RadioGroup budgetPeriodGroup;
    private EditText totalBudgetAmount;
    private SeekBar totalAlertThreshold;
    private TextView totalAlertPercentage;
    private TextView addCategoryBudget;
    private RecyclerView categoryBudgetList;
    private SwitchCompat notificationSwitch;
    private SwitchCompat overBudgetAlertSwitch;
    
    // 适配器
    private BudgetCategoryAdapter categoryBudgetAdapter;
    private List<BudgetCategoryItem> categoryBudgetItems;
    
    // 当前设置
    private String currentPeriod = "MONTHLY";
    private int currentYear;
    private int currentMonth;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_budget_settings);
        
        initCurrentPeriod();
        initViews();
        initViewModels();
        setupListeners();
        loadExistingBudgets();
    }
    
    private void initCurrentPeriod() {
        Calendar calendar = Calendar.getInstance();
        currentYear = calendar.get(Calendar.YEAR);
        currentMonth = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH is 0-based
    }
    
    private void initViews() {
        backButton = findViewById(R.id.budget_settings_back);
        saveButton = findViewById(R.id.budget_settings_save);
        budgetPeriodGroup = findViewById(R.id.budget_period_group);
        totalBudgetAmount = findViewById(R.id.budget_total_amount);
        totalAlertThreshold = findViewById(R.id.budget_total_alert_threshold);
        totalAlertPercentage = findViewById(R.id.budget_total_alert_percentage);
        addCategoryBudget = findViewById(R.id.budget_add_category_budget);
        categoryBudgetList = findViewById(R.id.budget_category_list);
        notificationSwitch = findViewById(R.id.budget_notification_switch);
        overBudgetAlertSwitch = findViewById(R.id.budget_over_budget_alert_switch);
        
        // 设置分类预算列表
        categoryBudgetItems = new ArrayList<>();
        categoryBudgetAdapter = new BudgetCategoryAdapter(categoryBudgetItems, this);
        categoryBudgetList.setLayoutManager(new LinearLayoutManager(this));
        categoryBudgetList.setAdapter(categoryBudgetAdapter);
    }
    
    private void initViewModels() {
        budgetViewModel = new ViewModelProvider(this).get(BudgetViewModel.class);
        categoryViewModel = new ViewModelProvider(this).get(TransactionCategoryViewModel.class);
    }
    
    private void setupListeners() {
        // 返回按钮
        backButton.setOnClickListener(v -> finish());
        
        // 保存按钮
        saveButton.setOnClickListener(v -> saveBudgetSettings());
        
        // 预算周期选择
        budgetPeriodGroup.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.budget_period_monthly) {
                currentPeriod = "MONTHLY";
            } else if (checkedId == R.id.budget_period_weekly) {
                currentPeriod = "WEEKLY";
            } else if (checkedId == R.id.budget_period_yearly) {
                currentPeriod = "YEARLY";
            }
            loadExistingBudgets();
        });
        
        // 总预算预警阈值
        totalAlertThreshold.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                totalAlertPercentage.setText(progress + "%");
            }
            
            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}
            
            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });
        
        // 添加分类预算
        addCategoryBudget.setOnClickListener(v -> showCategorySelectionDialog());
    }
    
    private void loadExistingBudgets() {
        // 加载总预算
        budgetViewModel.getTotalBudget(currentPeriod, currentYear, currentMonth)
                .observe(this, budget -> {
                    if (budget != null) {
                        totalBudgetAmount.setText(budget.getBudgetAmount().toString());
                        int threshold = (int) (budget.getAlertThreshold() * 100);
                        totalAlertThreshold.setProgress(threshold);
                        totalAlertPercentage.setText(threshold + "%");
                    }
                });
        
        // 加载分类预算
        budgetViewModel.getAllCategoryBudgets(currentPeriod, currentYear, currentMonth)
                .observe(this, budgets -> {
                    categoryBudgetItems.clear();
                    if (budgets != null) {
                        for (Budget budget : budgets) {
                            // 获取分类信息
                            categoryViewModel.getCategoryById(budget.getCategoryId())
                                    .observe(this, category -> {
                                        if (category != null) {
                                            BudgetCategoryItem item = new BudgetCategoryItem();
                                            item.budget = budget;
                                            item.category = category;
                                            categoryBudgetItems.add(item);
                                            categoryBudgetAdapter.notifyDataSetChanged();
                                        }
                                    });
                        }
                    }
                });
    }
    
    private void showCategorySelectionDialog() {
        Dialog dialog = new Dialog(this);
        dialog.setContentView(R.layout.dialog_budget_category_selection);
        
        RecyclerView categoryList = dialog.findViewById(R.id.budget_category_selection_list);
        TextView cancelButton = dialog.findViewById(R.id.budget_category_selection_cancel);
        TextView confirmButton = dialog.findViewById(R.id.budget_category_selection_confirm);
        
        // 获取支出分类列表
        categoryViewModel.getExpenseCategories().observe(this, categories -> {
            if (categories != null) {
                // 过滤掉已经设置预算的分类
                List<TransactionCategory> availableCategories = new ArrayList<>();
                for (TransactionCategory category : categories) {
                    boolean alreadyExists = false;
                    for (BudgetCategoryItem item : categoryBudgetItems) {
                        if (item.category.getCategoryId() == category.getCategoryId()) {
                            alreadyExists = true;
                            break;
                        }
                    }
                    if (!alreadyExists) {
                        availableCategories.add(category);
                    }
                }
                
                BudgetCategorySelectionAdapter adapter = new BudgetCategorySelectionAdapter(
                        availableCategories, this);
                categoryList.setLayoutManager(new LinearLayoutManager(this));
                categoryList.setAdapter(adapter);
            }
        });
        
        cancelButton.setOnClickListener(v -> dialog.dismiss());
        confirmButton.setOnClickListener(v -> {
            // 处理选择的分类
            dialog.dismiss();
        });
        
        dialog.show();
    }
    
    private void saveBudgetSettings() {
        try {
            // 保存总预算
            String totalAmountStr = totalBudgetAmount.getText().toString().trim();
            if (!totalAmountStr.isEmpty()) {
                BigDecimal totalAmount = new BigDecimal(totalAmountStr);
                double alertThreshold = totalAlertThreshold.getProgress() / 100.0;
                
                budgetViewModel.createOrUpdateTotalBudget(totalAmount, currentPeriod, 
                        currentYear, currentMonth, alertThreshold);
            }
            
            // 保存分类预算
            for (BudgetCategoryItem item : categoryBudgetItems) {
                if (item.isModified) {
                    budgetViewModel.createOrUpdateCategoryBudget(
                            item.category.getCategoryId(),
                            item.budgetAmount,
                            currentPeriod,
                            currentYear,
                            currentMonth,
                            item.alertThreshold
                    );
                }
            }
            
            Toast.makeText(this, "预算设置已保存", Toast.LENGTH_SHORT).show();
            finish();
            
        } catch (NumberFormatException e) {
            Toast.makeText(this, "请输入有效的金额", Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            Toast.makeText(this, "保存失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }
    
    @Override
    public void onBudgetCategoryAmountChanged(int position, BigDecimal amount) {
        if (position < categoryBudgetItems.size()) {
            categoryBudgetItems.get(position).budgetAmount = amount;
            categoryBudgetItems.get(position).isModified = true;
        }
    }
    
    @Override
    public void onBudgetCategoryAlertThresholdChanged(int position, double threshold) {
        if (position < categoryBudgetItems.size()) {
            categoryBudgetItems.get(position).alertThreshold = threshold;
            categoryBudgetItems.get(position).isModified = true;
        }
    }
    
    @Override
    public void onBudgetCategoryDelete(int position) {
        if (position < categoryBudgetItems.size()) {
            BudgetCategoryItem item = categoryBudgetItems.get(position);
            if (item.budget != null) {
                budgetViewModel.deleteBudget(item.budget);
            }
            categoryBudgetItems.remove(position);
            categoryBudgetAdapter.notifyItemRemoved(position);
        }
    }
    
    @Override
    public void onCategorySelected(TransactionCategory category) {
        // 添加新的分类预算项
        BudgetCategoryItem item = new BudgetCategoryItem();
        item.category = category;
        item.budgetAmount = BigDecimal.ZERO;
        item.alertThreshold = 0.8;
        item.isModified = true;
        
        categoryBudgetItems.add(item);
        categoryBudgetAdapter.notifyItemInserted(categoryBudgetItems.size() - 1);
    }
    
    // 预算分类项数据类
    public static class BudgetCategoryItem {
        public Budget budget;
        public TransactionCategory category;
        public BigDecimal budgetAmount = BigDecimal.ZERO;
        public double alertThreshold = 0.8;
        public boolean isModified = false;
    }
}
