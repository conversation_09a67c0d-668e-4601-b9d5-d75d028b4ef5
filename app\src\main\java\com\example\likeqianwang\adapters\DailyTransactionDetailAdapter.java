package com.example.likeqianwang.adapters;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.Entity.Transactions;
import com.example.likeqianwang.R;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class DailyTransactionDetailAdapter extends RecyclerView.Adapter<DailyTransactionDetailAdapter.TransactionViewHolder> {
    private final Context context;
    private List<Transactions> transactions;
    private final AppDatabase database;
    private final ExecutorService executorService;
    private final DecimalFormat amountFormatter;

    // 点击事件监听器
    private OnTransactionClickListener onTransactionClickListener;

    public DailyTransactionDetailAdapter(Context context, List<Transactions> transactions) {
        this.context = context;
        this.transactions = transactions;
        this.database = AppDatabase.getInstance(context);
        this.executorService = Executors.newSingleThreadExecutor();
        this.amountFormatter = new DecimalFormat("#,##0.00");
    }

    @NonNull
    @Override
    public TransactionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.style_daily_in_out_detail_view, parent, false);
        return new TransactionViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull TransactionViewHolder holder, int position) {
        Transactions transaction = transactions.get(position);
        holder.bind(transaction);
    }

    @Override
    public int getItemCount() {
        return transactions != null ? transactions.size() : 0;
    }

    /**
     * 更新交易数据
     */
    public void updateTransactions(List<Transactions> newTransactions) {
        this.transactions = newTransactions;
        notifyDataSetChanged();
    }

    /**
     * 设置交易点击监听器
     */
    public void setOnTransactionClickListener(OnTransactionClickListener listener) {
        this.onTransactionClickListener = listener;
    }

    class TransactionViewHolder extends RecyclerView.ViewHolder {
        private final ImageView iconView;
        private final TextView kindTextView;
        private final TextView remarkTextView;
        private final LinearLayout tagListLayout;
        private final TextView amountTextView;
        private final TextView accountTextView;

        public TransactionViewHolder(@NonNull View itemView) {
            super(itemView);
            iconView = itemView.findViewById(R.id.receipt_Daily_InOut_icon);
            kindTextView = itemView.findViewById(R.id.receipt_Daily_InOut_kind);
            remarkTextView = itemView.findViewById(R.id.receipt_Daily_InOut_remark);
            tagListLayout = itemView.findViewById(R.id.receipt_Daily_InOut_taglist);
            amountTextView = itemView.findViewById(R.id.receipt_Daily_InOut_amount);
            accountTextView = itemView.findViewById(R.id.receipt_Daily_InOut_account);
        }

        public void bind(Transactions transaction) {
            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onTransactionClickListener != null) {
                    onTransactionClickListener.onTransactionClick(transaction);
                }
            });

            // 异步加载详细信息
            executorService.execute(() -> {
                try {
                    // 获取分类信息
                    TransactionCategory category = database.transactionCategoryDao()
                            .getCategoryByIdSync(transaction.getCategoryId());

                    // 获取账户信息
                    Account fromAccount = database.accountDao()
                            .getAccountByIdSync(transaction.getFromAccountId());
                    Account toAccount;
                    if (transaction.getToAccountId() != null) {
                        toAccount = database.accountDao()
                                .getAccountByIdSync(transaction.getToAccountId());
                    } else {
                        toAccount = null;
                    }

                    // 获取标签信息
                    List<TransactionTag> tags = database.transactionTagCrossRefDao()
                            .getTagsForTransaction(transaction.getTransactionId());

                    // 在主线程更新UI
                    itemView.post(() -> updateUI(transaction, category, fromAccount, toAccount, tags));

                } catch (Exception e) {
                    e.printStackTrace();
                    // 发生错误时在主线程显示基本信息
                    itemView.post(() -> updateUIWithBasicInfo(transaction));
                }
            });
        }

        private void updateUI(Transactions transaction, TransactionCategory category,
                              Account fromAccount, Account toAccount, List<TransactionTag> tags) {

            // 设置交易类型图标和颜色
            String type = transaction.getType();
            int iconColor;
            String amountPrefix;

            switch (type) {
                case "INCOME":
                    iconColor = ContextCompat.getColor(context, R.color.WaLv);
                    amountPrefix = "+";
                    break;
                case "EXPENSE":
                    iconColor = ContextCompat.getColor(context, R.color.ChaHuaHong);
                    amountPrefix = "-";
                    break;
                case "TRANSFER":
                    iconColor = ContextCompat.getColor(context, R.color.HuaQing);
                    amountPrefix = "";
                    break;
                default:
                    iconColor = ContextCompat.getColor(context, R.color.grey);
                    amountPrefix = "";
                    break;
            }

            iconView.setColorFilter(iconColor);

            // 设置分类名称
            if (category != null) {
                kindTextView.setText(category.getCategoryName());
            } else {
                kindTextView.setText("未知分类");
            }

            // 设置备注
            String remark = transaction.getRemark();
            if (remark != null && !remark.trim().isEmpty()) {
                remarkTextView.setText(remark);
                remarkTextView.setVisibility(View.VISIBLE);
            } else {
                remarkTextView.setVisibility(View.GONE);
            }

            // 设置标签
            updateTagList(tags);

            // 设置金额
            BigDecimal amount = transaction.getAmount();
            String formattedAmount = amountPrefix + "¥" + amountFormatter.format(amount);
            amountTextView.setText(formattedAmount);
            amountTextView.setTextColor(iconColor);

            // 设置账户信息
            String accountText = "";
            if ("TRANSFER".equals(type)) {
                // 转账显示：从账户 → 到账户
                String fromAccountName = fromAccount != null ? fromAccount.getAccountName() : "未知账户";
                String toAccountName = toAccount != null ? toAccount.getAccountName() : "未知账户";
                accountText = fromAccountName + " → " + toAccountName;
            } else {
                // 收入/支出显示账户名称
                accountText = fromAccount != null ? fromAccount.getAccountName() : "未知账户";
            }
            accountTextView.setText(accountText);
        }

        private void updateUIWithBasicInfo(Transactions transaction) {
            // 显示基本信息，不依赖数据库查询
            kindTextView.setText("加载中...");
            remarkTextView.setText(transaction.getRemark());

            String type = transaction.getType();
            int iconColor;
            String amountPrefix;

            switch (type) {
                case "INCOME":
                    iconColor = ContextCompat.getColor(context, R.color.WaLv);
                    amountPrefix = "+";
                    break;
                case "EXPENSE":
                    iconColor = ContextCompat.getColor(context, R.color.ChaHuaHong);
                    amountPrefix = "-";
                    break;
                case "TRANSFER":
                    iconColor = ContextCompat.getColor(context, R.color.HuaQing);
                    amountPrefix = "";
                    break;
                default:
                    iconColor = ContextCompat.getColor(context, R.color.grey);
                    amountPrefix = "";
                    break;
            }

            iconView.setColorFilter(iconColor);

            BigDecimal amount = transaction.getAmount();
            String formattedAmount = amountPrefix + "¥" + amountFormatter.format(amount);
            amountTextView.setText(formattedAmount);
            amountTextView.setTextColor(iconColor);

            accountTextView.setText("加载中...");
        }

        private void updateTagList(List<TransactionTag> tags) {
            tagListLayout.removeAllViews();

            if (tags != null && !tags.isEmpty()) {
                for (TransactionTag tag : tags) {
                    View tagView = LayoutInflater.from(context)
                            .inflate(R.layout.style_daily_in_out_detail_tag_view, tagListLayout, false);

                    TextView tagTextView = tagView.findViewById(R.id.receipt_Daily_InOut_tags);
                    tagTextView.setText(tag.getTagName());

                    // 在receipts页面：标签底色使用自定义颜色的70%透明度HEX值
                    if (tag.getTagColor() != null && !tag.getTagColor().isEmpty()) {
                        try {
                            int customColor = Color.parseColor(tag.getTagColor());

                            // 创建70%透明度的背景色
                            int transparentColor = Color.argb(
                                    (int) (255 * 0.7), // 70%透明度
                                    Color.red(customColor),
                                    Color.green(customColor),
                                    Color.blue(customColor)
                            );

                            // 创建圆角背景drawable
                            android.graphics.drawable.GradientDrawable drawable = new android.graphics.drawable.GradientDrawable();
                            drawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);
                            drawable.setCornerRadius(8f); // 圆角
                            drawable.setColor(transparentColor);

                            tagView.setBackground(drawable);
                            tagTextView.setTextColor(customColor); // 文字颜色使用原始自定义颜色
                        } catch (IllegalArgumentException e) {
                            // 颜色格式错误，使用默认颜色
                            tagTextView.setTextColor(Color.parseColor("#ee3f4d")); // 默认茶花红
                        }
                    } else {
                        // 没有自定义颜色，使用默认颜色
                        tagTextView.setTextColor(Color.parseColor("#ee3f4d")); // 默认茶花红
                    }

                    tagListLayout.addView(tagView);
                }
            }
        }
    }

    /**
     * 交易点击事件监听器接口
     */
    public interface OnTransactionClickListener {
        void onTransactionClick(Transactions transaction);
    }

}
