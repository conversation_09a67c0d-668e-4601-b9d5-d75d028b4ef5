package com.example.likeqianwang.widgets;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.example.likeqianwang.R;
import com.example.likeqianwang.ViewModel.ReceiptViewModel;
import com.example.likeqianwang.databinding.WidgetInAndOutBinding;

import java.math.BigDecimal;
import java.text.DecimalFormat;

public class Widget_InAndOut extends Fragment {

    private WidgetInAndOutBinding binding;
    private ReceiptViewModel receiptViewModel;
    private TextView totalExpenseTextView;
    private TextView totalIncomeTextView;
    private TextView balanceTextView;
    private DecimalFormat amountFormatter;

    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = WidgetInAndOutBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 初始化视图
        totalExpenseTextView = binding.widgetInOutTotalExpenseAmount;
        totalIncomeTextView = binding.widgetInOutTotalIncomeAmount;
        balanceTextView = binding.widgetInOutBalanceAmount;

        // 初始化格式化工具
        amountFormatter = new DecimalFormat("¥#,##0.00");

        // 获取ViewModel
        receiptViewModel = new ViewModelProvider(requireActivity()).get(ReceiptViewModel.class);

        // 观察月度收支数据变化
        observeMonthlyTransactionSummary();
    }

    private void observeMonthlyTransactionSummary() {
        // 观察月度收支汇总数据
        receiptViewModel.getMonthlyTransactionSummary().observe(getViewLifecycleOwner(), summary -> {
            if (summary != null) {
                // 更新UI显示
                BigDecimal income = summary.totalIncome != null ? summary.totalIncome : BigDecimal.ZERO;
                BigDecimal expense = summary.totalExpense != null ? summary.totalExpense : BigDecimal.ZERO;
                BigDecimal balance = income.subtract(expense);

                totalIncomeTextView.setText(amountFormatter.format(income));
                totalExpenseTextView.setText(amountFormatter.format(expense));
                balanceTextView.setText(amountFormatter.format(balance));
            } else {
                // 如果没有数据，显示零值
                totalIncomeTextView.setText(amountFormatter.format(BigDecimal.ZERO));
                totalExpenseTextView.setText(amountFormatter.format(BigDecimal.ZERO));
                balanceTextView.setText(amountFormatter.format(BigDecimal.ZERO));
            }
        });
    }
}