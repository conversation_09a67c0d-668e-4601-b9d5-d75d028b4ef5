package com.example.likeqianwang.adapters;

import android.content.Context;
import android.graphics.Color;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.R;
import com.google.android.flexbox.FlexboxLayout;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class RecordingTagCategoryAdapter extends RecyclerView.Adapter<RecordingTagCategoryAdapter.CategoryViewHolder>{
    private final Context context;
    private final List<String> categories;
    private final Map<String, List<TransactionTag>> categorizedTags;
    private final List<TransactionTag> selectedTags;
    private OnTagClickListener onTagClickListener;

    public RecordingTagCategoryAdapter(Context context, List<String> categories,
                                       Map<String, List<TransactionTag>> categorizedTags,
                                       List<TransactionTag> selectedTags) {
        this.context = context;
        this.categories = categories != null ? categories : new ArrayList<>();
        this.categorizedTags = categorizedTags;
        this.selectedTags = selectedTags != null ? selectedTags : new ArrayList<>();
    }

    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_recording_tag_category, parent, false);
        return new CategoryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        String category = categories.get(position);
        holder.bind(category);
    }

    @Override
    public int getItemCount() {
        return categories.size();
    }

    public void setOnTagClickListener(OnTagClickListener listener) {
        this.onTagClickListener = listener;
    }

    public void updateSelectedTags(List<TransactionTag> newSelectedTags) {
        // 添加调试日志
        Log.d("RecordingTagCategoryAdapter", "Updating selected tags. Count: " +
                (newSelectedTags != null ? newSelectedTags.size() : 0));

        selectedTags.clear();
        if (newSelectedTags != null) {
            selectedTags.addAll(newSelectedTags);
        }
        notifyDataSetChanged();
    }

    class CategoryViewHolder extends RecyclerView.ViewHolder {
        private final TextView tvCategoryName;
        private final FlexboxLayout flexboxTags;

        public CategoryViewHolder(@NonNull View itemView) {
            super(itemView);
            tvCategoryName = itemView.findViewById(R.id.tv_category_name);
            flexboxTags = itemView.findViewById(R.id.flexbox_tags);
        }

        public void bind(String category) {
            tvCategoryName.setText(category);
            flexboxTags.removeAllViews();

            List<TransactionTag> tags = categorizedTags.get(category);
            if (tags != null) {
                for (TransactionTag tag : tags) {
                    View tagView = createTagView(tag);
                    flexboxTags.addView(tagView);
                }
            }
        }

        private View createTagView(TransactionTag tag) {
            View tagView = LayoutInflater.from(context).inflate(R.layout.style_recording_tag_selectable, flexboxTags, false);
            TextView tvTagName = tagView.findViewById(R.id.tv_tag_name);

            tvTagName.setText(tag.getTagName());

            // 设置选中状态
            boolean isSelected = isTagSelected(tag);
            tagView.setSelected(isSelected);

            // 根据选中状态和自定义颜色设置显示样式
            if (tag.getTagColor() != null && !tag.getTagColor().isEmpty()) {
                try {
                    int customColor = Color.parseColor(tag.getTagColor());

                    if (isSelected) {
                        // 选中状态：底色为自定义颜色的70%透明度，文字颜色与底色相同
                        int transparentColor = Color.argb(
                                (int) (255 * 0.7), // 70%透明度
                                Color.red(customColor),
                                Color.green(customColor),
                                Color.blue(customColor)
                        );
                        tvTagName.setBackgroundColor(transparentColor);
                        tvTagName.setTextColor(customColor);
                    } else {
                        // 未选中状态：应用widget_tag_item_bg背景，显示自定义颜色边框，文字颜色为自定义颜色
                        tvTagName.setBackgroundResource(R.drawable.widget_tag_item_bg);
                        tvTagName.getBackground().setTint(Color.TRANSPARENT); // 清除背景色

                        // 创建带自定义颜色边框的drawable
                        android.graphics.drawable.GradientDrawable drawable = new android.graphics.drawable.GradientDrawable();
                        drawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);
                        drawable.setCornerRadius(16f); // 与原始样式保持一致
                        drawable.setStroke(2, customColor); // 自定义颜色边框
                        drawable.setColor(Color.TRANSPARENT); // 透明背景

                        tvTagName.setBackground(drawable);
                        tvTagName.setTextColor(customColor);
                    }
                } catch (IllegalArgumentException e) {
                    // 如果颜色格式不正确，使用默认背景和选择器
                    tvTagName.setBackgroundResource(R.drawable.selector_tag_item);
                    tvTagName.setTextColor(context.getColorStateList(R.color.selector_tag_text));
                }
            } else {
                // 没有自定义颜色，使用默认样式
                tvTagName.setBackgroundResource(R.drawable.selector_tag_item);
                tvTagName.setTextColor(context.getColorStateList(R.color.selector_tag_text));
            }

            // 设置点击事件
            tagView.setOnClickListener(v -> {
                // 添加调试日志
                Log.d("RecordingTagCategoryAdapter", "Tag clicked: " + tag.getTagName() +
                        ", ID: " + tag.getTagId());

                if (onTagClickListener != null) {
                    onTagClickListener.onTagClick(tag);
                }
            });

            return tagView;
        }

        private boolean isTagSelected(TransactionTag tag) {
            for (TransactionTag selectedTag : selectedTags) {
                if (selectedTag.getTagId() == tag.getTagId()) {
                    return true;
                }
            }
            return false;
        }
    }

    public interface OnTagClickListener {
        void onTagClick(TransactionTag tag);
    }

}
