package com.example.likeqianwang.Entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.example.likeqianwang.Utils.Converters;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

@Entity(
        tableName = "budgets",
        indices = {
                @Index(value = {"budget_period", "budget_year", "budget_month"}),
                @Index(value = "category_id"),
                @Index(value = "budget_type")
        }
)
@TypeConverters(Converters.class)
public class Budget implements Serializable {
    @PrimaryKey
    @NonNull
    private String budgetId;

    @ColumnInfo(name = "budget_type")
    private String budgetType; // "TOTAL", "CATEGORY", "SUBCATEGORY"

    @ColumnInfo(name = "category_id")
    private Long categoryId; // null for total budget, category ID for category budget

    @ColumnInfo(name = "subcategory_id")
    private Long subcategoryId; // null for total/category budget, subcategory ID for subcategory budget

    @ColumnInfo(name = "budget_amount")
    private BigDecimal budgetAmount; // 预算金额

    @ColumnInfo(name = "budget_period")
    private String budgetPeriod; // "MONTHLY", "WEEKLY", "YEARLY"

    @ColumnInfo(name = "budget_year")
    private int budgetYear; // 预算年份

    @ColumnInfo(name = "budget_month")
    private int budgetMonth; // 预算月份 (1-12), 0 for yearly budget

    @ColumnInfo(name = "budget_week")
    private int budgetWeek; // 预算周数 (1-53), 0 for monthly/yearly budget

    @ColumnInfo(name = "is_active")
    private boolean isActive; // 是否启用

    @ColumnInfo(name = "alert_threshold")
    private double alertThreshold; // 预警阈值 (0.0-1.0, 如0.8表示80%)

    @ColumnInfo(name = "currency_symbol")
    private String currencySymbol; // 币种

    @ColumnInfo(name = "create_time")
    private Date createTime;

    @ColumnInfo(name = "update_time")
    private Date updateTime;

    @ColumnInfo(name = "remark")
    private String remark; // 备注

    // 提醒设置
    @ColumnInfo(name = "threshold_alert_enabled")
    private boolean thresholdAlertEnabled; // 是否启用阈值提醒

    @ColumnInfo(name = "over_budget_alert_enabled")
    private boolean overBudgetAlertEnabled; // 是否启用超预算提醒

    public Budget() {
        this.budgetId = UUID.randomUUID().toString();
        this.createTime = new Date();
        this.updateTime = new Date();
        this.currencySymbol = "CNY";
        this.isActive = true;
        this.alertThreshold = 0.8; // 默认80%预警
        this.thresholdAlertEnabled = true; // 默认启用阈值提醒
        this.overBudgetAlertEnabled = true; // 默认启用超预算提醒
    }

    // Getters and Setters
    @NonNull
    public String getBudgetId() {
        return budgetId;
    }

    public void setBudgetId(@NonNull String budgetId) {
        this.budgetId = budgetId;
    }

    public String getBudgetType() {
        return budgetType;
    }

    public void setBudgetType(String budgetType) {
        this.budgetType = budgetType;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getSubcategoryId() {
        return subcategoryId;
    }

    public void setSubcategoryId(Long subcategoryId) {
        this.subcategoryId = subcategoryId;
    }

    public BigDecimal getBudgetAmount() {
        return budgetAmount;
    }

    public void setBudgetAmount(BigDecimal budgetAmount) {
        this.budgetAmount = budgetAmount;
    }

    public String getBudgetPeriod() {
        return budgetPeriod;
    }

    public void setBudgetPeriod(String budgetPeriod) {
        this.budgetPeriod = budgetPeriod;
    }

    public int getBudgetYear() {
        return budgetYear;
    }

    public void setBudgetYear(int budgetYear) {
        this.budgetYear = budgetYear;
    }

    public int getBudgetMonth() {
        return budgetMonth;
    }

    public void setBudgetMonth(int budgetMonth) {
        this.budgetMonth = budgetMonth;
    }

    public int getBudgetWeek() {
        return budgetWeek;
    }

    public void setBudgetWeek(int budgetWeek) {
        this.budgetWeek = budgetWeek;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public double getAlertThreshold() {
        return alertThreshold;
    }

    public void setAlertThreshold(double alertThreshold) {
        this.alertThreshold = alertThreshold;
    }

    public String getCurrencySymbol() {
        return currencySymbol;
    }

    public void setCurrencySymbol(String currencySymbol) {
        this.currencySymbol = currencySymbol;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public boolean isThresholdAlertEnabled() {
        return thresholdAlertEnabled;
    }

    public void setThresholdAlertEnabled(boolean thresholdAlertEnabled) {
        this.thresholdAlertEnabled = thresholdAlertEnabled;
    }

    public boolean isOverBudgetAlertEnabled() {
        return overBudgetAlertEnabled;
    }

    public void setOverBudgetAlertEnabled(boolean overBudgetAlertEnabled) {
        this.overBudgetAlertEnabled = overBudgetAlertEnabled;
    }
}
