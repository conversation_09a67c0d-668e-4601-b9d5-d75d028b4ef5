package com.example.likeqianwang.Dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.example.likeqianwang.Entity.Notebook;

import java.util.Date;
import java.util.List;

@Dao
public interface NotebookDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(Notebook notebook);

    @Update
    void update(Notebook notebook);

    @Delete
    void delete(Notebook notebook);

    @Query("SELECT * FROM notebooks WHERE notebookId = :notebookId")
    LiveData<Notebook> getNotebookById(String notebookId);

    @Query("SELECT * FROM notebooks WHERE notebookId = :notebookId")
    Notebook getNotebookByIdSync(String notebookId);

    @Query("SELECT * FROM notebooks WHERE is_active = 1 ORDER BY is_default DESC, last_used_time DESC")
    LiveData<List<Notebook>> getAllActiveNotebooks();

    @Query("SELECT * FROM notebooks WHERE is_active = 1 ORDER BY is_default DESC, last_used_time DESC")
    List<Notebook> getAllActiveNotebooksSync();

    @Query("SELECT * FROM notebooks WHERE is_default = 1 AND is_active = 1 LIMIT 1")
    LiveData<Notebook> getDefaultNotebook();

    @Query("SELECT * FROM notebooks WHERE is_default = 1 AND is_active = 1 LIMIT 1")
    Notebook getDefaultNotebookSync();

    @Query("SELECT * FROM notebooks ORDER BY last_used_time DESC LIMIT 1")
    LiveData<Notebook> getCurrentNotebook();

    @Query("SELECT * FROM notebooks ORDER BY last_used_time DESC LIMIT 1")
    Notebook getCurrentNotebookSync();

    @Query("UPDATE notebooks SET is_default = 0")
    void clearAllDefaultFlags();

    @Query("UPDATE notebooks SET is_default = 1 WHERE notebookId = :notebookId")
    void setAsDefault(String notebookId);

    @Query("UPDATE notebooks SET last_used_time = :lastUsedTime WHERE notebookId = :notebookId")
    void updateLastUsedTime(String notebookId, Date lastUsedTime);

    @Query("SELECT COUNT(*) FROM notebooks WHERE is_active = 1")
    int getActiveNotebookCount();

    @Query("SELECT COUNT(*) FROM notebooks WHERE notebook_name = :name AND is_active = 1")
    int getNotebookCountByName(String name);

    @Query("SELECT COUNT(*) FROM notebooks WHERE notebook_name = :name AND notebookId != :excludeId AND is_active = 1")
    int getNotebookCountByNameExcluding(String name, String excludeId);

    @Query("UPDATE notebooks SET is_active = 0 WHERE notebookId = :notebookId")
    void softDelete(String notebookId);

    @Query("SELECT * FROM notebooks WHERE is_active = 1 AND notebook_name LIKE '%' || :query || '%' ORDER BY is_default DESC, last_used_time DESC")
    LiveData<List<Notebook>> searchNotebooks(String query);
}
