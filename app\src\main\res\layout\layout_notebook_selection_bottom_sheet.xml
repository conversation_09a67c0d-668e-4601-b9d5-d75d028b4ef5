<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/widget_common_bg"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/notebook_selection_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="选择账本"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/notebook_selection_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/receipt_关闭"
            android:src="@drawable/ic_close"
            app:tint="@color/grey" />

    </LinearLayout>

    <!-- 账本列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/notebook_selection_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:maxHeight="400dp"
        tools:listitem="@layout/item_notebook_selection" />

    <!-- 空状态 -->
    <LinearLayout
        android:id="@+id/notebook_selection_empty_state"
        android:layout_width="match_parent"
        android:layout_height="200dp"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:alpha="0.5"
            android:src="@drawable/ic_book"
            app:tint="@color/grey" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="暂无账本"
            android:textColor="@color/grey"
            android:textSize="16sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="请先创建账本"
            android:textColor="@color/grey"
            android:textSize="14sp" />

    </LinearLayout>

</LinearLayout>
