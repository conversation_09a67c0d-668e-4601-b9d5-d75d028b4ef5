<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/wallets_add_new_account_bank_selection"
    android:layout_width="match_parent"
    android:layout_height="800dp"
    android:background="@drawable/dialog_bottom_sheet_background"
    android:orientation="vertical"
    android:paddingStart="15dp"
    android:paddingTop="10dp"
    android:paddingEnd="15dp"
    android:paddingBottom="25dp">

    <!-- 顶部拖动条 -->
    <View
        android:id="@+id/drag_handle"
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_gravity="center_horizontal"
        android:layout_marginBottom="16dp"
        android:background="#DDDDDD" />

    <!-- 标题栏 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/wallets_account_back"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:text="@string/add_new_account_返回"
            android:textSize="16sp"
            app:drawableStartCompat="@drawable/icon_arrow_back"
            app:layout_constraintBottom_toBottomOf="@id/wallets_account_bank_selection_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@id/wallets_account_bank_selection_title" />

        <TextView
            android:id="@+id/wallets_account_bank_selection_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="10dp"
            android:text="@string/add_new_account_选择银行"
            android:textColor="@color/black"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 搜索框 -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/wallets_account_search_bank"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="0dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="10dp">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@android:drawable/ic_menu_search"
                app:tint="@color/grey" />

            <EditText
                android:id="@+id/et_wallets_account_bank_search_input"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:background="@null"
                android:hint="@string/add_new_account_hint_搜索"
                android:imeOptions="actionSearch"
                android:importantForAutofill="no"
                android:inputType="text"
                android:maxLines="1"
                android:textSize="15sp"
                tools:ignore="TouchTargetSizeCheck" />
        </LinearLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- 银行列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_bank_list"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="10dp"
        android:contentDescription="@string/wallets_desc_银行列表" />

</LinearLayout>