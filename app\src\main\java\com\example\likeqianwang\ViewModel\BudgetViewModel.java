package com.example.likeqianwang.ViewModel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.example.likeqianwang.Dao.BudgetDao;
import com.example.likeqianwang.Entity.Budget;
import com.example.likeqianwang.Repository.BudgetRepository;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.List;

public class BudgetViewModel extends AndroidViewModel {
    private final BudgetRepository repository;
    private final MutableLiveData<String> operationStatus = new MutableLiveData<>();
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();

    public BudgetViewModel(@NonNull Application application) {
        super(application);
        repository = new BudgetRepository(application);
    }

    // 获取当前月份的所有预算
    public LiveData<List<Budget>> getCurrentMonthBudgets() {
        return repository.getCurrentMonthBudgets();
    }

    // 获取指定时期的预算
    public LiveData<List<Budget>> getBudgetsByPeriod(String period, int year, int month) {
        return repository.getBudgetsByPeriod(period, year, month);
    }

    // 获取总预算
    public LiveData<Budget> getTotalBudget(String period, int year, int month) {
        return repository.getTotalBudget(period, year, month);
    }

    // 获取当前月份的总预算
    public LiveData<Budget> getCurrentMonthTotalBudget() {
        return repository.getCurrentMonthTotalBudget();
    }

    // 获取分类预算
    public LiveData<Budget> getCategoryBudget(long categoryId, String period, int year, int month) {
        return repository.getCategoryBudget(categoryId, period, year, month);
    }

    // 获取所有分类预算
    public LiveData<List<Budget>> getAllCategoryBudgets(String period, int year, int month) {
        return repository.getAllCategoryBudgets(period, year, month);
    }

    // 获取当前月份的所有分类预算
    public LiveData<List<Budget>> getCurrentMonthCategoryBudgets() {
        return repository.getCurrentMonthCategoryBudgets();
    }

    // 获取预算使用情况
    public LiveData<List<BudgetDao.BudgetUsage>> getBudgetUsage(String period, int year, int month) {
        return repository.getBudgetUsage(period, year, month);
    }

    // 获取当前月份的预算使用情况
    public LiveData<List<BudgetDao.BudgetUsage>> getCurrentMonthBudgetUsage() {
        return repository.getCurrentMonthBudgetUsage();
    }

    // 创建或更新总预算
    public void createOrUpdateTotalBudget(BigDecimal amount, String period, int year, int month, double alertThreshold) {
        repository.createTotalBudget(amount, period, year, month, alertThreshold, new BudgetRepository.BudgetOperationCallback() {
            @Override
            public void onSuccess(long result) {
                operationStatus.postValue("总预算保存成功");
            }

            @Override
            public void onError(String error) {
                errorMessage.postValue("保存总预算失败: " + error);
            }
        });
    }

    // 创建或更新分类预算
    public void createOrUpdateCategoryBudget(long categoryId, BigDecimal amount, String period, 
                                           int year, int month, double alertThreshold) {
        repository.createCategoryBudget(categoryId, amount, period, year, month, alertThreshold, 
                new BudgetRepository.BudgetOperationCallback() {
            @Override
            public void onSuccess(long result) {
                operationStatus.postValue("分类预算保存成功");
            }

            @Override
            public void onError(String error) {
                errorMessage.postValue("保存分类预算失败: " + error);
            }
        });
    }

    // 删除预算
    public void deleteBudget(Budget budget) {
        repository.deleteBudget(budget, new BudgetRepository.BudgetOperationCallback() {
            @Override
            public void onSuccess(long result) {
                operationStatus.postValue("预算删除成功");
            }

            @Override
            public void onError(String error) {
                errorMessage.postValue("删除预算失败: " + error);
            }
        });
    }

    // 插入预算
    public void insertBudget(Budget budget) {
        repository.insertOrUpdateBudget(budget, new BudgetRepository.BudgetOperationCallback() {
            @Override
            public void onSuccess(long result) {
                operationStatus.postValue("预算保存成功");
            }

            @Override
            public void onError(String error) {
                errorMessage.postValue("保存预算失败: " + error);
            }
        });
    }

    // 更新预算
    public void updateBudget(Budget budget) {
        repository.insertOrUpdateBudget(budget, new BudgetRepository.BudgetOperationCallback() {
            @Override
            public void onSuccess(long result) {
                operationStatus.postValue("预算更新成功");
            }

            @Override
            public void onError(String error) {
                errorMessage.postValue("更新预算失败: " + error);
            }
        });
    }

    // 检查预算是否存在
    public void checkBudgetExists(String budgetType, Long categoryId, Long subcategoryId, 
                                  String period, int year, int month, BudgetExistsCallback callback) {
        repository.checkBudgetExists(budgetType, categoryId, subcategoryId, period, year, month, 
                new BudgetRepository.BudgetExistsCallback() {
            @Override
            public void onResult(boolean exists) {
                if (callback != null) {
                    callback.onResult(exists);
                }
            }

            @Override
            public void onError(String error) {
                errorMessage.postValue("检查预算失败: " + error);
                if (callback != null) {
                    callback.onError(error);
                }
            }
        });
    }

    // 获取所有预算
    public LiveData<List<Budget>> getAllBudgets() {
        return repository.getAllBudgets();
    }

    // 获取操作状态
    public LiveData<String> getOperationStatus() {
        return operationStatus;
    }

    // 获取错误消息
    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    // 清除状态
    public void clearStatus() {
        operationStatus.setValue(null);
        errorMessage.setValue(null);
    }

    // 预算状态计算方法
    public static class BudgetStatus {
        public BigDecimal budgetAmount;
        public BigDecimal usedAmount;
        public BigDecimal remainingAmount;
        public double usagePercentage;
        public boolean isOverBudget;
        public boolean isNearLimit;
        public String statusText;
        public int statusColor;

        public BudgetStatus(BigDecimal budgetAmount, BigDecimal usedAmount, double alertThreshold) {
            this.budgetAmount = budgetAmount != null ? budgetAmount : BigDecimal.ZERO;
            this.usedAmount = usedAmount != null ? usedAmount : BigDecimal.ZERO;
            this.remainingAmount = this.budgetAmount.subtract(this.usedAmount);
            
            if (this.budgetAmount.compareTo(BigDecimal.ZERO) > 0) {
                this.usagePercentage = this.usedAmount.divide(this.budgetAmount, 4, BigDecimal.ROUND_HALF_UP)
                        .multiply(new BigDecimal("100")).doubleValue();
            } else {
                this.usagePercentage = 0.0;
            }
            
            this.isOverBudget = this.usedAmount.compareTo(this.budgetAmount) > 0;
            this.isNearLimit = this.usagePercentage >= (alertThreshold * 100);
            
            // 设置状态文本和颜色
            if (this.isOverBudget) {
                this.statusText = "超出预算";
                this.statusColor = android.graphics.Color.RED;
            } else if (this.isNearLimit) {
                this.statusText = "预算紧张";
                this.statusColor = android.graphics.Color.parseColor("#FF9800"); // Orange
            } else {
                this.statusText = "预算充足";
                this.statusColor = android.graphics.Color.parseColor("#4CAF50"); // Green
            }
        }
    }

    // 计算预算状态
    public static BudgetStatus calculateBudgetStatus(BigDecimal budgetAmount, BigDecimal usedAmount, double alertThreshold) {
        return new BudgetStatus(budgetAmount, usedAmount, alertThreshold);
    }

    // 获取当前时期信息
    public static class CurrentPeriod {
        public int year;
        public int month;
        public int week;
        
        public CurrentPeriod() {
            Calendar calendar = Calendar.getInstance();
            this.year = calendar.get(Calendar.YEAR);
            this.month = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH is 0-based
            this.week = calendar.get(Calendar.WEEK_OF_YEAR);
        }
    }

    public static CurrentPeriod getCurrentPeriod() {
        return new CurrentPeriod();
    }

    // 回调接口
    public interface BudgetExistsCallback {
        void onResult(boolean exists);
        void onError(String error);
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        repository.cleanup();
    }
}
