<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/YinBai"
    android:orientation="vertical"
    tools:context=".ui.category_management.CategoryManagementActivity">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/white"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <ImageView
            android:id="@+id/category_management_back"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/budget_desc_返回"
            android:src="@drawable/ic_arrow_back"
            app:tint="@color/black" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:text="收支类型管理"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/category_management_add"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="新增分类"
            android:src="@drawable/ic_add"
            app:tint="@color/black" />

    </LinearLayout>

    <!-- 类型选择标签 -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/category_management_type_tabs"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="@color/white"
        app:tabIndicatorColor="@color/HuaQing"
        app:tabSelectedTextColor="@color/HuaQing"
        app:tabTextColor="@color/grey" />

    <!-- 分类列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/category_management_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/YinBai"
        android:clipToPadding="false"
        android:padding="16dp"
        tools:listitem="@layout/item_category_management" />

    <!-- 空状态 -->
    <LinearLayout
        android:id="@+id/category_management_empty_state"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:alpha="0.5"
            android:src="@drawable/ic_category"
            app:tint="@color/grey" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="暂无分类数据"
            android:textColor="@color/grey"
            android:textSize="16sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="点击右上角 + 号添加分类"
            android:textColor="@color/grey"
            android:textSize="14sp" />

    </LinearLayout>

</LinearLayout>
