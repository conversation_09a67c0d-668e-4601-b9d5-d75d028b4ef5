package com.example.likeqianwang.Dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.example.likeqianwang.Entity.Budget;

import java.math.BigDecimal;
import java.util.List;

@Dao
public interface BudgetDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(Budget budget);

    @Update
    void update(Budget budget);

    @Delete
    void delete(Budget budget);

    @Query("SELECT * FROM budgets WHERE budgetId = :budgetId")
    Budget getBudgetById(String budgetId);

    @Query("SELECT * FROM budgets WHERE budgetId = :budgetId")
    LiveData<Budget> getBudgetByIdLive(String budgetId);

    // 获取指定时期的所有预算
    @Query("SELECT * FROM budgets WHERE budget_period = :period AND budget_year = :year AND budget_month = :month AND is_active = 1")
    LiveData<List<Budget>> getBudgetsByPeriod(String period, int year, int month);

    @Query("SELECT * FROM budgets WHERE budget_period = :period AND budget_year = :year AND budget_month = :month AND is_active = 1")
    List<Budget> getBudgetsByPeriodSync(String period, int year, int month);

    // 获取总预算
    @Query("SELECT * FROM budgets WHERE budget_type = 'TOTAL' AND budget_period = :period AND budget_year = :year AND budget_month = :month AND is_active = 1")
    LiveData<Budget> getTotalBudget(String period, int year, int month);

    @Query("SELECT * FROM budgets WHERE budget_type = 'TOTAL' AND budget_period = :period AND budget_year = :year AND budget_month = :month AND is_active = 1")
    Budget getTotalBudgetSync(String period, int year, int month);

    // 获取分类预算
    @Query("SELECT * FROM budgets WHERE budget_type = 'CATEGORY' AND category_id = :categoryId AND budget_period = :period AND budget_year = :year AND budget_month = :month AND is_active = 1")
    LiveData<Budget> getCategoryBudget(long categoryId, String period, int year, int month);

    @Query("SELECT * FROM budgets WHERE budget_type = 'CATEGORY' AND category_id = :categoryId AND budget_period = :period AND budget_year = :year AND budget_month = :month AND is_active = 1")
    Budget getCategoryBudgetSync(long categoryId, String period, int year, int month);

    // 获取所有分类预算
    @Query("SELECT * FROM budgets WHERE budget_type = 'CATEGORY' AND budget_period = :period AND budget_year = :year AND budget_month = :month AND is_active = 1")
    LiveData<List<Budget>> getAllCategoryBudgets(String period, int year, int month);

    @Query("SELECT * FROM budgets WHERE budget_type = 'CATEGORY' AND budget_period = :period AND budget_year = :year AND budget_month = :month AND is_active = 1")
    List<Budget> getAllCategoryBudgetsSync(String period, int year, int month);

    // 获取子分类预算
    @Query("SELECT * FROM budgets WHERE budget_type = 'SUBCATEGORY' AND subcategory_id = :subcategoryId AND budget_period = :period AND budget_year = :year AND budget_month = :month AND is_active = 1")
    LiveData<Budget> getSubcategoryBudget(long subcategoryId, String period, int year, int month);

    @Query("SELECT * FROM budgets WHERE budget_type = 'SUBCATEGORY' AND subcategory_id = :subcategoryId AND budget_period = :period AND budget_year = :year AND budget_month = :month AND is_active = 1")
    Budget getSubcategoryBudgetSync(long subcategoryId, String period, int year, int month);

    // 删除指定时期的所有预算
    @Query("DELETE FROM budgets WHERE budget_period = :period AND budget_year = :year AND budget_month = :month")
    void deleteBudgetsByPeriod(String period, int year, int month);

    // 获取所有预算
    @Query("SELECT * FROM budgets WHERE is_active = 1 ORDER BY budget_year DESC, budget_month DESC, budget_type ASC")
    LiveData<List<Budget>> getAllBudgets();

    // 检查是否存在指定的预算
    @Query("SELECT COUNT(*) FROM budgets WHERE budget_type = :budgetType AND " +
            "(:categoryId IS NULL OR category_id = :categoryId) AND " +
            "(:subcategoryId IS NULL OR subcategory_id = :subcategoryId) AND " +
            "budget_period = :period AND budget_year = :year AND budget_month = :month AND is_active = 1")
    int checkBudgetExists(String budgetType, Long categoryId, Long subcategoryId, String period, int year, int month);

    // 预算统计相关查询
    @Query("SELECT SUM(budget_amount) FROM budgets WHERE budget_type = 'CATEGORY' AND budget_period = :period AND budget_year = :year AND budget_month = :month AND is_active = 1")
    BigDecimal getTotalCategoryBudgetAmount(String period, int year, int month);

    // 获取预算使用情况的数据类
    class BudgetUsage {
        public String budgetId;
        public String budgetType;
        public Long categoryId;
        public Long subcategoryId;
        public BigDecimal budgetAmount;
        public BigDecimal usedAmount;
        public double usagePercentage;
        public boolean isOverBudget;
        public double alertThreshold;
    }

    // 获取预算使用情况（需要与交易表关联）
    @Query("SELECT b.budgetId, b.budget_type as budgetType, b.category_id as categoryId, " +
            "b.subcategory_id as subcategoryId, b.budget_amount as budgetAmount, " +
            "COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' AND t.include_in_budget = 1 THEN t.amount ELSE 0 END), 0) as usedAmount, " +
            "CASE WHEN b.budget_amount > 0 THEN " +
            "   COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' AND t.include_in_budget = 1 THEN t.amount ELSE 0 END), 0) * 100.0 / b.budget_amount " +
            "ELSE 0 END as usagePercentage, " +
            "CASE WHEN COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' AND t.include_in_budget = 1 THEN t.amount ELSE 0 END), 0) > b.budget_amount THEN 1 ELSE 0 END as isOverBudget, " +
            "b.alert_threshold as alertThreshold " +
            "FROM budgets b " +
            "LEFT JOIN transactions t ON " +
            "   (b.budget_type = 'TOTAL' OR " +
            "    (b.budget_type = 'CATEGORY' AND t.categoryId = b.category_id) OR " +
            "    (b.budget_type = 'SUBCATEGORY' AND t.categoryId = b.category_id)) " +
            "   AND strftime('%Y', t.transactionDate/1000, 'unixepoch') = CAST(b.budget_year AS TEXT) " +
            "   AND strftime('%m', t.transactionDate/1000, 'unixepoch') = printf('%02d', b.budget_month) " +
            "WHERE b.budget_period = :period AND b.budget_year = :year AND b.budget_month = :month AND b.is_active = 1 " +
            "GROUP BY b.budgetId")
    LiveData<List<BudgetUsage>> getBudgetUsageByPeriod(String period, int year, int month);

    @Query("SELECT b.budgetId, b.budget_type as budgetType, b.category_id as categoryId, " +
            "b.subcategory_id as subcategoryId, b.budget_amount as budgetAmount, " +
            "COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' AND t.include_in_budget = 1 THEN t.amount ELSE 0 END), 0) as usedAmount, " +
            "CASE WHEN b.budget_amount > 0 THEN " +
            "   COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' AND t.include_in_budget = 1 THEN t.amount ELSE 0 END), 0) * 100.0 / b.budget_amount " +
            "ELSE 0 END as usagePercentage, " +
            "CASE WHEN COALESCE(SUM(CASE WHEN t.type = 'EXPENSE' AND t.include_in_budget = 1 THEN t.amount ELSE 0 END), 0) > b.budget_amount THEN 1 ELSE 0 END as isOverBudget, " +
            "b.alert_threshold as alertThreshold " +
            "FROM budgets b " +
            "LEFT JOIN transactions t ON " +
            "   (b.budget_type = 'TOTAL' OR " +
            "    (b.budget_type = 'CATEGORY' AND t.categoryId = b.category_id) OR " +
            "    (b.budget_type = 'SUBCATEGORY' AND t.categoryId = b.category_id)) " +
            "   AND strftime('%Y', t.transactionDate/1000, 'unixepoch') = CAST(b.budget_year AS TEXT) " +
            "   AND strftime('%m', t.transactionDate/1000, 'unixepoch') = printf('%02d', b.budget_month) " +
            "WHERE b.budget_period = :period AND b.budget_year = :year AND b.budget_month = :month AND b.is_active = 1 " +
            "GROUP BY b.budgetId")
    List<BudgetUsage> getBudgetUsageByPeriodSync(String period, int year, int month);
}
