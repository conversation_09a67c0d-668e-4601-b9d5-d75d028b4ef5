package com.example.likeqianwang.ui.recording_account_selection;

import android.app.Dialog;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.R;
import com.example.likeqianwang.databinding.DialogRecordingAccountSelectionViewBinding;
import com.example.likeqianwang.databinding.StyleRecordingAccountSelectionItemViewBinding;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.google.android.material.shape.CornerFamily;
import com.google.android.material.shape.MaterialShapeDrawable;
import com.google.android.material.shape.ShapeAppearanceModel;

import java.util.List;
import java.util.Locale;

public class RecordingAccountSelectionDialog extends BottomSheetDialogFragment {
    private DialogRecordingAccountSelectionViewBinding accountSelectionViewBinding;
    private List<Account> accountList;
    private OnAccountSelectedListener listener;

    public interface OnAccountSelectedListener {
        void onAccountSelected(Account account);
    }

    public static RecordingAccountSelectionDialog newInstance(List<Account> accounts, OnAccountSelectedListener listener) {
        RecordingAccountSelectionDialog dialog = new RecordingAccountSelectionDialog();
        dialog.accountList = accounts;
        dialog.listener = listener;
        return dialog;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(BottomSheetDialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialog);

        // 设置对话框背景半透明
        setDialogBackgroundDim();
    }

    // 设置对话框背景半透明
    private void setDialogBackgroundDim() {
        if (getDialog() != null) {
            getDialog().getWindow().setDimAmount(0.5f);
        }
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);

        // 设置对话框显示时为展开状态
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                BottomSheetBehavior<View> behavior = BottomSheetBehavior.from(bottomSheet);

                // 确保对话框紧贴屏幕底部
                behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                behavior.setSkipCollapsed(true);

                // 设置圆角背景
                setupBottomSheetBackground(bottomSheet);
            }
        });

        return dialog;
    }

    // 设置底部表单的圆角背景
    private void setupBottomSheetBackground(View bottomSheet) {
        // 创建圆角形状
        ShapeAppearanceModel shapeAppearanceModel = new ShapeAppearanceModel.Builder()
                .setTopRightCorner(CornerFamily.ROUNDED, dpToPx(16))
                .setTopLeftCorner(CornerFamily.ROUNDED, dpToPx(16))
                .build();

        // 创建MaterialShapeDrawable
        MaterialShapeDrawable shapeDrawable = new MaterialShapeDrawable(shapeAppearanceModel);
        shapeDrawable.setFillColor(ColorStateList.valueOf(Color.WHITE));
        shapeDrawable.setElevation(dpToPx(8));
        shapeDrawable.setShadowColor(Color.LTGRAY);

        // 设置背景
        bottomSheet.setBackground(shapeDrawable);
    }

    private float dpToPx(int dp) {
        return dp * getResources().getDisplayMetrics().density;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        accountSelectionViewBinding = DialogRecordingAccountSelectionViewBinding.inflate(inflater, container, false);
        RecyclerView recyclerView = accountSelectionViewBinding.recordingPageAccountSelectionList;
        TextView backButton = accountSelectionViewBinding.recordingPageAccountSelectionBack;

        // 设置返回按钮点击事件
        backButton.setOnClickListener(v -> dismiss());

        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        AccountAdapter adapter = new AccountAdapter(accountList, account -> {
            if (listener != null) {
                listener.onAccountSelected(account);
            }
            dismiss();
        });
        recyclerView.setAdapter(adapter);

        return accountSelectionViewBinding.getRoot();
    }

    private static class AccountAdapter extends RecyclerView.Adapter<AccountAdapter.AccountViewHolder> {
        private final List<Account> accounts;
        private final OnAccountClickListener listener;

        interface OnAccountClickListener {
            void onAccountClick(Account account);
        }

        AccountAdapter(List<Account> accounts, OnAccountClickListener listener) {
            this.accounts = accounts;
            this.listener = listener;
        }

        @NonNull
        @Override
        public AccountViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            StyleRecordingAccountSelectionItemViewBinding accountSelectionItemViewBinding =
                StyleRecordingAccountSelectionItemViewBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
            return new AccountViewHolder(accountSelectionItemViewBinding);
        }

        @Override
        public void onBindViewHolder(@NonNull AccountViewHolder holder, int position) {
            Account account = accounts.get(position);

            // 设置账户图标 - 优先显示银行图标，否则显示账户类型图标
            if (account.getBankIcon() != 0) {
                holder.accountIcon.setImageResource(account.getBankIcon());
            } else {
                holder.accountIcon.setImageResource(account.getAccountTypeIcon());
            }

            // 设置账户名称
            holder.accountName.setText(account.getAccountName());

            // 设置账户类型
            holder.accountType.setText(account.getAccountTypeName());

            // 设置账户余额
            String balanceText = String.format(Locale.CHINA, "¥%,.2f", account.getAccountBalance());
            holder.accountBalance.setText(balanceText);

            // 设置点击事件
            holder.itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onAccountClick(account);
                }
            });
        }

        @Override
        public int getItemCount() {
            return accounts.size();
        }

        static class AccountViewHolder extends RecyclerView.ViewHolder {
            android.widget.ImageView accountIcon;
            TextView accountName;
            TextView accountType;
            TextView accountBalance;

            AccountViewHolder(StyleRecordingAccountSelectionItemViewBinding accountSelectionItemViewBinding) {
                super(accountSelectionItemViewBinding.getRoot());
                accountIcon = accountSelectionItemViewBinding.ivRecordingPageAccountItemIcon;
                accountName = accountSelectionItemViewBinding.tvRecordingPageAccountName;
                accountType = accountSelectionItemViewBinding.tvRecordingPageAccountType;
                accountBalance = accountSelectionItemViewBinding.tvRecordingPageAccountBalance;
            }
        }
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        // 设置对话框的样式，使其占满屏幕宽度
        if (getDialog() != null && getDialog().getWindow() != null) {
            getDialog().getWindow().setLayout(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.WRAP_CONTENT
            );
        }

        // 设置背景半透明
        View parent = (View) view.getParent();
        parent.setBackgroundColor(Color.TRANSPARENT);
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        accountSelectionViewBinding = null;
    }
}
