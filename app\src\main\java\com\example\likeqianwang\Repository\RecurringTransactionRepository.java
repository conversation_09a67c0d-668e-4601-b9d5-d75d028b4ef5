package com.example.likeqianwang.Repository;

import android.util.Log;

import androidx.lifecycle.LiveData;

import com.example.likeqianwang.Dao.RecurringTransactionDao;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.RecurringTransaction;
import com.example.likeqianwang.Entity.Transactions;

import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class RecurringTransactionRepository {
    private static final String TAG = "RecurringTransactionRepo";
    
    private final RecurringTransactionDao recurringTransactionDao;
    private final TransactionRepository transactionRepository;
    private final ExecutorService executor;

    public RecurringTransactionRepository(AppDatabase database) {
        this.recurringTransactionDao = database.recurringTransactionDao();
        this.transactionRepository = new TransactionRepository(database);
        this.executor = Executors.newFixedThreadPool(4);
    }

    /**
     * 插入周期记账记录
     */
    public void insertRecurringTransaction(RecurringTransaction recurringTransaction, RecurringTransactionOperationCallback callback) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "Inserting recurring transaction: " + recurringTransaction.getName());
                
                // 计算下次执行时间
                calculateNextExecutionDate(recurringTransaction);
                
                long id = recurringTransactionDao.insert(recurringTransaction);
                Log.d(TAG, "Recurring transaction inserted successfully with ID: " + id);
                
                if (callback != null) {
                    callback.onSuccess();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error inserting recurring transaction", e);
                if (callback != null) {
                    callback.onError(e.getMessage());
                }
            }
        });
    }

    /**
     * 更新周期记账记录
     */
    public void updateRecurringTransaction(RecurringTransaction recurringTransaction, RecurringTransactionOperationCallback callback) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "Updating recurring transaction: " + recurringTransaction.getName());
                
                recurringTransaction.setUpdateTime(new Date());
                recurringTransactionDao.update(recurringTransaction);
                
                Log.d(TAG, "Recurring transaction updated successfully");
                
                if (callback != null) {
                    callback.onSuccess();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error updating recurring transaction", e);
                if (callback != null) {
                    callback.onError(e.getMessage());
                }
            }
        });
    }

    /**
     * 删除周期记账记录
     */
    public void deleteRecurringTransaction(RecurringTransaction recurringTransaction, RecurringTransactionOperationCallback callback) {
        executor.execute(() -> {
            try {
                Log.d(TAG, "Deleting recurring transaction: " + recurringTransaction.getName());
                
                recurringTransactionDao.delete(recurringTransaction);
                
                Log.d(TAG, "Recurring transaction deleted successfully");
                
                if (callback != null) {
                    callback.onSuccess();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error deleting recurring transaction", e);
                if (callback != null) {
                    callback.onError(e.getMessage());
                }
            }
        });
    }

    /**
     * 获取所有周期记账记录
     */
    public LiveData<List<RecurringTransaction>> getAllRecurringTransactions() {
        return recurringTransactionDao.getAllRecurringTransactions();
    }

    /**
     * 获取启用的周期记账记录
     */
    public LiveData<List<RecurringTransaction>> getActiveRecurringTransactions() {
        return recurringTransactionDao.getActiveRecurringTransactions();
    }

    /**
     * 根据ID获取周期记账记录
     */
    public RecurringTransaction getRecurringTransactionById(String recurringId) {
        return recurringTransactionDao.getRecurringTransactionByIdSync(recurringId);
    }

    /**
     * 执行到期的周期记账
     */
    public void executeRecurringTransactions(RecurringTransactionExecutionCallback callback) {
        executor.execute(() -> {
            try {
                Date currentTime = new Date();
                List<RecurringTransaction> dueTransactions = recurringTransactionDao.getRecurringTransactionsDue(currentTime);
                
                Log.d(TAG, "Found " + dueTransactions.size() + " due recurring transactions");
                
                int successCount = 0;
                int failCount = 0;
                
                for (RecurringTransaction recurringTransaction : dueTransactions) {
                    try {
                        // 检查是否已过结束时间
                        if (recurringTransaction.getEndDate() != null && 
                            currentTime.after(recurringTransaction.getEndDate())) {
                            // 禁用已过期的周期记账
                            recurringTransactionDao.updateActiveStatus(
                                recurringTransaction.getRecurringId(), 
                                false, 
                                currentTime
                            );
                            Log.d(TAG, "Disabled expired recurring transaction: " + recurringTransaction.getName());
                            continue;
                        }
                        
                        // 创建普通交易记录
                        Transactions transaction = createTransactionFromRecurring(recurringTransaction);
                        long transactionId = transactionRepository.addTransaction(transaction, null);
                        
                        // 更新周期记账的下次执行时间
                        Date nextExecutionDate = calculateNextExecutionDate(recurringTransaction);
                        recurringTransactionDao.updateNextExecutionDate(
                            recurringTransaction.getRecurringId(),
                            nextExecutionDate,
                            currentTime,
                            currentTime
                        );
                        
                        successCount++;
                        Log.d(TAG, "Successfully executed recurring transaction: " + recurringTransaction.getName() + 
                              ", created transaction ID: " + transactionId);
                        
                    } catch (Exception e) {
                        failCount++;
                        Log.e(TAG, "Failed to execute recurring transaction: " + recurringTransaction.getName(), e);
                    }
                }
                
                Log.d(TAG, "Recurring transaction execution completed. Success: " + successCount + ", Failed: " + failCount);
                
                if (callback != null) {
                    callback.onExecutionCompleted(successCount, failCount);
                }
                
            } catch (Exception e) {
                Log.e(TAG, "Error executing recurring transactions", e);
                if (callback != null) {
                    callback.onExecutionCompleted(0, -1);
                }
            }
        });
    }

    /**
     * 从周期记账创建普通交易记录
     */
    private Transactions createTransactionFromRecurring(RecurringTransaction recurringTransaction) {
        Transactions transaction = new Transactions();
        transaction.setType(recurringTransaction.getType());
        transaction.setTransactionDate(new Date());
        transaction.setCategoryId(recurringTransaction.getCategoryId());
        transaction.setAmount(recurringTransaction.getAmount());
        transaction.setCurrencySymbol(recurringTransaction.getCurrencySymbol());
        transaction.setFromAccountId(recurringTransaction.getFromAccountId());
        transaction.setToAccountId(recurringTransaction.getToAccountId());
        transaction.setIncludeInStats(recurringTransaction.isIncludeInStats());
        transaction.setIncludeInBudget(recurringTransaction.isIncludeInBudget());
        transaction.setRemark("[周期记账] " + recurringTransaction.getName() + 
                             (recurringTransaction.getRemark() != null ? " - " + recurringTransaction.getRemark() : ""));
        
        return transaction;
    }

    /**
     * 计算下次执行时间
     */
    private Date calculateNextExecutionDate(RecurringTransaction recurringTransaction) {
        Calendar calendar = Calendar.getInstance();
        Date baseDate = recurringTransaction.getNextExecutionDate() != null ? 
                       recurringTransaction.getNextExecutionDate() : 
                       recurringTransaction.getStartDate();
        
        if (baseDate != null) {
            calendar.setTime(baseDate);
        }
        
        String repeatType = recurringTransaction.getRepeatType();
        int interval = recurringTransaction.getRepeatInterval();
        
        switch (repeatType) {
            case "DAILY":
                calendar.add(Calendar.DAY_OF_MONTH, interval);
                break;
            case "WEEKLY":
                calendar.add(Calendar.WEEK_OF_YEAR, interval);
                break;
            case "MONTHLY":
                calendar.add(Calendar.MONTH, interval);
                break;
            case "YEARLY":
                calendar.add(Calendar.YEAR, interval);
                break;
            default:
                Log.w(TAG, "Unknown repeat type: " + repeatType);
                calendar.add(Calendar.DAY_OF_MONTH, 1); // 默认每天
                break;
        }
        
        Date nextDate = calendar.getTime();
        recurringTransaction.setNextExecutionDate(nextDate);
        
        return nextDate;
    }

    /**
     * 启用/禁用周期记账
     */
    public void updateActiveStatus(String recurringId, boolean isActive, RecurringTransactionOperationCallback callback) {
        executor.execute(() -> {
            try {
                recurringTransactionDao.updateActiveStatus(recurringId, isActive, new Date());
                Log.d(TAG, "Updated active status for recurring transaction: " + recurringId + " to " + isActive);
                
                if (callback != null) {
                    callback.onSuccess();
                }
            } catch (Exception e) {
                Log.e(TAG, "Error updating active status", e);
                if (callback != null) {
                    callback.onError(e.getMessage());
                }
            }
        });
    }

    // 回调接口
    public interface RecurringTransactionOperationCallback {
        void onSuccess();
        void onError(String error);
    }

    public interface RecurringTransactionExecutionCallback {
        void onExecutionCompleted(int successCount, int failCount);
    }
}
