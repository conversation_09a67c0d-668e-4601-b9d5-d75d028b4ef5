package com.example.likeqianwang;

import android.os.Bundle;
import android.view.View;
import android.view.WindowInsetsController;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.EdgeToEdge;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Repository.AccountRepository;
import com.example.likeqianwang.databinding.ActivityFillNewAccountInfoBinding;
import com.example.likeqianwang.ui.fill_new_account_info_credit_date_picker.CreditDatePickerDialog;
import com.example.likeqianwang.ui.wallets_bank_seleciton.WalletsBankSelectionDialog;
import com.example.likeqianwang.ui.wallets_add_new_account.WalletsAddNewAccountDialog;
import com.google.android.material.imageview.ShapeableImageView;

public class FillNewAccountInfoActivity extends AppCompatActivity implements CreditDatePickerDialog.DateSelectedListener{
    private ActivityFillNewAccountInfoBinding fillNewAccountInfoBinding;
    private String accountTypeId;
    private String accountTypeName;
    private int accountTypeIcon;
    private String accountTypeCategoryId;
    private int accountTypeDebitCredit;
    private String bankId;
    private String bankName;
    private int bankIcon;
    private String combinedAccountName;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        EdgeToEdge.enable(this);
        fillNewAccountInfoBinding = ActivityFillNewAccountInfoBinding.inflate(getLayoutInflater());
        setContentView(fillNewAccountInfoBinding.getRoot());

        // 设置状态栏颜色
        getWindow().setStatusBarColor(ContextCompat.getColor(this, R.color.YinBai));

        // 如果是浅色背景，需要设置状态栏文字为深色
        WindowInsetsController insetsController = getWindow().getInsetsController();
        if (insetsController != null) {
            insetsController.setSystemBarsAppearance(
                    WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS,
                    WindowInsetsController.APPEARANCE_LIGHT_STATUS_BARS);
        }

        // 使用 Insets API 处理系统窗口边距
        getWindow().setDecorFitsSystemWindows(false);

        ViewCompat.setOnApplyWindowInsetsListener(fillNewAccountInfoBinding.getRoot(), (v, windowInsets) -> {
            Insets insets = windowInsets.getInsets(WindowInsetsCompat.Type.systemBars());

            // 应用顶部 padding (状态栏高度 + 原始 padding)
            v.setPadding(
                    v.getPaddingLeft(),
                    insets.top + 15,  // 15dp 是原始 padding
                    v.getPaddingRight(),
                    v.getPaddingBottom()
            );

            return WindowInsetsCompat.CONSUMED;
        });

        // 获取传递的数据
        accountTypeId = getIntent().getStringExtra("ACCOUNT_TYPE_ID");
        accountTypeName = getIntent().getStringExtra("ACCOUNT_TYPE_NAME");
        accountTypeIcon = getIntent().getIntExtra("ACCOUNT_TYPE_ICON", 0);
        accountTypeCategoryId = getIntent().getStringExtra("ACCOUNT_TYPE_CATEGORY_ID");
        accountTypeDebitCredit = getIntent().getIntExtra("ACCOUNT_TYPE_DEBIT_CREDIT", 1);

        // 获取银行信息（如果有）
        bankId = getIntent().getStringExtra("BANK_ID");
        bankName = getIntent().getStringExtra("BANK_NAME");
        bankIcon = getIntent().getIntExtra("BANK_ICON", 0);
        combinedAccountName = getIntent().getStringExtra("COMBINED_ACCOUNT_NAME");

        // 设置账户类型信息
        updateAccountTypeUI();

        // 设置返回按钮点击事件
        fillNewAccountInfoBinding.tvWalletsBack.setOnClickListener(v -> finish());

        // 设置保存按钮点击事件
        fillNewAccountInfoBinding.tvWalletsSave.setOnClickListener(v -> {
            // 保存账户信息的逻辑
            saveAccountInfo();
        });

        // 设置账户类型区域点击事件
        fillNewAccountInfoBinding.walletsAccountType.setOnClickListener(v -> showAccountTypeDialog());

        // 根据账户类型显示或隐藏信用卡相关信息
        updateUIBasedOnAccountType(accountTypeDebitCredit);

        // 设置账单日点击事件
        fillNewAccountInfoBinding.etWalletsAccountInputStatementDate.setOnClickListener(v -> {
            showDatePickerDialog(CreditDatePickerDialog.TYPE_STATEMENT_DATE);
        });

        // 设置还款日点击事件
        fillNewAccountInfoBinding.etWalletsAccountInputDueDate.setOnClickListener(v -> {
            showDatePickerDialog(CreditDatePickerDialog.TYPE_DUE_DATE);
        });
    }

    // 更新账户类型UI
    private void updateAccountTypeUI() {
        // 设置账户类型信息
        ShapeableImageView iconView = fillNewAccountInfoBinding.ivWalletsAccountIcon;
        TextView nameView = fillNewAccountInfoBinding.tvWalletsAccountName;

        // 优先使用银行图标，如果没有则使用账户类型图标
        if (bankIcon != 0) {
            iconView.setImageResource(bankIcon);
        } else if (accountTypeIcon != 0) {
            iconView.setImageResource(accountTypeIcon);
        }

        // 设置账户名称（优先使用组合名称）
        if (combinedAccountName != null && !combinedAccountName.isEmpty()) {
            nameView.setText(combinedAccountName);
        } else if (accountTypeName != null) {
            nameView.setText(accountTypeName);
        }
    }

    // 根据账户类型更新UI
    private void updateUIBasedOnAccountType(int accountTypeDebitCredit) {
        if (accountTypeDebitCredit == 0) {
            // 如果是信用卡，显示信用卡信息区域
            fillNewAccountInfoBinding.walletsAccountCreditInfo.setVisibility(View.VISIBLE);
            fillNewAccountInfoBinding.walletsAccountInputBalanceArea.setVisibility(View.GONE);
        } else {
            // 否则隐藏信用卡信息区域
            fillNewAccountInfoBinding.walletsAccountCreditInfo.setVisibility(View.GONE);
            fillNewAccountInfoBinding.walletsAccountInputBalanceArea.setVisibility(View.VISIBLE);
        }
    }

    // 显示账户类型选择对话框
    private void showAccountTypeDialog() {
        WalletsAddNewAccountDialog dialog = WalletsAddNewAccountDialog.newInstance();

        // 设置不启动新Activity
        dialog.setShouldStartNewActivity(false);

        // 设置选择监听器
        dialog.setOnAccountTypeSelectedListener(accountType -> {
            // 检查是否是银行卡类型
            if (accountType.getParentAccountTypeCategoryId().equals("cat_bank")) {
                // 如果是银行卡类型，显示银行选择对话框
                showBankSelectionDialog(
                        accountType.getId(),
                        accountType.getName(),
                        accountType.getParentAccountTypeCategoryId(),
                        accountType.getDebitOrCredit()
                );
            } else {
                // 非银行卡类型，直接更新UI
                updateAccountTypeInfo(
                        accountType.getId(),
                        accountType.getName(),
                        accountType.getIconResId(),
                        accountType.getParentAccountTypeCategoryId(),
                        accountType.getDebitOrCredit(),
                        null, null, 0
                );
            }
        });

        dialog.show(getSupportFragmentManager(), "account_type_dialog");
    }

    public void showBankSelectionDialog(String typeId, String typeName, String typeCategoryId, int typeDebitCredit) {
        WalletsBankSelectionDialog bankDialog = WalletsBankSelectionDialog.newInstance();

        // 设置不启动新Activity
        bankDialog.setShouldStartNewActivity(false);

        // 传递账户类型信息到银行选择对话框
        Bundle args = new Bundle();
        args.putString("ACCOUNT_TYPE_ID", typeId);
        args.putString("ACCOUNT_TYPE_NAME", typeName);
        args.putString("ACCOUNT_TYPE_CATEGORY_ID", typeCategoryId);
        args.putInt("ACCOUNT_TYPE_DEBIT_CREDIT", typeDebitCredit);
        bankDialog.setArguments(args);

        // 设置银行选择监听器
        bankDialog.setOnBankSelectedListener(bank -> {
            // 更新账户类型信息，包含银行信息
            updateAccountTypeInfo(
                    typeId,
                    typeName,
                    0, // 不使用账户类型图标
                    typeCategoryId,
                    typeDebitCredit,
                    bank.getId(),
                    bank.getName(),
                    bank.getIconResId()
            );
        });

        bankDialog.show(getSupportFragmentManager(), "bank_selection_dialog");
    }

    /**
     * 更新账户类型信息
     */
    public void updateAccountTypeInfo(String typeId, String typeName, int typeIcon,
                                       String typeCategoryId, int typeDebitCredit,
                                       String bankId, String bankName, int bankIcon) {
        // 更新UI显示
        ShapeableImageView iconView = fillNewAccountInfoBinding.ivWalletsAccountIcon;
        TextView nameView = fillNewAccountInfoBinding.tvWalletsAccountName;

        // 优先使用银行图标，如果没有则使用账户类型图标
        if (bankIcon != 0) {
            iconView.setImageResource(bankIcon);
        } else if (typeIcon != 0) {
            iconView.setImageResource(typeIcon);
        }

        // 设置账户名称（如果有银行信息则组合名称）
        if (bankName != null && !bankName.isEmpty()) {
            String combinedName = bankName + (typeDebitCredit == 0 ? "信用卡" : "储蓄卡");
            nameView.setText(combinedName);
        } else {
            nameView.setText(typeName);
        }

        // 根据账户类型更新UI
        updateUIBasedOnAccountType(typeDebitCredit);
    }

    // 显示日期选择对话框
    private void showDatePickerDialog(String type) {
        CreditDatePickerDialog dialog = CreditDatePickerDialog.newInstance(1, type);
        dialog.setDateSelectedListener(this);
        dialog.show(getSupportFragmentManager(), "date_picker_dialog");
    }

    // 实现 DateSelectedListener 接口方法
    @Override
    public void onDateSelected(int day, String type) {
        String formattedDate = "每月" + day + "日";

        if (CreditDatePickerDialog.TYPE_STATEMENT_DATE.equals(type)) {
            // 更新账单日显示
            fillNewAccountInfoBinding.etWalletsAccountInputStatementDate.setText(formattedDate);
        } else if (CreditDatePickerDialog.TYPE_DUE_DATE.equals(type)) {
            // 更新还款日显示
            fillNewAccountInfoBinding.etWalletsAccountInputDueDate.setText(formattedDate);
        }
    }

    private void saveAccountInfo() {
        // 获取用户输入的信息
        String accountName = fillNewAccountInfoBinding.etWalletsAccountInputName.getText().toString();

        // 验证必填字段
        if (accountName.isEmpty()) {
            Toast.makeText(this, "请输入账户名称", Toast.LENGTH_SHORT).show();
            return;
        }

        // 创建账户对象
        Account account = new Account();

        // 设置基本信息
        account.setAccountName(accountName);
        account.setAccountTypeId(accountTypeId);
        account.setAccountTypeName(accountTypeName);
        account.setAccountTypeIcon(accountTypeIcon);
        account.setAccountTypeCategoryId(accountTypeCategoryId);
        account.setAccountTypeDebitCredit(accountTypeDebitCredit);

        // 设置银行信息（如果有）
        if (bankId != null && !bankId.isEmpty()) {
            account.setBankId(bankId);
            account.setBankName(bankName);
            account.setBankIcon(bankIcon);
        }

        // 设置备注
        String accountRemark = fillNewAccountInfoBinding.etWalletsAccountInputRemark.getText().toString();
        account.setAccountRemark(accountRemark);

        // 设置是否计入总资产
        account.setIncludeInAsset(fillNewAccountInfoBinding.switchWalletsAccountIncludeInAsset.isChecked());

        // 设置币种
        String currencySymbol = fillNewAccountInfoBinding.tvWalletsAccountCurrency.getText().toString();
        account.setCurrencySymbol(currencySymbol);

        // 根据账户类型设置不同的信息
        if (accountTypeDebitCredit == 1) {
            // 借记卡/储蓄卡
            String balanceStr = fillNewAccountInfoBinding.etWalletsAccountInputBalance.getText().toString();
            double balance = 0.00;
            if (!balanceStr.isEmpty()) {
                try {
                    balance = Double.parseDouble(balanceStr);
                } catch (NumberFormatException e) {
                    Toast.makeText(this, "请输入有效的余额", Toast.LENGTH_SHORT).show();
                    return;
                }
            }
            account.setAccountBalance(balance);
        } else {
            // 信用卡
            String totalCreditStr = fillNewAccountInfoBinding.etWalletsAccountInputTotalCredit.getText().toString();
            String currentCreditStr = fillNewAccountInfoBinding.etWalletsAccountInputCurrentCredit.getText().toString();

            double totalCredit = 0.00;
            double currentCredit = 0.00;

            if (!totalCreditStr.isEmpty()) {
                try {
                    totalCredit = Double.parseDouble(totalCreditStr);
                } catch (NumberFormatException e) {
                    Toast.makeText(this, "请输入有效的总额度", Toast.LENGTH_SHORT).show();
                    return;
                }
            }

            if (!currentCreditStr.isEmpty()) {
                try {
                    currentCredit = Double.parseDouble(currentCreditStr);
                } catch (NumberFormatException e) {
                    Toast.makeText(this, "请输入有效的当前欠款", Toast.LENGTH_SHORT).show();
                    return;
                }
            }

            account.setTotalCredit(totalCredit);
            account.setAccountBalance(currentCredit);

            // 设置账单日和还款日
            String statementDateStr = fillNewAccountInfoBinding.etWalletsAccountInputStatementDate.getText().toString();
            String dueDateStr = fillNewAccountInfoBinding.etWalletsAccountInputDueDate.getText().toString();

            // 这里需要解析日期，假设格式为"每月X日"
            if (!statementDateStr.equals("请点击设置")) {
                try {
                    String dayStr = statementDateStr.replace("每月", "").replace("日", "");
                    int day = Integer.parseInt(dayStr);
                    account.setStatementDate(day);
                } catch (Exception e) {
                    // 使用默认值或提示用户
                }
            }

            if (!dueDateStr.equals("请点击设置")) {
                try {
                    String dayStr = dueDateStr.replace("每月", "").replace("日", "");
                    int day = Integer.parseInt(dayStr);
                    account.setDueDate(day);
                } catch (Exception e) {
                    // 使用默认值或提示用户
                }
            }

            // 设置出账日账单是否计入当期
            account.setDueDateInCurrentPeriod(
                    fillNewAccountInfoBinding.switchWalletsAccountDueDateInCurrentPeriod.isChecked());
        }

        // 保存账户信息
        AccountRepository repository = new AccountRepository(getApplication());
        repository.insert(account);

        Toast.makeText(this, "账户保存成功", Toast.LENGTH_SHORT).show();

        // 设置结果为成功，这样 WalletsFragment 可以知道需要刷新数据
        setResult(RESULT_OK);

        // 保存成功后返回
        finish();
    }
}