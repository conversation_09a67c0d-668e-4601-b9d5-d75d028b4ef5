package com.example.likeqianwang.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.Switch;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.RecurringTransaction;
import com.example.likeqianwang.R;
import com.example.likeqianwang.Utils.CurrencyFormatter;
import com.example.likeqianwang.Utils.DateUtils;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Locale;

public class RecurringTransactionAdapter extends RecyclerView.Adapter<RecurringTransactionAdapter.RecurringTransactionViewHolder> {

    private final Context context;
    private final List<RecurringTransaction> recurringTransactions;
    private OnRecurringTransactionItemClickListener onItemClickListener;

    public interface OnRecurringTransactionItemClickListener {
        void onRecurringTransactionItemClick(RecurringTransaction recurringTransaction);
        void onRecurringTransactionToggleActive(RecurringTransaction recurringTransaction);
        void onRecurringTransactionDelete(RecurringTransaction recurringTransaction);
    }

    public RecurringTransactionAdapter(Context context, List<RecurringTransaction> recurringTransactions) {
        this.context = context;
        this.recurringTransactions = recurringTransactions;
    }

    public void setOnItemClickListener(OnRecurringTransactionItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    @NonNull
    @Override
    public RecurringTransactionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_recurring_transaction, parent, false);
        return new RecurringTransactionViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RecurringTransactionViewHolder holder, int position) {
        RecurringTransaction recurringTransaction = recurringTransactions.get(position);
        holder.bind(recurringTransaction);
    }

    @Override
    public int getItemCount() {
        return recurringTransactions.size();
    }

    class RecurringTransactionViewHolder extends RecyclerView.ViewHolder {
        private final ImageView typeIcon;
        private final TextView nameText;
        private final TextView amountText;
        private final TextView typeText;
        private final TextView nextExecutionText;
        private final TextView repeatInfoText;
        private final Switch activeSwitch;
        private final ImageView deleteButton;

        public RecurringTransactionViewHolder(@NonNull View itemView) {
            super(itemView);
            typeIcon = itemView.findViewById(R.id.recurring_transaction_type_icon);
            nameText = itemView.findViewById(R.id.recurring_transaction_name);
            amountText = itemView.findViewById(R.id.recurring_transaction_amount);
            typeText = itemView.findViewById(R.id.recurring_transaction_type);
            nextExecutionText = itemView.findViewById(R.id.recurring_transaction_next_execution);
            repeatInfoText = itemView.findViewById(R.id.recurring_transaction_repeat_info);
            activeSwitch = itemView.findViewById(R.id.recurring_transaction_active_switch);
            deleteButton = itemView.findViewById(R.id.recurring_transaction_delete);
        }

        public void bind(RecurringTransaction recurringTransaction) {
            // 设置名称
            nameText.setText(recurringTransaction.getName());

            // 设置金额
            String formattedAmount = CurrencyFormatter.format(recurringTransaction.getAmount().doubleValue());
            amountText.setText(formattedAmount);

            // 设置类型和图标
            String type = recurringTransaction.getType();
            switch (type) {
                case "INCOME":
                    typeIcon.setImageResource(R.drawable.ic_income);
                    typeText.setText("收入");
                    amountText.setTextColor(context.getResources().getColor(android.R.color.holo_green_dark));
                    break;
                case "EXPENSE":
                    typeIcon.setImageResource(R.drawable.ic_expense);
                    typeText.setText("支出");
                    amountText.setTextColor(context.getResources().getColor(android.R.color.holo_red_dark));
                    break;
                case "TRANSFER":
                    typeIcon.setImageResource(R.drawable.ic_transfer);
                    typeText.setText("转账");
                    amountText.setTextColor(context.getResources().getColor(android.R.color.holo_blue_dark));
                    break;
                default:
                    typeIcon.setImageResource(R.drawable.ic_category);
                    typeText.setText("未知");
                    amountText.setTextColor(context.getResources().getColor(android.R.color.black));
                    break;
            }

            // 设置下次执行时间
            if (recurringTransaction.getNextExecutionDate() != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
                String nextExecution = dateFormat.format(recurringTransaction.getNextExecutionDate());
                nextExecutionText.setText("下次执行: " + nextExecution);
            } else {
                nextExecutionText.setText("下次执行: 未设置");
            }

            // 设置重复信息
            String repeatInfo = getRepeatInfoText(recurringTransaction);
            repeatInfoText.setText(repeatInfo);

            // 设置启用状态
            activeSwitch.setChecked(recurringTransaction.isActive());
            
            // 根据启用状态调整透明度
            float alpha = recurringTransaction.isActive() ? 1.0f : 0.5f;
            itemView.setAlpha(alpha);

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onItemClickListener != null) {
                    onItemClickListener.onRecurringTransactionItemClick(recurringTransaction);
                }
            });

            activeSwitch.setOnCheckedChangeListener((buttonView, isChecked) -> {
                if (onItemClickListener != null) {
                    onItemClickListener.onRecurringTransactionToggleActive(recurringTransaction);
                }
            });

            deleteButton.setOnClickListener(v -> {
                if (onItemClickListener != null) {
                    onItemClickListener.onRecurringTransactionDelete(recurringTransaction);
                }
            });
        }

        private String getRepeatInfoText(RecurringTransaction recurringTransaction) {
            String repeatType = recurringTransaction.getRepeatType();
            int interval = recurringTransaction.getRepeatInterval();
            
            StringBuilder sb = new StringBuilder();
            
            if (interval == 1) {
                switch (repeatType) {
                    case "DAILY":
                        sb.append("每日");
                        break;
                    case "WEEKLY":
                        sb.append("每周");
                        break;
                    case "MONTHLY":
                        sb.append("每月");
                        break;
                    case "YEARLY":
                        sb.append("每年");
                        break;
                    default:
                        sb.append("未知周期");
                        break;
                }
            } else {
                switch (repeatType) {
                    case "DAILY":
                        sb.append("每").append(interval).append("天");
                        break;
                    case "WEEKLY":
                        sb.append("每").append(interval).append("周");
                        break;
                    case "MONTHLY":
                        sb.append("每").append(interval).append("月");
                        break;
                    case "YEARLY":
                        sb.append("每").append(interval).append("年");
                        break;
                    default:
                        sb.append("未知周期");
                        break;
                }
            }
            
            // 添加执行次数信息
            sb.append(" (已执行").append(recurringTransaction.getExecutionCount()).append("次)");
            
            return sb.toString();
        }
    }
}
