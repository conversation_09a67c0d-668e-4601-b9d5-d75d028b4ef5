<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:background="?attr/selectableItemBackground"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="16dp">

    <!-- 账本图标 -->
    <ImageView
        android:id="@+id/notebook_item_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="16dp"
        android:contentDescription="账本图标"
        app:tint="@color/HuaQing"
        tools:src="@drawable/ic_book" />

    <!-- 账本信息 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/notebook_item_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="默认账本" />

            <!-- 默认标签 -->
            <TextView
                android:id="@+id/notebook_item_default_badge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/widget_tag_item_bg"
                android:backgroundTint="@color/HuaQing"
                android:paddingHorizontal="8dp"
                android:paddingVertical="2dp"
                android:text="默认"
                android:textColor="@color/white"
                android:textSize="10sp"
                android:visibility="gone"
                tools:visibility="visible" />

        </LinearLayout>

        <TextView
            android:id="@+id/notebook_item_description"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:textColor="@color/grey"
            android:textSize="12sp"
            android:visibility="gone"
            tools:text="这是账本描述"
            tools:visibility="visible" />

    </LinearLayout>

    <!-- 选中状态指示器 -->
    <ImageView
        android:id="@+id/notebook_item_selected_indicator"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:layout_marginStart="8dp"
        android:contentDescription="当前选中"
        android:src="@drawable/ic_check"
        android:visibility="gone"
        app:tint="@color/HuaQing"
        tools:visibility="visible" />

</LinearLayout>
