package com.example.likeqianwang.adapters;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.PopupMenu;
import android.widget.ProgressBar;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.Budget;
import com.example.likeqianwang.R;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.List;
import java.util.Locale;

public class BudgetManagementAdapter extends RecyclerView.Adapter<BudgetManagementAdapter.BudgetViewHolder> {

    private final Context context;
    private final List<Budget> budgetList;
    private OnBudgetItemClickListener listener;

    public interface OnBudgetItemClickListener {
        void onBudgetItemClick(Budget budget);
        void onBudgetItemEdit(Budget budget);
        void onBudgetItemDelete(Budget budget);
    }

    public BudgetManagementAdapter(Context context, List<Budget> budgetList) {
        this.context = context;
        this.budgetList = budgetList;
    }

    public void setOnBudgetItemClickListener(OnBudgetItemClickListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public BudgetViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_budget_management, parent, false);
        return new BudgetViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull BudgetViewHolder holder, int position) {
        Budget budget = budgetList.get(position);
        holder.bind(budget);
    }

    @Override
    public int getItemCount() {
        return budgetList != null ? budgetList.size() : 0;
    }

    class BudgetViewHolder extends RecyclerView.ViewHolder {
        private final ImageView typeIcon;
        private final TextView nameTextView;
        private final TextView typeTextView;
        private final TextView amountTextView;
        private final TextView statusTextView;
        private final ImageView moreButton;
        private final TextView usedAmountTextView;
        private final TextView remainingAmountTextView;
        private final ProgressBar progressBar;
        private final TextView remarkTextView;

        public BudgetViewHolder(@NonNull View itemView) {
            super(itemView);
            typeIcon = itemView.findViewById(R.id.budget_item_type_icon);
            nameTextView = itemView.findViewById(R.id.budget_item_name);
            typeTextView = itemView.findViewById(R.id.budget_item_type);
            amountTextView = itemView.findViewById(R.id.budget_item_amount);
            statusTextView = itemView.findViewById(R.id.budget_item_status);
            moreButton = itemView.findViewById(R.id.budget_item_more);
            usedAmountTextView = itemView.findViewById(R.id.budget_item_used_amount);
            remainingAmountTextView = itemView.findViewById(R.id.budget_item_remaining_amount);
            progressBar = itemView.findViewById(R.id.budget_item_progress);
            remarkTextView = itemView.findViewById(R.id.budget_item_remark);
        }

        public void bind(Budget budget) {
            // 设置预算名称和类型
            setBudgetNameAndType(budget);
            
            // 设置预算金额
            setBudgetAmount(budget);
            
            // 设置预算使用情况（这里暂时使用模拟数据，实际应该从数据库查询）
            setBudgetUsage(budget);
            
            // 设置备注
            setBudgetRemark(budget);
            
            // 设置点击事件
            setupClickListeners(budget);
        }

        private void setBudgetNameAndType(Budget budget) {
            String budgetName;
            String budgetType;
            int iconRes;

            switch (budget.getBudgetType()) {
                case "TOTAL":
                    budgetName = "总预算";
                    iconRes = R.drawable.ic_budget;
                    break;
                case "CATEGORY":
                    budgetName = "分类预算"; // 实际应该显示分类名称
                    iconRes = R.drawable.ic_category;
                    break;
                case "SUBCATEGORY":
                    budgetName = "子分类预算"; // 实际应该显示子分类名称
                    iconRes = R.drawable.ic_category;
                    break;
                default:
                    budgetName = "未知预算";
                    iconRes = R.drawable.ic_budget;
                    break;
            }

            // 设置预算周期类型
            switch (budget.getBudgetPeriod()) {
                case "MONTHLY":
                    budgetType = "月度预算";
                    break;
                case "WEEKLY":
                    budgetType = "周度预算";
                    break;
                case "YEARLY":
                    budgetType = "年度预算";
                    break;
                default:
                    budgetType = "未知周期";
                    break;
            }

            typeIcon.setImageResource(iconRes);
            nameTextView.setText(budgetName);
            typeTextView.setText(budgetType);
        }

        private void setBudgetAmount(Budget budget) {
            NumberFormat currencyFormat = NumberFormat.getCurrencyInstance(Locale.CHINA);
            String amountText = currencyFormat.format(budget.getBudgetAmount());
            amountTextView.setText(amountText);
        }

        private void setBudgetUsage(Budget budget) {
            // 这里使用模拟数据，实际应该从BudgetUsage查询
            // 模拟已使用金额为预算的60%
            BigDecimal usedAmount = budget.getBudgetAmount().multiply(new BigDecimal("0.6"));
            BigDecimal remainingAmount = budget.getBudgetAmount().subtract(usedAmount);
            double usagePercentage = usedAmount.divide(budget.getBudgetAmount(), 4, BigDecimal.ROUND_HALF_UP)
                    .multiply(new BigDecimal("100")).doubleValue();

            NumberFormat currencyFormat = NumberFormat.getCurrencyInstance(Locale.CHINA);
            
            // 设置使用情况文字
            usedAmountTextView.setText("已用: " + currencyFormat.format(usedAmount));
            remainingAmountTextView.setText("剩余: " + currencyFormat.format(remainingAmount));
            
            // 设置状态文字和颜色
            String statusText = String.format("已用 %.0f%%", usagePercentage);
            statusTextView.setText(statusText);
            
            // 根据使用率设置颜色
            if (usagePercentage >= budget.getAlertThreshold() * 100) {
                statusTextView.setTextColor(Color.parseColor("#FF5722")); // 红色警告
            } else if (usagePercentage >= budget.getAlertThreshold() * 80) {
                statusTextView.setTextColor(Color.parseColor("#FF9800")); // 橙色提醒
            } else {
                statusTextView.setTextColor(Color.parseColor("#4CAF50")); // 绿色正常
            }
            
            // 设置进度条
            progressBar.setMax(100);
            progressBar.setProgress((int) usagePercentage);
            progressBar.setSecondaryProgress((int) (budget.getAlertThreshold() * 100));
        }

        private void setBudgetRemark(Budget budget) {
            if (budget.getRemark() != null && !budget.getRemark().trim().isEmpty()) {
                remarkTextView.setText(budget.getRemark());
                remarkTextView.setVisibility(View.VISIBLE);
            } else {
                remarkTextView.setVisibility(View.GONE);
            }
        }

        private void setupClickListeners(Budget budget) {
            // 整个项目点击事件
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onBudgetItemClick(budget);
                }
            });

            // 更多操作按钮
            moreButton.setOnClickListener(v -> showPopupMenu(v, budget));
        }

        private void showPopupMenu(View view, Budget budget) {
            PopupMenu popupMenu = new PopupMenu(context, view);
            popupMenu.getMenuInflater().inflate(R.menu.budget_item_menu, popupMenu.getMenu());
            
            popupMenu.setOnMenuItemClickListener(item -> {
                int itemId = item.getItemId();
                if (itemId == R.id.menu_budget_edit) {
                    if (listener != null) {
                        listener.onBudgetItemEdit(budget);
                    }
                    return true;
                } else if (itemId == R.id.menu_budget_delete) {
                    if (listener != null) {
                        listener.onBudgetItemDelete(budget);
                    }
                    return true;
                }
                return false;
            });
            
            popupMenu.show();
        }
    }
}
