package com.example.likeqianwang.Repository;

import android.app.Application;
import android.os.Looper;

import androidx.lifecycle.LiveData;

import com.example.likeqianwang.Dao.TransactionTagDao;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.TransactionTag;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import android.os.Handler;

public class TransactionTagRepository {
    private final TransactionTagDao tagDao;
    private final ExecutorService executorService;

    public TransactionTagRepository(Application application) {
        AppDatabase database = AppDatabase.getInstance(application);
        this.tagDao = database.transactionTagDao();
        this.executorService = Executors.newSingleThreadExecutor();
    }

    // 获取所有标签
    public LiveData<List<TransactionTag>> getAllTags() {
        return tagDao.getAllTags();
    }

    // 根据分类获取标签
    public LiveData<List<TransactionTag>> getTagsByCategory(String category) {
        return tagDao.getTagsByCategory(category);
    }

    // 获取所有分类
    public LiveData<List<String>> getAllCategories() {
        return tagDao.getAllCategories();
    }

    // 插入标签
    public void insertTag(TransactionTag tag, OnTagOperationListener listener) {
        executorService.execute(() -> {
            try {
                long tagId = tagDao.insert(tag);
                tag.setTagId(tagId);
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onSuccess(tag);
                    }
                });
            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onError(e.getMessage());
                    }
                });
            }
        });
    }

    // 更新标签
    public void updateTag(TransactionTag tag, OnTagOperationListener listener) {
        executorService.execute(() -> {
            try {
                tagDao.update(tag);
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onSuccess(tag);
                    }
                });
            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onError(e.getMessage());
                    }
                });
            }
        });
    }

    // 删除标签
    public void deleteTag(TransactionTag tag, OnTagOperationListener listener) {
        executorService.execute(() -> {
            try {
                tagDao.delete(tag);
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onSuccess(tag);
                    }
                });
            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onError(e.getMessage());
                    }
                });
            }
        });
    }

    // 根据ID删除标签
    public void deleteTagById(long tagId, OnTagOperationListener listener) {
        executorService.execute(() -> {
            try {
                tagDao.deleteById(tagId);
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onSuccess(null);
                    }
                });
            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onError(e.getMessage());
                    }
                });
            }
        });
    }

    // 根据ID列表获取标签
    public void getTagsByIds(List<Long> tagIds, OnTagsLoadedListener listener) {
        executorService.execute(() -> {
            try {
                List<TransactionTag> tags = tagDao.getTagsByIds(tagIds);
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onTagsLoaded(tags);
                    }
                });
            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onError(e.getMessage());
                    }
                });
            }
        });
    }

    // 加载所有标签和分类
    public void loadTagsWithCategories(OnTagsWithCategoriesLoadedListener listener) {
        executorService.execute(() -> {
            try {
                List<String> categories = tagDao.getAllCategoriesSync();
                List<TransactionTag> allTags = tagDao.getAllTagsSync();

                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onTagsWithCategoriesLoaded(categories, allTags);
                    }
                });
            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onError(e.getMessage());
                    }
                });
            }
        });
    }

    // 回调接口
    public interface OnTagOperationListener {
        void onSuccess(TransactionTag tag);
        void onError(String error);
    }

    public interface OnTagsLoadedListener {
        void onTagsLoaded(List<TransactionTag> tags);
        void onError(String error);
    }

    public interface OnTagsWithCategoriesLoadedListener {
        void onTagsWithCategoriesLoaded(List<String> categories, List<TransactionTag> tags);
        void onError(String error);
    }
}
