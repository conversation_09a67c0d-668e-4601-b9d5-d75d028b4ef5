<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <!-- 周期记账服务需要的权限 -->
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />

    <application
        android:name=".LikeQianWangApplication"
        android:allowBackup="true"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.LikeQianWang"
        tools:targetApi="35">
        <activity
            android:name=".FillNewAccountInfoActivity"
            android:exported="false" />
        <activity
            android:name=".TagManagementActivity"
            android:exported="false" />
        <activity
            android:name=".ui.budget_settings.BudgetSettingsActivity"
            android:exported="false" />
        <activity
            android:name=".ui.budget_management.BudgetManagementActivity"
            android:exported="false" />
        <activity
            android:name=".ui.category_management.CategoryManagementActivity"
            android:exported="false" />
        <activity
            android:name=".ui.recurring_transactions.RecurringTransactionsActivity"
            android:exported="false" />
        <activity
            android:name=".RecordingPageActivity"
            android:exported="false"
            android:windowSoftInputMode="adjustNothing" />
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:label="@string/app_name">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>

        <!-- 周期记账服务 -->
        <service
            android:name=".service.RecurringTransactionService"
            android:enabled="true"
            android:exported="false" />
    </application>

</manifest>