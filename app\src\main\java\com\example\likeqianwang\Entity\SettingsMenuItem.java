package com.example.likeqianwang.Entity;

public class SettingsMenuItem {
    private int iconRes;
    private String title;
    private String subtitle;
    private SettingsMenuAction action;

    public enum SettingsMenuAction {
        BUDGET_SETTINGS,
        BUDGET_MANAGEMENT,
        CATEGORY_MANAGEMENT,
        ACCOUNT_MANAGEMENT,
        DATA_BACKUP,
        RECURRING_TRANSACTIONS,
        TAG_MANAGEMENT
    }

    public SettingsMenuItem(int iconRes, String title, String subtitle, SettingsMenuAction action) {
        this.iconRes = iconRes;
        this.title = title;
        this.subtitle = subtitle;
        this.action = action;
    }

    public SettingsMenuItem(int iconRes, String title, SettingsMenuAction action) {
        this(iconRes, title, null, action);
    }

    public int getIconRes() {
        return iconRes;
    }

    public void setIconRes(int iconRes) {
        this.iconRes = iconRes;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public SettingsMenuAction getAction() {
        return action;
    }

    public void setAction(SettingsMenuAction action) {
        this.action = action;
    }

    public boolean hasSubtitle() {
        return subtitle != null && !subtitle.trim().isEmpty();
    }
}
