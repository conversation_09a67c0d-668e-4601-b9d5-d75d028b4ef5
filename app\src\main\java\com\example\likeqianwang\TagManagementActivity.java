package com.example.likeqianwang;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Toast;

import java.util.ArrayList;
import java.util.List;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.ViewModel.TransactionTagViewModel;
import com.example.likeqianwang.adapters.TagManagementAdapter;
import com.example.likeqianwang.ui.dialogs.TagEditBottomSheetDialogFragment;

import java.util.ArrayList;
import java.util.List;

public class TagManagementActivity extends AppCompatActivity {
    private static final String TAG = "TagManagementActivity";
    private TransactionTagViewModel viewModel;
    private TagManagementAdapter adapter;
    private RecyclerView rvTagManagement;
    private LinearLayout llEmptyState;
    private ImageView ivBack;
    private ImageView ivAddTag;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "onCreate: Starting TagManagementActivity");

        try {
            setContentView(R.layout.activity_tag_management);
            Log.d(TAG, "onCreate: Layout set successfully");

            initViews();
            Log.d(TAG, "onCreate: Views initialized");

            initViewModel();
            Log.d(TAG, "onCreate: ViewModel initialized");

            setupRecyclerView();
            Log.d(TAG, "onCreate: RecyclerView setup complete");

            setupObservers();
            Log.d(TAG, "onCreate: Observers setup complete");

            setupClickListeners();
            Log.d(TAG, "onCreate: Click listeners setup complete");

        } catch (Exception e) {
            Log.e(TAG, "onCreate: Error during initialization", e);
            finish();
        }
    }

    private void initViews() {
        try {
            rvTagManagement = findViewById(R.id.rv_tag_management);
            Log.d(TAG, "initViews: rvTagManagement found: " + (rvTagManagement != null));

            llEmptyState = findViewById(R.id.ll_empty_state);
            Log.d(TAG, "initViews: llEmptyState found: " + (llEmptyState != null));

            ivBack = findViewById(R.id.iv_back);
            Log.d(TAG, "initViews: ivBack found: " + (ivBack != null));

            ivAddTag = findViewById(R.id.iv_add_tag);
            Log.d(TAG, "initViews: ivAddTag found: " + (ivAddTag != null));

        } catch (Exception e) {
            Log.e(TAG, "initViews: Error finding views", e);
            throw e;
        }
    }

    private void initViewModel() {
        viewModel = new ViewModelProvider(this).get(TransactionTagViewModel.class);
    }

    private void setupRecyclerView() {
        rvTagManagement.setLayoutManager(new LinearLayoutManager(this));
        adapter = new TagManagementAdapter(this, new ArrayList<>(), null);
        adapter.setOnTagActionListener(new TagManagementAdapter.OnTagActionListener() {
            @Override
            public void onAddTagToCategory(String category) {
                showTagEditDialog(null, category);
            }

            @Override
            public void onEditTag(TransactionTag tag) {
                showTagEditDialog(tag, null);
            }

            @Override
            public void onDeleteTag(TransactionTag tag) {
                showDeleteConfirmDialog(tag);
            }
        });
        rvTagManagement.setAdapter(adapter);
    }

    private void setupObservers() {
        // 观察分类化的标签数据
        viewModel.getCategorizedTags().observe(this, categorizedTags -> {
            try {
                if (categorizedTags != null && !categorizedTags.isEmpty()) {
                    List<String> categories = new ArrayList<>(categorizedTags.keySet());
                    if (adapter != null) {
                        adapter.updateData(categories, categorizedTags);
                    }
                    showContent();
                } else {
                    showEmptyState();
                }
            } catch (Exception e) {
                e.printStackTrace();
                showEmptyState();
            }
        });

        // 观察操作状态
        viewModel.getOperationStatus().observe(this, status -> {
            try {
                if (status != null && !status.isEmpty()) {
                    Toast.makeText(this, status, Toast.LENGTH_SHORT).show();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });

        // 观察加载状态
        viewModel.getIsLoading().observe(this, isLoading -> {
            try {
                // 可以在这里显示/隐藏加载指示器
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    private void setupClickListeners() {
        ivBack.setOnClickListener(v -> finish());

        ivAddTag.setOnClickListener(v -> showTagEditDialog(null, null));

        // 长按添加按钮显示分类管理菜单
        ivAddTag.setOnLongClickListener(v -> {
            showCategoryManagementMenu();
            return true;
        });
    }

    private void showCategoryManagementMenu() {
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        builder.setTitle("分类管理");

        String[] options = {"添加标签", "管理分类"};
        builder.setItems(options, (dialog, which) -> {
            switch (which) {
                case 0:
                    showTagEditDialog(null, null);
                    break;
                case 1:
                    showCategoryManagementDialog();
                    break;
            }
        });

        builder.show();
    }

    private void showCategoryManagementDialog() {
        // 获取所有分类
        viewModel.getAllCategories().observe(this, categories -> {
            if (categories != null) {
                showCategoryListDialog(categories);
            }
        });
    }

    private void showCategoryListDialog(List<String> categories) {
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        builder.setTitle("标签分类管理");

        // 添加"新增分类"选项
        List<String> displayCategories = new ArrayList<>(categories);
        displayCategories.add("+ 新增分类");

        String[] categoryArray = displayCategories.toArray(new String[0]);

        builder.setItems(categoryArray, (dialog, which) -> {
            if (which == displayCategories.size() - 1) {
                // 新增分类
                showAddCategoryDialog();
            } else {
                // 编辑现有分类
                String category = categories.get(which);
                showEditCategoryDialog(category);
            }
        });

        builder.setNegativeButton("取消", null);
        builder.show();
    }

    private void showAddCategoryDialog() {
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        builder.setTitle("新增分类");

        EditText editText = new EditText(this);
        editText.setHint("请输入分类名称");
        builder.setView(editText);

        builder.setPositiveButton("确定", (dialog, which) -> {
            String categoryName = editText.getText().toString().trim();
            if (!categoryName.isEmpty()) {
                // 创建一个临时标签来添加新分类
                TransactionTag tempTag = new TransactionTag("临时", "#ee3f4d", categoryName);
                viewModel.addTag(tempTag);
                // 立即删除这个临时标签，但分类会保留
                viewModel.deleteTag(tempTag);
            } else {
                Toast.makeText(this, "分类名称不能为空", Toast.LENGTH_SHORT).show();
            }
        });

        builder.setNegativeButton("取消", null);
        builder.show();
    }

    private void showEditCategoryDialog(String category) {
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        builder.setTitle("编辑分类: " + category);

        String[] options = {"重命名分类", "删除分类"};
        builder.setItems(options, (dialog, which) -> {
            switch (which) {
                case 0:
                    showRenameCategoryDialog(category);
                    break;
                case 1:
                    showDeleteCategoryDialog(category);
                    break;
            }
        });

        builder.show();
    }

    private void showRenameCategoryDialog(String oldCategory) {
        androidx.appcompat.app.AlertDialog.Builder builder = new androidx.appcompat.app.AlertDialog.Builder(this);
        builder.setTitle("重命名分类");

        EditText editText = new EditText(this);
        editText.setText(oldCategory);
        editText.setSelection(oldCategory.length());
        builder.setView(editText);

        builder.setPositiveButton("确定", (dialog, which) -> {
            String newCategory = editText.getText().toString().trim();
            if (!newCategory.isEmpty() && !newCategory.equals(oldCategory)) {
                viewModel.renameCategoryForAllTags(oldCategory, newCategory);
            } else if (newCategory.isEmpty()) {
                Toast.makeText(this, "分类名称不能为空", Toast.LENGTH_SHORT).show();
            }
        });

        builder.setNegativeButton("取消", null);
        builder.show();
    }

    private void showDeleteCategoryDialog(String category) {
        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("删除分类")
                .setMessage("确定要删除分类 \"" + category + "\" 吗？\n\n该分类下的所有标签也将被删除。")
                .setPositiveButton("删除", (dialog, which) -> {
                    viewModel.deleteCategoryAndAllTags(category);
                })
                .setNegativeButton("取消", null)
                .show();
    }

    private void showContent() {
        rvTagManagement.setVisibility(View.VISIBLE);
        llEmptyState.setVisibility(View.GONE);
    }

    private void showEmptyState() {
        rvTagManagement.setVisibility(View.GONE);
        llEmptyState.setVisibility(View.VISIBLE);
    }

    private void showTagEditDialog(TransactionTag tag, String defaultCategory) {
        TagEditBottomSheetDialogFragment dialog =
                TagEditBottomSheetDialogFragment.newInstance(tag, defaultCategory);
        dialog.setOnTagSaveListener(new TagEditBottomSheetDialogFragment.OnTagSaveListener() {
            @Override
            public void onTagSaved(TransactionTag savedTag, boolean isEdit) {
                if (isEdit) {
                    viewModel.updateTag(savedTag);
                } else {
                    viewModel.addTag(savedTag);
                }
            }
        });
        dialog.show(getSupportFragmentManager(), "TagEditDialog");
    }

    private void showDeleteConfirmDialog(TransactionTag tag) {
        new AlertDialog.Builder(this)
                .setTitle("删除标签")
                .setMessage("确定要删除标签 \"" + tag.getTagName() + "\" 吗？")
                .setPositiveButton("删除", (dialog, which) -> {
                    viewModel.deleteTag(tag);
                })
                .setNegativeButton("取消", null)
                .show();
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 刷新数据
        viewModel.refresh();
    }

}
