package com.example.likeqianwang.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.PopupMenu;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.Entity.TransactionSubcategory;
import com.example.likeqianwang.R;

import java.util.Collections;
import java.util.List;

public class CategoryManagementAdapter extends RecyclerView.Adapter<CategoryManagementAdapter.CategoryViewHolder> {

    private final Context context;
    private final List<TransactionCategory> categoryList;
    private OnCategoryActionListener listener;

    public interface OnCategoryActionListener {
        void onCategoryEdit(TransactionCategory category);
        void onCategoryDelete(TransactionCategory category);
        void onSubcategoryAdd(TransactionCategory parentCategory);
        void onSubcategoryEdit(TransactionSubcategory subcategory);
        void onSubcategoryDelete(TransactionSubcategory subcategory);
    }

    public CategoryManagementAdapter(Context context, List<TransactionCategory> categoryList) {
        this.context = context;
        this.categoryList = categoryList;
    }

    public void setOnCategoryActionListener(OnCategoryActionListener listener) {
        this.listener = listener;
    }

    public void moveItem(int fromPosition, int toPosition) {
        if (fromPosition < toPosition) {
            for (int i = fromPosition; i < toPosition; i++) {
                Collections.swap(categoryList, i, i + 1);
            }
        } else {
            for (int i = fromPosition; i > toPosition; i--) {
                Collections.swap(categoryList, i, i - 1);
            }
        }
        notifyItemMoved(fromPosition, toPosition);
    }

    @NonNull
    @Override
    public CategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_category_management, parent, false);
        return new CategoryViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CategoryViewHolder holder, int position) {
        TransactionCategory category = categoryList.get(position);
        holder.bind(category);
    }

    @Override
    public int getItemCount() {
        return categoryList != null ? categoryList.size() : 0;
    }

    class CategoryViewHolder extends RecyclerView.ViewHolder {
        private final ImageView dragHandle;
        private final ImageView categoryIcon;
        private final TextView categoryName;
        private final TextView subcategoryCount;
        private final ImageView addSubcategoryButton;
        private final ImageView moreButton;
        private final RecyclerView subcategoryRecyclerView;

        private SubcategoryManagementAdapter subcategoryAdapter;

        public CategoryViewHolder(@NonNull View itemView) {
            super(itemView);
            dragHandle = itemView.findViewById(R.id.category_item_drag_handle);
            categoryIcon = itemView.findViewById(R.id.category_item_icon);
            categoryName = itemView.findViewById(R.id.category_item_name);
            subcategoryCount = itemView.findViewById(R.id.category_item_subcategory_count);
            addSubcategoryButton = itemView.findViewById(R.id.category_item_add_subcategory);
            moreButton = itemView.findViewById(R.id.category_item_more);
            subcategoryRecyclerView = itemView.findViewById(R.id.category_item_subcategory_list);
        }

        public void bind(TransactionCategory category) {
            // 设置分类信息
            categoryName.setText(category.getCategoryName());
            categoryIcon.setImageResource(category.getCategoryIcon());

            // 设置子分类信息
            List<TransactionSubcategory> subcategories = category.getSubcategories();
            if (subcategories != null && !subcategories.isEmpty()) {
                subcategoryCount.setVisibility(View.VISIBLE);
                subcategoryCount.setText(subcategories.size() + "个子分类");
                
                // 设置子分类列表
                setupSubcategoryList(category, subcategories);
                subcategoryRecyclerView.setVisibility(View.VISIBLE);
            } else {
                subcategoryCount.setVisibility(View.GONE);
                subcategoryRecyclerView.setVisibility(View.GONE);
            }

            // 设置点击事件
            setupClickListeners(category);
        }

        private void setupSubcategoryList(TransactionCategory parentCategory, List<TransactionSubcategory> subcategories) {
            if (subcategoryAdapter == null) {
                subcategoryAdapter = new SubcategoryManagementAdapter(context, subcategories);
                subcategoryAdapter.setOnSubcategoryActionListener(new SubcategoryManagementAdapter.OnSubcategoryActionListener() {
                    @Override
                    public void onSubcategoryEdit(TransactionSubcategory subcategory) {
                        if (listener != null) {
                            listener.onSubcategoryEdit(subcategory);
                        }
                    }

                    @Override
                    public void onSubcategoryDelete(TransactionSubcategory subcategory) {
                        if (listener != null) {
                            listener.onSubcategoryDelete(subcategory);
                        }
                    }
                });
                
                subcategoryRecyclerView.setLayoutManager(new LinearLayoutManager(context));
                subcategoryRecyclerView.setAdapter(subcategoryAdapter);
            } else {
                subcategoryAdapter.updateSubcategories(subcategories);
            }
        }

        private void setupClickListeners(TransactionCategory category) {
            // 添加子分类按钮
            addSubcategoryButton.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onSubcategoryAdd(category);
                }
            });

            // 更多操作按钮
            moreButton.setOnClickListener(v -> showPopupMenu(v, category));

            // 整个项目点击事件（编辑）
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onCategoryEdit(category);
                }
            });
        }

        private void showPopupMenu(View view, TransactionCategory category) {
            PopupMenu popupMenu = new PopupMenu(context, view);
            popupMenu.getMenuInflater().inflate(R.menu.category_item_menu, popupMenu.getMenu());
            
            popupMenu.setOnMenuItemClickListener(item -> {
                int itemId = item.getItemId();
                if (itemId == R.id.menu_category_edit) {
                    if (listener != null) {
                        listener.onCategoryEdit(category);
                    }
                    return true;
                } else if (itemId == R.id.menu_category_delete) {
                    if (listener != null) {
                        listener.onCategoryDelete(category);
                    }
                    return true;
                } else if (itemId == R.id.menu_category_add_subcategory) {
                    if (listener != null) {
                        listener.onSubcategoryAdd(category);
                    }
                    return true;
                }
                return false;
            });
            
            popupMenu.show();
        }
    }

    // 子分类适配器
    static class SubcategoryManagementAdapter extends RecyclerView.Adapter<SubcategoryManagementAdapter.SubcategoryViewHolder> {

        private final Context context;
        private List<TransactionSubcategory> subcategoryList;
        private OnSubcategoryActionListener listener;

        public interface OnSubcategoryActionListener {
            void onSubcategoryEdit(TransactionSubcategory subcategory);
            void onSubcategoryDelete(TransactionSubcategory subcategory);
        }

        public SubcategoryManagementAdapter(Context context, List<TransactionSubcategory> subcategoryList) {
            this.context = context;
            this.subcategoryList = subcategoryList;
        }

        public void setOnSubcategoryActionListener(OnSubcategoryActionListener listener) {
            this.listener = listener;
        }

        public void updateSubcategories(List<TransactionSubcategory> subcategories) {
            this.subcategoryList = subcategories;
            notifyDataSetChanged();
        }

        @NonNull
        @Override
        public SubcategoryViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            View view = LayoutInflater.from(context).inflate(R.layout.item_subcategory_management, parent, false);
            return new SubcategoryViewHolder(view);
        }

        @Override
        public void onBindViewHolder(@NonNull SubcategoryViewHolder holder, int position) {
            TransactionSubcategory subcategory = subcategoryList.get(position);
            holder.bind(subcategory);
        }

        @Override
        public int getItemCount() {
            return subcategoryList != null ? subcategoryList.size() : 0;
        }

        class SubcategoryViewHolder extends RecyclerView.ViewHolder {
            private final ImageView subcategoryIcon;
            private final TextView subcategoryName;
            private final ImageView moreButton;

            public SubcategoryViewHolder(@NonNull View itemView) {
                super(itemView);
                subcategoryIcon = itemView.findViewById(R.id.subcategory_item_icon);
                subcategoryName = itemView.findViewById(R.id.subcategory_item_name);
                moreButton = itemView.findViewById(R.id.subcategory_item_more);
            }

            public void bind(TransactionSubcategory subcategory) {
                subcategoryName.setText(subcategory.getSubcategoryName());
                subcategoryIcon.setImageResource(subcategory.getSubcategoryIcon());

                // 设置点击事件
                moreButton.setOnClickListener(v -> showPopupMenu(v, subcategory));

                itemView.setOnClickListener(v -> {
                    if (listener != null) {
                        listener.onSubcategoryEdit(subcategory);
                    }
                });
            }

            private void showPopupMenu(View view, TransactionSubcategory subcategory) {
                PopupMenu popupMenu = new PopupMenu(context, view);
                popupMenu.getMenuInflater().inflate(R.menu.subcategory_item_menu, popupMenu.getMenu());

                popupMenu.setOnMenuItemClickListener(item -> {
                    int itemId = item.getItemId();
                    if (itemId == R.id.menu_subcategory_edit) {
                        if (listener != null) {
                            listener.onSubcategoryEdit(subcategory);
                        }
                        return true;
                    } else if (itemId == R.id.menu_subcategory_delete) {
                        if (listener != null) {
                            listener.onSubcategoryDelete(subcategory);
                        }
                        return true;
                    }
                    return false;
                });

                popupMenu.show();
            }
        }
    }
}
