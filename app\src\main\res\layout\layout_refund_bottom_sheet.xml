<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_background"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingBottom="16dp">

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/receipt_退款"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <TextView
            android:id="@+id/refund_cancel"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="8dp"
            android:text="@string/receipt_取消"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <TextView
            android:id="@+id/refund_confirm"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="8dp"
            android:text="@string/receipt_确定"
            android:textColor="@color/HuaQing"
            android:textSize="14sp"
            android:textStyle="bold" />

    </LinearLayout>

    <!-- 原交易信息 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/receipt_原交易信息"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:id="@+id/refund_original_icon"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginEnd="8dp"
                    android:contentDescription="@string/receipt_desc_交易类型图标" />

                <TextView
                    android:id="@+id/refund_original_category"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/black"
                    android:textSize="14sp" />

                <TextView
                    android:id="@+id/refund_original_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="14sp"
                    android:textStyle="bold" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 退款金额输入 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="16dp"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/receipt_退款金额"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:textStyle="bold" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="￥"
                    android:textColor="@color/black"
                    android:textSize="18sp"
                    android:textStyle="bold" />

                <EditText
                    android:id="@+id/refund_amount_input"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@null"
                    android:hint="@string/receipt_请输入退款金额"
                    android:inputType="numberDecimal"
                    android:padding="8dp"
                    android:textColor="@color/black"
                    android:textSize="18sp" />

                <TextView
                    android:id="@+id/refund_full_amount_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:padding="8dp"
                    android:text="@string/receipt_全额退款"
                    android:textColor="@color/HuaQing"
                    android:textSize="12sp" />

            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <!-- 退款原因输入 -->
    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardCornerRadius="8dp"
        app:cardElevation="2dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="16dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="@string/receipt_退款原因"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:textStyle="bold" />

            <EditText
                android:id="@+id/refund_reason_input"
                android:layout_width="match_parent"
                android:layout_height="80dp"
                android:background="@drawable/edit_text_background"
                android:gravity="top|start"
                android:hint="@string/receipt_请输入退款原因"
                android:inputType="textMultiLine"
                android:padding="12dp"
                android:textColor="@color/black"
                android:textSize="14sp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</LinearLayout>
