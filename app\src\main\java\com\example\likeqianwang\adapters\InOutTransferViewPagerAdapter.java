package com.example.likeqianwang.adapters;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import java.util.List;

public class InOutTransferViewPagerAdapter extends FragmentStateAdapter {

    List<Fragment> InOut_Transfer_fragments;

    public InOutTransferViewPagerAdapter(@NonNull FragmentManager fragmentManager, @NonNull Lifecycle lifecycle, List<Fragment> InOut_Transfer_fragments) {
        super(fragmentManager, lifecycle);
        this.InOut_Transfer_fragments = InOut_Transfer_fragments;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        return InOut_Transfer_fragments.get(position);
    }

    @Override
    public int getItemCount() {
        return InOut_Transfer_fragments.size();
    }
}
