<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/widget_common_bg"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/tag_edit_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="添加标签"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/tag_edit_close"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/receipt_关闭"
            android:src="@drawable/ic_close"
            app:tint="@color/grey" />

    </LinearLayout>

    <!-- 标签名称 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="标签名称"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/tag_edit_name"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/style_tag_management_item_bg"
            android:hint="请输入标签名称"
            android:inputType="text"
            android:maxLength="10"
            android:paddingHorizontal="12dp"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- 标签分类 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="标签分类"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <Spinner
            android:id="@+id/tag_edit_category_spinner"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginTop="8dp" />

    </LinearLayout>

    <!-- 新建分类（条件显示） -->
    <LinearLayout
        android:id="@+id/tag_edit_new_category_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="vertical"
        android:visibility="gone">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="新分类名称"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <EditText
            android:id="@+id/tag_edit_new_category"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:layout_marginTop="8dp"
            android:background="@drawable/style_tag_management_item_bg"
            android:hint="请输入新分类名称"
            android:inputType="text"
            android:maxLength="10"
            android:paddingHorizontal="12dp"
            android:textSize="14sp" />

    </LinearLayout>

    <!-- 标签颜色 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:orientation="vertical">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="标签颜色"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:textStyle="bold" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/tag_edit_color_picker"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            tools:listitem="@layout/style_tag_color_picker" />

    </LinearLayout>

    <!-- 操作按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="horizontal">

        <Button
            android:id="@+id/tag_edit_cancel"
            style="@style/Widget.Material3.Button.TextButton"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="8dp"
            android:layout_weight="1"
            android:text="@string/取消"
            android:textColor="@color/grey" />

        <Button
            android:id="@+id/tag_edit_save"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:backgroundTint="@color/HuaQing"
            android:text="@string/budget_保存"
            android:textColor="@color/white" />

    </LinearLayout>

</LinearLayout>
