package com.example.likeqianwang.ViewModel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.example.likeqianwang.Entity.Notebook;
import com.example.likeqianwang.Repository.NotebookRepository;

import java.util.List;

public class NotebookViewModel extends AndroidViewModel {
    private final NotebookRepository repository;
    private final MutableLiveData<String> operationStatus = new MutableLiveData<>();
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>();
    private final MutableLiveData<Notebook> currentNotebook = new MutableLiveData<>();

    public NotebookViewModel(@NonNull Application application) {
        super(application);
        repository = new NotebookRepository(application);
        isLoading.setValue(false);
        loadCurrentNotebook();
    }

    // LiveData getters
    public LiveData<String> getOperationStatus() {
        return operationStatus;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }

    public LiveData<Notebook> getCurrentNotebook() {
        return currentNotebook;
    }

    // Repository methods
    public LiveData<List<Notebook>> getAllActiveNotebooks() {
        return repository.getAllActiveNotebooks();
    }

    public LiveData<Notebook> getDefaultNotebook() {
        return repository.getDefaultNotebook();
    }

    public LiveData<Notebook> getNotebookById(String notebookId) {
        return repository.getNotebookById(notebookId);
    }

    public LiveData<List<Notebook>> searchNotebooks(String query) {
        return repository.searchNotebooks(query);
    }

    // 插入或更新账本
    public void insertOrUpdateNotebook(Notebook notebook) {
        isLoading.setValue(true);
        repository.insertOrUpdateNotebook(notebook, new NotebookRepository.NotebookOperationCallback() {
            @Override
            public void onSuccess(long result) {
                isLoading.setValue(false);
                operationStatus.setValue("账本保存成功");
                loadCurrentNotebook();
            }

            @Override
            public void onError(String error) {
                isLoading.setValue(false);
                errorMessage.setValue("保存失败: " + error);
            }
        });
    }

    // 删除账本
    public void deleteNotebook(Notebook notebook) {
        isLoading.setValue(true);
        repository.deleteNotebook(notebook, new NotebookRepository.NotebookOperationCallback() {
            @Override
            public void onSuccess(long result) {
                isLoading.setValue(false);
                operationStatus.setValue("账本删除成功");
                loadCurrentNotebook();
            }

            @Override
            public void onError(String error) {
                isLoading.setValue(false);
                errorMessage.setValue("删除失败: " + error);
            }
        });
    }

    // 设置为默认账本
    public void setAsDefaultNotebook(String notebookId) {
        isLoading.setValue(true);
        repository.setAsDefaultNotebook(notebookId, new NotebookRepository.NotebookOperationCallback() {
            @Override
            public void onSuccess(long result) {
                isLoading.setValue(false);
                operationStatus.setValue("默认账本设置成功");
                loadCurrentNotebook();
            }

            @Override
            public void onError(String error) {
                isLoading.setValue(false);
                errorMessage.setValue("设置失败: " + error);
            }
        });
    }

    // 切换账本
    public void switchToNotebook(String notebookId) {
        isLoading.setValue(true);
        repository.switchToNotebook(notebookId, new NotebookRepository.NotebookOperationCallback() {
            @Override
            public void onSuccess(long result) {
                isLoading.setValue(false);
                operationStatus.setValue("账本切换成功");
                loadCurrentNotebook();
            }

            @Override
            public void onError(String error) {
                isLoading.setValue(false);
                errorMessage.setValue("切换失败: " + error);
            }
        });
    }

    // 创建默认账本
    public void createDefaultNotebook() {
        isLoading.setValue(true);
        repository.createDefaultNotebook(new NotebookRepository.NotebookOperationCallback() {
            @Override
            public void onSuccess(long result) {
                isLoading.setValue(false);
                operationStatus.setValue("默认账本创建成功");
                loadCurrentNotebook();
            }

            @Override
            public void onError(String error) {
                isLoading.setValue(false);
                errorMessage.setValue("创建失败: " + error);
            }
        });
    }

    // 加载当前账本
    private void loadCurrentNotebook() {
        repository.getCurrentNotebook(new NotebookRepository.NotebookQueryCallback() {
            @Override
            public void onSuccess(Notebook notebook) {
                currentNotebook.setValue(notebook);
            }

            @Override
            public void onError(String error) {
                // 如果没有当前账本，尝试创建默认账本
                createDefaultNotebook();
            }
        });
    }

    // 刷新当前账本
    public void refreshCurrentNotebook() {
        loadCurrentNotebook();
    }

    // 清除状态消息
    public void clearMessages() {
        operationStatus.setValue("");
        errorMessage.setValue("");
    }
}
