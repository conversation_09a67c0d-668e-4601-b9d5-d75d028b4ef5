package com.example.likeqianwang.ui.dialogs;

import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.R;
import com.example.likeqianwang.adapters.TagColorPickerAdapter;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.util.Arrays;
import java.util.List;

public class TagEditBottomSheetDialogFragment extends BottomSheetDialogFragment {

    private static final String ARG_TAG = "tag";
    private static final String ARG_DEFAULT_CATEGORY = "default_category";

    // UI组件
    private TextView titleTextView;
    private ImageView closeButton;
    private EditText tagNameEditText;
    private Spinner categorySpinner;
    private LinearLayout newCategoryContainer;
    private EditText newCategoryEditText;
    private RecyclerView colorPickerRecyclerView;
    private Button cancelButton;
    private Button saveButton;

    // 数据
    private TransactionTag editingTag;
    private String defaultCategory;
    private OnTagSaveListener listener;

    // 预定义数据
    private final List<String> predefinedCategories = Arrays.asList(
            "交通", "餐饮", "购物", "娱乐", "医疗", "教育", "住房", "通讯", "其他", "新建分类..."
    );
    private final List<String> predefinedColors = Arrays.asList(
            "#ee3f4d", "#ff6b35", "#f7931e", "#ffd23f", "#06d6a0",
            "#118ab2", "#073b4c", "#8e44ad", "#e91e63", "#795548"
    );
    private String selectedColor = "#ee3f4d";
    private TagColorPickerAdapter colorAdapter;

    public interface OnTagSaveListener {
        void onTagSaved(TransactionTag tag, boolean isEdit);
    }

    public static TagEditBottomSheetDialogFragment newInstance(TransactionTag tag, String defaultCategory) {
        TagEditBottomSheetDialogFragment fragment = new TagEditBottomSheetDialogFragment();
        Bundle args = new Bundle();
        args.putSerializable(ARG_TAG, tag);
        args.putString(ARG_DEFAULT_CATEGORY, defaultCategory);
        fragment.setArguments(args);
        return fragment;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            setupFullHeight(bottomSheetDialog);
        });
        return dialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.layout_tag_edit_bottom_sheet, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        parseArguments();
        initViews(view);
        setupSpinner();
        setupColorPicker();
        setupClickListeners();
        populateFields();
    }

    private void parseArguments() {
        if (getArguments() != null) {
            editingTag = (TransactionTag) getArguments().getSerializable(ARG_TAG);
            defaultCategory = getArguments().getString(ARG_DEFAULT_CATEGORY);
        }
    }

    private void initViews(View view) {
        titleTextView = view.findViewById(R.id.tag_edit_title);
        closeButton = view.findViewById(R.id.tag_edit_close);
        tagNameEditText = view.findViewById(R.id.tag_edit_name);
        categorySpinner = view.findViewById(R.id.tag_edit_category_spinner);
        newCategoryContainer = view.findViewById(R.id.tag_edit_new_category_container);
        newCategoryEditText = view.findViewById(R.id.tag_edit_new_category);
        colorPickerRecyclerView = view.findViewById(R.id.tag_edit_color_picker);
        cancelButton = view.findViewById(R.id.tag_edit_cancel);
        saveButton = view.findViewById(R.id.tag_edit_save);

        // 设置标题
        if (editingTag != null) {
            titleTextView.setText("编辑标签");
        } else {
            titleTextView.setText("添加标签");
        }
    }

    private void setupSpinner() {
        ArrayAdapter<String> adapter = new ArrayAdapter<>(requireContext(),
                android.R.layout.simple_spinner_item, predefinedCategories);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        categorySpinner.setAdapter(adapter);

        categorySpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                if (position == predefinedCategories.size() - 1) {
                    // 选择了"新建分类..."
                    newCategoryContainer.setVisibility(View.VISIBLE);
                } else {
                    newCategoryContainer.setVisibility(View.GONE);
                }
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
    }

    private void setupColorPicker() {
        colorAdapter = new TagColorPickerAdapter(requireContext(), predefinedColors, selectedColor);
        colorAdapter.setOnColorSelectedListener(color -> selectedColor = color);
        
        colorPickerRecyclerView.setLayoutManager(new GridLayoutManager(requireContext(), 5));
        colorPickerRecyclerView.setAdapter(colorAdapter);
    }

    private void setupClickListeners() {
        closeButton.setOnClickListener(v -> dismiss());
        cancelButton.setOnClickListener(v -> dismiss());
        saveButton.setOnClickListener(v -> saveTag());
    }

    private void populateFields() {
        if (editingTag != null) {
            tagNameEditText.setText(editingTag.getTagName());
            
            // 设置分类
            String category = editingTag.getTagCategory();
            if (category != null) {
                int index = predefinedCategories.indexOf(category);
                if (index >= 0) {
                    categorySpinner.setSelection(index);
                } else {
                    // 自定义分类
                    categorySpinner.setSelection(predefinedCategories.size() - 1);
                    newCategoryEditText.setText(category);
                }
            }
            
            // 设置颜色
            if (editingTag.getTagColor() != null) {
                selectedColor = editingTag.getTagColor();
                colorAdapter.setSelectedColor(selectedColor);
            }
        } else if (defaultCategory != null) {
            int index = predefinedCategories.indexOf(defaultCategory);
            if (index >= 0) {
                categorySpinner.setSelection(index);
            }
        }
    }

    private void saveTag() {
        String tagName = tagNameEditText.getText().toString().trim();
        if (TextUtils.isEmpty(tagName)) {
            Toast.makeText(getContext(), "请输入标签名称", Toast.LENGTH_SHORT).show();
            return;
        }

        String category;
        int selectedPosition = categorySpinner.getSelectedItemPosition();
        if (selectedPosition == predefinedCategories.size() - 1) {
            // 新建分类
            category = newCategoryEditText.getText().toString().trim();
            if (TextUtils.isEmpty(category)) {
                Toast.makeText(getContext(), "请输入新分类名称", Toast.LENGTH_SHORT).show();
                return;
            }
        } else {
            category = predefinedCategories.get(selectedPosition);
        }

        TransactionTag tag;
        boolean isEdit = editingTag != null;

        if (isEdit) {
            tag = editingTag;
            tag.setTagName(tagName);
            tag.setTagCategory(category);
            tag.setTagColor(selectedColor);
        } else {
            tag = new TransactionTag(tagName, selectedColor, category);
        }

        if (listener != null) {
            listener.onTagSaved(tag, isEdit);
        }

        dismiss();
    }

    public void setOnTagSaveListener(OnTagSaveListener listener) {
        this.listener = listener;
    }

    private void setupFullHeight(BottomSheetDialog bottomSheetDialog) {
        try {
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                bottomSheet.getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
            }
        } catch (Exception e) {
            // 忽略错误
        }
    }
}
