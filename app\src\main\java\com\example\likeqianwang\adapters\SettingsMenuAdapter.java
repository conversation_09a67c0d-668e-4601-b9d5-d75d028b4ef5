package com.example.likeqianwang.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.SettingsMenuItem;
import com.example.likeqianwang.R;

import java.util.List;

public class SettingsMenuAdapter extends RecyclerView.Adapter<SettingsMenuAdapter.SettingsMenuViewHolder> {

    private final Context context;
    private final List<SettingsMenuItem> menuItems;
    private OnSettingsMenuItemClickListener onItemClickListener;

    public interface OnSettingsMenuItemClickListener {
        void onSettingsMenuItemClick(SettingsMenuItem item);
    }

    public SettingsMenuAdapter(Context context, List<SettingsMenuItem> menuItems) {
        this.context = context;
        this.menuItems = menuItems;
    }

    @NonNull
    @Override
    public SettingsMenuViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_settings_menu, parent, false);
        return new SettingsMenuViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SettingsMenuViewHolder holder, int position) {
        SettingsMenuItem item = menuItems.get(position);
        holder.bind(item);
    }

    @Override
    public int getItemCount() {
        return menuItems != null ? menuItems.size() : 0;
    }

    public void setOnItemClickListener(OnSettingsMenuItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    class SettingsMenuViewHolder extends RecyclerView.ViewHolder {
        private final ImageView iconView;
        private final TextView titleView;
        private final TextView subtitleView;
        private final ImageView arrowView;

        public SettingsMenuViewHolder(@NonNull View itemView) {
            super(itemView);
            iconView = itemView.findViewById(R.id.settings_menu_item_icon);
            titleView = itemView.findViewById(R.id.settings_menu_item_title);
            subtitleView = itemView.findViewById(R.id.settings_menu_item_subtitle);
            arrowView = itemView.findViewById(R.id.settings_menu_item_arrow);
        }

        public void bind(SettingsMenuItem item) {
            // 设置图标
            iconView.setImageResource(item.getIconRes());
            
            // 设置标题
            titleView.setText(item.getTitle());
            
            // 设置副标题
            if (item.hasSubtitle()) {
                subtitleView.setText(item.getSubtitle());
                subtitleView.setVisibility(View.VISIBLE);
            } else {
                subtitleView.setVisibility(View.GONE);
            }
            
            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onItemClickListener != null) {
                    onItemClickListener.onSettingsMenuItemClick(item);
                }
            });
        }
    }
}
