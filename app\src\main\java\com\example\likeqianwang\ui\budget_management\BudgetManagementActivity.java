package com.example.likeqianwang.ui.budget_management;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.AdapterView;
import android.widget.ArrayAdapter;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.Budget;
import com.example.likeqianwang.R;
import com.example.likeqianwang.ViewModel.BudgetViewModel;
import com.example.likeqianwang.adapters.BudgetManagementAdapter;
import com.example.likeqianwang.ui.dialogs.BudgetEditBottomSheetDialogFragment;
import com.example.likeqianwang.ui.dialogs.YearMonthPickerBottomSheetDialogFragment;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

public class BudgetManagementActivity extends AppCompatActivity implements 
        BudgetManagementAdapter.OnBudgetItemClickListener,
        BudgetEditBottomSheetDialogFragment.OnBudgetSaveListener,
        YearMonthPickerBottomSheetDialogFragment.OnYearMonthSelectedListener {

    private static final String TAG = "BudgetManagement";

    // UI组件
    private ImageView backButton;
    private ImageView addButton;
    private Spinner periodSpinner;
    private LinearLayout yearMonthContainer;
    private TextView yearMonthTextView;
    private RecyclerView recyclerView;
    private LinearLayout emptyStateLayout;

    // ViewModel
    private BudgetViewModel budgetViewModel;

    // 适配器
    private BudgetManagementAdapter adapter;
    private List<Budget> budgetList = new ArrayList<>();

    // 当前选择的时间和周期
    private String currentPeriod = "MONTHLY";
    private int currentYear;
    private int currentMonth;

    // 周期选项
    private final String[] periodOptions = {"月度", "周度", "年度"};
    private final String[] periodValues = {"MONTHLY", "WEEKLY", "YEARLY"};

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_budget_management);

        initCurrentTime();
        initViews();
        initViewModel();
        setupSpinner();
        setupRecyclerView();
        setupClickListeners();
        loadBudgets();
    }

    private void initCurrentTime() {
        Calendar calendar = Calendar.getInstance();
        currentYear = calendar.get(Calendar.YEAR);
        currentMonth = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH is 0-based
    }

    private void initViews() {
        backButton = findViewById(R.id.budget_management_back);
        addButton = findViewById(R.id.budget_management_add);
        periodSpinner = findViewById(R.id.budget_management_period_spinner);
        yearMonthContainer = findViewById(R.id.budget_management_year_month_container);
        yearMonthTextView = findViewById(R.id.budget_management_year_month);
        recyclerView = findViewById(R.id.budget_management_recycler_view);
        emptyStateLayout = findViewById(R.id.budget_management_empty_state);

        updateYearMonthDisplay();
    }

    private void initViewModel() {
        budgetViewModel = new ViewModelProvider(this).get(BudgetViewModel.class);

        // 观察操作状态
        budgetViewModel.getOperationStatus().observe(this, status -> {
            if (status != null && !status.isEmpty()) {
                Toast.makeText(this, status, Toast.LENGTH_SHORT).show();
                loadBudgets(); // 重新加载数据
            }
        });

        // 观察错误信息
        budgetViewModel.getErrorMessage().observe(this, error -> {
            if (error != null && !error.isEmpty()) {
                Toast.makeText(this, error, Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void setupSpinner() {
        ArrayAdapter<String> adapter = new ArrayAdapter<>(this, 
                android.R.layout.simple_spinner_item, periodOptions);
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        periodSpinner.setAdapter(adapter);

        periodSpinner.setOnItemSelectedListener(new AdapterView.OnItemSelectedListener() {
            @Override
            public void onItemSelected(AdapterView<?> parent, View view, int position, long id) {
                currentPeriod = periodValues[position];
                loadBudgets();
            }

            @Override
            public void onNothingSelected(AdapterView<?> parent) {}
        });
    }

    private void setupRecyclerView() {
        adapter = new BudgetManagementAdapter(this, budgetList);
        adapter.setOnBudgetItemClickListener(this);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }

    private void setupClickListeners() {
        backButton.setOnClickListener(v -> finish());
        
        addButton.setOnClickListener(v -> showAddBudgetDialog());
        
        yearMonthContainer.setOnClickListener(v -> showYearMonthPicker());
    }

    private void loadBudgets() {
        budgetViewModel.getBudgetsByPeriod(currentPeriod, currentYear, currentMonth)
                .observe(this, budgets -> {
                    if (budgets != null) {
                        budgetList.clear();
                        budgetList.addAll(budgets);
                        adapter.notifyDataSetChanged();
                        
                        // 显示/隐藏空状态
                        if (budgets.isEmpty()) {
                            recyclerView.setVisibility(View.GONE);
                            emptyStateLayout.setVisibility(View.VISIBLE);
                        } else {
                            recyclerView.setVisibility(View.VISIBLE);
                            emptyStateLayout.setVisibility(View.GONE);
                        }
                    }
                });
    }

    private void showAddBudgetDialog() {
        BudgetEditBottomSheetDialogFragment dialog = 
                BudgetEditBottomSheetDialogFragment.newInstance(null, currentPeriod, currentYear, currentMonth);
        dialog.setOnBudgetSaveListener(this);
        dialog.show(getSupportFragmentManager(), "BudgetEditDialog");
    }

    private void showEditBudgetDialog(Budget budget) {
        BudgetEditBottomSheetDialogFragment dialog = 
                BudgetEditBottomSheetDialogFragment.newInstance(budget, currentPeriod, currentYear, currentMonth);
        dialog.setOnBudgetSaveListener(this);
        dialog.show(getSupportFragmentManager(), "BudgetEditDialog");
    }

    private void showYearMonthPicker() {
        YearMonthPickerBottomSheetDialogFragment dialog = 
                YearMonthPickerBottomSheetDialogFragment.newInstance(currentYear, currentMonth);
        dialog.setOnYearMonthSelectedListener(this);
        dialog.show(getSupportFragmentManager(), "YearMonthPicker");
    }

    private void updateYearMonthDisplay() {
        yearMonthTextView.setText(String.format("%d年%d月", currentYear, currentMonth));
    }

    @Override
    public void onBudgetItemClick(Budget budget) {
        showEditBudgetDialog(budget);
    }

    @Override
    public void onBudgetItemEdit(Budget budget) {
        showEditBudgetDialog(budget);
    }

    @Override
    public void onBudgetItemDelete(Budget budget) {
        budgetViewModel.deleteBudget(budget);
    }

    @Override
    public void onBudgetSaved(Budget budget, boolean isEdit) {
        if (isEdit) {
            budgetViewModel.updateBudget(budget);
        } else {
            budgetViewModel.insertBudget(budget);
        }
    }

    @Override
    public void onYearMonthSelected(int year, int month) {
        currentYear = year;
        currentMonth = month;
        updateYearMonthDisplay();
        loadBudgets();
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadBudgets();
    }
}
