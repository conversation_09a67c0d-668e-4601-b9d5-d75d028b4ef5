<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="12dp">

    <!-- 账户图标 -->
    <com.google.android.material.imageview.ShapeableImageView
        android:id="@+id/iv_recording_page_account_item_icon"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:padding="1dp"
        app:shapeAppearanceOverlay="@style/circleIconStyle"
        app:strokeColor="@color/YinBai"
        app:strokeWidth="1dp"
        tools:src="@drawable/icon_bank_icbc" />

    <!-- 账户信息 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_weight="1"
        android:orientation="vertical">

        <!-- 账户名称 -->
        <TextView
            android:id="@+id/tv_recording_page_account_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@color/black"
            tools:text="中国工商银行" />

        <!-- 账户类型 -->
        <TextView
            android:id="@+id/tv_recording_page_account_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="2dp"
            android:textSize="12sp"
            android:textColor="@color/grey"
            tools:text="储蓄卡" />

    </LinearLayout>

    <!-- 账户余额 -->
    <TextView
        android:id="@+id/tv_recording_page_account_balance"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/black"
        tools:text="¥5,000.00" />

</LinearLayout>
