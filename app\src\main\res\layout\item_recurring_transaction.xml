<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:background="?attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 第一行：图标、名称、金额、开关 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/recurring_transaction_type_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="12dp"
                android:contentDescription="@string/receipt_desc_类型图标"
                app:tint="@color/black"
                tools:src="@drawable/ic_income" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/recurring_transaction_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="工资收入" />

                <TextView
                    android:id="@+id/recurring_transaction_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textColor="@color/black"
                    android:textSize="12sp"
                    android:alpha="0.7"
                    tools:text="收入" />

            </LinearLayout>

            <TextView
                android:id="@+id/recurring_transaction_amount"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="12dp"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:textStyle="bold"
                tools:text="¥5,000.00" />

            <Switch
                android:id="@+id/recurring_transaction_active_switch"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                tools:checked="true" />

            <ImageView
                android:id="@+id/recurring_transaction_delete"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/receipt_删除"
                android:src="@drawable/ic_delete"
                app:tint="@color/black"
                android:alpha="0.6" />

        </LinearLayout>

        <!-- 第二行：下次执行时间 -->
        <TextView
            android:id="@+id/recurring_transaction_next_execution"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginStart="36dp"
            android:textColor="@color/black"
            android:textSize="14sp"
            android:alpha="0.8"
            tools:text="下次执行: 2024-01-01 09:00" />

        <!-- 第三行：重复信息 -->
        <TextView
            android:id="@+id/recurring_transaction_repeat_info"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginStart="36dp"
            android:textColor="@color/black"
            android:textSize="12sp"
            android:alpha="0.6"
            tools:text="每月 (已执行5次)" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
