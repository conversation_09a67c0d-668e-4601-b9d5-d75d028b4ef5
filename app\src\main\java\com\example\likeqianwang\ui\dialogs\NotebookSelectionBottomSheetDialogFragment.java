package com.example.likeqianwang.ui.dialogs;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.Notebook;
import com.example.likeqianwang.R;
import com.example.likeqianwang.ViewModel.NotebookViewModel;
import com.example.likeqianwang.adapters.NotebookSelectionAdapter;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.util.ArrayList;
import java.util.List;

public class NotebookSelectionBottomSheetDialogFragment extends BottomSheetDialogFragment implements 
        NotebookSelectionAdapter.OnNotebookSelectionListener {

    // UI组件
    private TextView titleTextView;
    private ImageView closeButton;
    private RecyclerView recyclerView;
    private LinearLayout emptyStateLayout;

    // ViewModel
    private NotebookViewModel viewModel;

    // 适配器
    private NotebookSelectionAdapter adapter;
    private List<Notebook> notebookList = new ArrayList<>();

    // 回调接口
    private OnNotebookSelectedListener listener;
    private String currentNotebookId;

    public interface OnNotebookSelectedListener {
        void onNotebookSelected(Notebook notebook);
    }

    public static NotebookSelectionBottomSheetDialogFragment newInstance(String currentNotebookId) {
        NotebookSelectionBottomSheetDialogFragment fragment = new NotebookSelectionBottomSheetDialogFragment();
        Bundle args = new Bundle();
        args.putString("current_notebook_id", currentNotebookId);
        fragment.setArguments(args);
        return fragment;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            setupFullHeight(bottomSheetDialog);
        });
        return dialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.layout_notebook_selection_bottom_sheet, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        parseArguments();
        initViews(view);
        initViewModel();
        setupRecyclerView();
        setupClickListeners();
        loadNotebooks();
    }

    private void parseArguments() {
        if (getArguments() != null) {
            currentNotebookId = getArguments().getString("current_notebook_id");
        }
    }

    private void initViews(View view) {
        titleTextView = view.findViewById(R.id.notebook_selection_title);
        closeButton = view.findViewById(R.id.notebook_selection_close);
        recyclerView = view.findViewById(R.id.notebook_selection_recycler_view);
        emptyStateLayout = view.findViewById(R.id.notebook_selection_empty_state);
    }

    private void initViewModel() {
        viewModel = new ViewModelProvider(this).get(NotebookViewModel.class);

        // 观察操作状态
        viewModel.getOperationStatus().observe(this, status -> {
            if (status != null && !status.isEmpty()) {
                Toast.makeText(getContext(), status, Toast.LENGTH_SHORT).show();
                if (status.contains("切换成功")) {
                    dismiss();
                }
            }
        });

        // 观察错误信息
        viewModel.getErrorMessage().observe(this, error -> {
            if (error != null && !error.isEmpty()) {
                Toast.makeText(getContext(), error, Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void setupRecyclerView() {
        adapter = new NotebookSelectionAdapter(requireContext(), notebookList, currentNotebookId);
        adapter.setOnNotebookSelectionListener(this);
        recyclerView.setLayoutManager(new LinearLayoutManager(requireContext()));
        recyclerView.setAdapter(adapter);
    }

    private void setupClickListeners() {
        closeButton.setOnClickListener(v -> dismiss());
    }

    private void loadNotebooks() {
        viewModel.getAllActiveNotebooks().observe(this, notebooks -> {
            if (notebooks != null) {
                notebookList.clear();
                notebookList.addAll(notebooks);
                adapter.notifyDataSetChanged();
                
                // 显示/隐藏空状态
                if (notebooks.isEmpty()) {
                    recyclerView.setVisibility(View.GONE);
                    emptyStateLayout.setVisibility(View.VISIBLE);
                } else {
                    recyclerView.setVisibility(View.VISIBLE);
                    emptyStateLayout.setVisibility(View.GONE);
                }
            }
        });
    }

    @Override
    public void onNotebookSelected(Notebook notebook) {
        // 切换到选中的账本
        viewModel.switchToNotebook(notebook.getNotebookId());
        
        // 通知外部监听器
        if (listener != null) {
            listener.onNotebookSelected(notebook);
        }
    }

    public void setOnNotebookSelectedListener(OnNotebookSelectedListener listener) {
        this.listener = listener;
    }

    private void setupFullHeight(BottomSheetDialog bottomSheetDialog) {
        try {
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                bottomSheet.getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
            }
        } catch (Exception e) {
            // 忽略错误
        }
    }
}
