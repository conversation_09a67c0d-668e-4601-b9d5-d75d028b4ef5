<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    tools:context=".ui.recurring_transactions.RecurringTransactionsActivity">

    <!-- 标题栏 -->
    <LinearLayout
        android:id="@+id/recurring_transactions_header"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="16dp">

        <ImageView
            android:id="@+id/recurring_transactions_back"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/receipt_返回"
            android:src="@drawable/ic_arrow_back"
            app:tint="@color/black" />

        <TextView
            android:id="@+id/recurring_transactions_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:text="@string/receipt_周期记账"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/recurring_transactions_search"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="搜索"
            android:src="@drawable/ic_search"
            app:tint="@color/black" />

    </LinearLayout>

    <!-- 搜索栏 -->
    <LinearLayout
        android:id="@+id/recurring_transactions_search_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="56dp"
        android:background="@color/white"
        android:elevation="2dp"
        android:orientation="horizontal"
        android:padding="16dp"
        android:visibility="gone">

        <EditText
            android:id="@+id/recurring_transactions_search_edit"
            android:layout_width="0dp"
            android:layout_height="40dp"
            android:layout_weight="1"
            android:background="@drawable/style_edittext_bg"
            android:hint="搜索周期记账名称..."
            android:imeOptions="actionSearch"
            android:inputType="text"
            android:paddingHorizontal="12dp"
            android:textSize="14sp" />

        <ImageView
            android:id="@+id/recurring_transactions_search_clear"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_marginStart="8dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="清除搜索"
            android:src="@drawable/ic_close"
            app:tint="@color/grey" />

    </LinearLayout>

    <!-- 主内容区域 -->
    <FrameLayout
        android:id="@+id/recurring_transactions_content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="56dp">

        <!-- 周期记账列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recurring_transactions_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingTop="8dp"
            android:paddingBottom="80dp"
            tools:listitem="@layout/item_recurring_transaction" />

        <!-- 空状态视图 -->
        <LinearLayout
            android:id="@+id/recurring_transactions_empty_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible">

            <ImageView
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:layout_marginBottom="16dp"
                android:alpha="0.3"
                android:src="@drawable/ic_recurring"
                app:tint="@color/black" />

            <TextView
                android:id="@+id/recurring_transactions_empty_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="暂无周期记账记录\n点击右下角按钮添加"
                android:textColor="@color/black"
                android:textSize="16sp"
                android:alpha="0.6" />

        </LinearLayout>

    </FrameLayout>

    <!-- 添加按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/recurring_transactions_fab_add"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="@string/receipt_新增周期记账"
        android:src="@drawable/icon_add"
        app:backgroundTint="@color/colorPrimary"
        app:tint="@color/white" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
