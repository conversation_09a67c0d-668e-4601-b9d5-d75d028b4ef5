package com.example.likeqianwang.ui.dialogs;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.NumberPicker;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.example.likeqianwang.R;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.util.Calendar;

public class YearMonthPickerBottomSheetDialogFragment extends BottomSheetDialogFragment {

    private static final String ARG_CURRENT_YEAR = "current_year";
    private static final String ARG_CURRENT_MONTH = "current_month";

    private NumberPicker yearPicker;
    private NumberPicker monthPicker;
    private TextView cancelButton;
    private TextView confirmButton;

    private int currentYear;
    private int currentMonth;
    private OnYearMonthSelectedListener listener;

    public interface OnYearMonthSelectedListener {
        void onYearMonthSelected(int year, int month);
    }

    public static YearMonthPickerBottomSheetDialogFragment newInstance(int currentYear, int currentMonth) {
        YearMonthPickerBottomSheetDialogFragment fragment = new YearMonthPickerBottomSheetDialogFragment();
        Bundle args = new Bundle();
        args.putInt(ARG_CURRENT_YEAR, currentYear);
        args.putInt(ARG_CURRENT_MONTH, currentMonth);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            currentYear = getArguments().getInt(ARG_CURRENT_YEAR);
            currentMonth = getArguments().getInt(ARG_CURRENT_MONTH);
        } else {
            // 默认使用当前年月
            Calendar calendar = Calendar.getInstance();
            currentYear = calendar.get(Calendar.YEAR);
            currentMonth = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH is 0-based
        }
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            setupFullHeight(bottomSheetDialog);
        });
        return dialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.layout_year_month_picker_bottom_sheet, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        setupYearPicker();
        setupMonthPicker();
        setupClickListeners();
    }

    private void initViews(View view) {
        yearPicker = view.findViewById(R.id.year_picker);
        monthPicker = view.findViewById(R.id.month_picker);
        cancelButton = view.findViewById(R.id.year_month_picker_cancel);
        confirmButton = view.findViewById(R.id.year_month_picker_confirm);
    }

    private void setupYearPicker() {
        Calendar calendar = Calendar.getInstance();
        int currentCalendarYear = calendar.get(Calendar.YEAR);
        
        // 设置年份范围：当前年份 ± 5年
        int minYear = currentCalendarYear - 5;
        int maxYear = currentCalendarYear + 5;
        
        yearPicker.setMinValue(minYear);
        yearPicker.setMaxValue(maxYear);
        yearPicker.setValue(currentYear);
        
        // 设置年份显示格式
        String[] yearValues = new String[maxYear - minYear + 1];
        for (int i = 0; i < yearValues.length; i++) {
            yearValues[i] = String.valueOf(minYear + i);
        }
        yearPicker.setDisplayedValues(yearValues);
        
        // 禁用循环滚动
        yearPicker.setWrapSelectorWheel(false);
    }

    private void setupMonthPicker() {
        // 设置月份范围：1-12
        monthPicker.setMinValue(1);
        monthPicker.setMaxValue(12);
        monthPicker.setValue(currentMonth);
        
        // 设置月份显示格式
        String[] monthValues = new String[12];
        for (int i = 0; i < 12; i++) {
            monthValues[i] = String.format("%02d月", i + 1);
        }
        monthPicker.setDisplayedValues(monthValues);
        
        // 禁用循环滚动
        monthPicker.setWrapSelectorWheel(false);
    }

    private void setupClickListeners() {
        cancelButton.setOnClickListener(v -> dismiss());
        
        confirmButton.setOnClickListener(v -> {
            int selectedYear = yearPicker.getValue();
            int selectedMonth = monthPicker.getValue();
            
            if (listener != null) {
                listener.onYearMonthSelected(selectedYear, selectedMonth);
            }
            dismiss();
        });
    }

    private void setupFullHeight(BottomSheetDialog bottomSheetDialog) {
        try {
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                bottomSheet.getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
            }
        } catch (Exception e) {
            // 忽略异常，使用默认行为
        }
    }

    public void setOnYearMonthSelectedListener(OnYearMonthSelectedListener listener) {
        this.listener = listener;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        listener = null;
    }
}
