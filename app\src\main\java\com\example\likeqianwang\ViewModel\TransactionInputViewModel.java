package com.example.likeqianwang.ViewModel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.Entity.Transactions;
import com.example.likeqianwang.Repository.TransactionRepository;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class TransactionInputViewModel extends AndroidViewModel {
    private final TransactionRepository repository;
    private final MutableLiveData<Boolean> saveStatus = new MutableLiveData<>();
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();
    private final MutableLiveData<Long> savedTransactionId = new MutableLiveData<>();
    private final ExecutorService executorService = Executors.newSingleThreadExecutor();

    public TransactionInputViewModel(@NonNull Application application) {
        super(application);
        this.repository = new TransactionRepository(AppDatabase.getInstance(application));
    }

    // 保存交易记录 - 新版本，支持TransactionTag列表
    public void saveTransaction(String type,
                                Date date,
                                BigDecimal amount,
                                String currencySymbol,
                                long categoryId,
                                String fromAccountId,
                                String toAccountId,  // 转账时使用
                                String remark,
                                List<TransactionTag> tags,
                                boolean includeInStats,
                                boolean includeInBudget) {
        executorService.execute(() -> {
            try {
                // 创建交易记录
                Transactions transaction = new Transactions();
                transaction.setType(type);
                transaction.setTransactionDate(date);
                transaction.setAmount(amount);
                transaction.setCurrencySymbol(currencySymbol);
                transaction.setCategoryId(categoryId);
                transaction.setFromAccountId(fromAccountId);
                transaction.setToAccountId(toAccountId);
                transaction.setRemark(remark);
                transaction.setIncludeInStats(includeInStats);
                transaction.setIncludeInBudget(includeInBudget);

                // 保存交易记录并获取ID
                long transactionId = repository.addTransaction(transaction, tags);

                // 通知保存成功
                saveStatus.postValue(true);
                savedTransactionId.postValue(transactionId);
            } catch (Exception e) {
                // 通知保存失败
                saveStatus.postValue(false);
                errorMessage.postValue(e.getMessage());
            }
        });
    }

    // 获取保存状态
    public LiveData<Boolean> getSaveStatus() {
        return saveStatus;
    }

    // 获取错误消息
    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    // 获取保存的交易记录ID
    public LiveData<Long> getSavedTransactionId() {
        return savedTransactionId;
    }

    // 清除状态
    public void clearStatus() {
        saveStatus.setValue(null);
        errorMessage.setValue(null);
        savedTransactionId.setValue(null);
    }

    // 直接保存已构建的交易记录
    public void saveTransaction(Transactions transaction, List<TransactionTag> tags) {
        executorService.execute(() -> {
            try {
                // 保存交易记录并获取ID
                long transactionId = repository.addTransaction(transaction, tags);

                // 通知保存成功
                saveStatus.postValue(true);
                savedTransactionId.postValue(transactionId);
            } catch (Exception e) {
                // 通知保存失败
                saveStatus.postValue(false);
                errorMessage.postValue(e.getMessage());
            }
        });
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
