package com.example.likeqianwang.ui.main_receipts;

import android.content.Intent;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.util.Log;
import android.widget.ImageButton;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.example.likeqianwang.Entity.Transactions;
import com.example.likeqianwang.R;
import com.example.likeqianwang.RecordingPageActivity;
import com.example.likeqianwang.ViewModel.ReceiptViewModel;
import com.example.likeqianwang.ViewModel.BudgetViewModel;
import com.example.likeqianwang.ui.budget_settings.BudgetSettingsActivity;
import com.example.likeqianwang.adapters.DailyTransactionListAdapter;
import com.example.likeqianwang.adapters.InOutBudgetViewPagerAdapter;
import com.example.likeqianwang.databinding.FragmentReceiptBinding;
import com.example.likeqianwang.ui.dialogs.YearMonthPickerBottomSheetDialogFragment;
import com.example.likeqianwang.ui.dialogs.TransactionDetailBottomSheetDialogFragment;
import com.example.likeqianwang.ui.dialogs.SettingsMenuBottomSheetDialogFragment;
import com.example.likeqianwang.widgets.Widget_Budget;
import com.example.likeqianwang.widgets.Widget_InAndOut;
import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

import java.util.ArrayList;
import java.util.List;

public class ReceiptsFragment extends Fragment implements View.OnClickListener {

    private FragmentReceiptBinding binding;
    private TabLayout InOut_Budget_tabLayout;
    private ViewPager2 InOut_Budget_viewPager;
    private final int[] InOut_Budget_tabIcons = {R.drawable.widget_icon_dot_combine, R.drawable.widget_icon_dot_combine};

    // 收支预算组件相关
    List<Fragment> InOut_Budget_fragments = new ArrayList<>();

    // 交易数据相关
    private ReceiptViewModel receiptViewModel;
    private BudgetViewModel budgetViewModel;
    private DailyTransactionListAdapter transactionListAdapter;
    private RecyclerView dailyTransactionRecyclerView;
    private TextView yearMonthTextView;
    private LinearLayout yearMonthContainer;


    public View onCreateView(@NonNull LayoutInflater inflater,
                             ViewGroup container, Bundle savedInstanceState) {

        binding = FragmentReceiptBinding.inflate(inflater, container, false);
        View root = binding.getRoot();

        // 初始化ViewModel
        receiptViewModel = new ViewModelProvider(this).get(ReceiptViewModel.class);
        budgetViewModel = new ViewModelProvider(this).get(BudgetViewModel.class);

        // 初始化UI组件
        initViews();

        // 设置收支预算组件
        setupInOutBudgetWidget();

        // 设置交易列表
        setupTransactionList();

        // 设置观察者
        setupObservers();

        // 设置点击事件
        setupClickListeners();

        return root;

    }

    @Override
    public void onResume() {
        super.onResume();
        // 每次Fragment恢复时刷新数据
        receiptViewModel.refreshData();
        // 清除预算ViewModel的状态，确保数据重新加载
        budgetViewModel.clearStatus();
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        InOut_Budget_tabLayout = binding.receiptInOutBudgetWidgetTabs;
        InOut_Budget_viewPager = binding.receiptInOutBudgetWidget;
        dailyTransactionRecyclerView = binding.receiptDailyInOutList;
        yearMonthTextView = binding.receiptYearMonth;
        yearMonthContainer = binding.receiptYearMonthContainer;
    }

    /**
     * 设置收支预算组件
     */
    private void setupInOutBudgetWidget() {
        InOut_Budget_fragments.add(new Widget_InAndOut());
        InOut_Budget_fragments.add(new Widget_Budget());

        InOutBudgetViewPagerAdapter inOut_budget_viewPager_adapter =
                new InOutBudgetViewPagerAdapter(getChildFragmentManager(), getLifecycle(), InOut_Budget_fragments);
        InOut_Budget_viewPager.setAdapter(inOut_budget_viewPager_adapter);

        new TabLayoutMediator(InOut_Budget_tabLayout, InOut_Budget_viewPager,
                new TabLayoutMediator.TabConfigurationStrategy() {
                    @Override
                    public void onConfigureTab(@NonNull TabLayout.Tab tab, int position) {
                        tab.setIcon(InOut_Budget_tabIcons[position]);
                    }
                }).attach();
    }

    /**
     * 设置交易列表
     */
    private void setupTransactionList() {
        // 设置RecyclerView
        dailyTransactionRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));

        // 创建适配器
        transactionListAdapter = new DailyTransactionListAdapter(getContext(), receiptViewModel);
        dailyTransactionRecyclerView.setAdapter(transactionListAdapter);

        // 设置交易点击事件
        transactionListAdapter.setOnTransactionClickListener(this::onTransactionClick);
    }

    /**
     * 设置数据观察者
     */
    private void setupObservers() {
        // 观察年月变化
        receiptViewModel.getCurrentYearMonthText().observe(getViewLifecycleOwner(), yearMonthText -> {
            if (yearMonthText != null) {
                yearMonthTextView.setText(yearMonthText);
            }
        });

        // 观察分组交易数据变化
        receiptViewModel.getGroupedTransactions().observe(getViewLifecycleOwner(), groupedTransactions -> {
            if (groupedTransactions != null) {
                transactionListAdapter.updateData(groupedTransactions);
            }
        });
    }

    /**
     * 设置点击事件监听器
     */
    private void setupClickListeners() {
        ImageButton add_record = binding.buttonAdd;
        ImageView receipt_setting = binding.receiptSetting;

        add_record.setTag("add_record");
        receipt_setting.setTag("receipt_setting");
        add_record.setOnClickListener(this);
        receipt_setting.setOnClickListener(this);

        // 年月点击事件
        yearMonthContainer.setOnClickListener(v -> showYearMonthPicker());
    }

    /**
     * 显示年月选择器
     */
    private void showYearMonthPicker() {
        try {
            int currentYear = receiptViewModel.getCurrentYear();
            int currentMonth = receiptViewModel.getCurrentMonth();

            YearMonthPickerBottomSheetDialogFragment picker =
                    YearMonthPickerBottomSheetDialogFragment.newInstance(currentYear, currentMonth);

            picker.setOnYearMonthSelectedListener((year, month) -> {
                Log.d("ReceiptsFragment", "Selected year: " + year + ", month: " + month);
                receiptViewModel.setYearMonth(year, month);
            });

            picker.show(getChildFragmentManager(), "YearMonthPicker");
        } catch (Exception e) {
            Log.e("ReceiptsFragment", "Error showing year month picker", e);
            Toast.makeText(getContext(), "打开日期选择器失败", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 显示设置菜单
     */
    private void showSettingsMenu() {
        try {
            SettingsMenuBottomSheetDialogFragment settingsMenu =
                    SettingsMenuBottomSheetDialogFragment.newInstance();
            settingsMenu.show(getChildFragmentManager(), "SettingsMenu");
        } catch (Exception e) {
            Log.e("ReceiptsFragment", "Error showing settings menu", e);
            Toast.makeText(getContext(), "打开设置菜单失败", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 处理交易项点击事件
     */
    private void onTransactionClick(Transactions transaction) {
        try {
            TransactionDetailBottomSheetDialogFragment detailDialog =
                    TransactionDetailBottomSheetDialogFragment.newInstance(transaction);
            detailDialog.show(getChildFragmentManager(), "TransactionDetail");
        } catch (Exception e) {
            Log.e("ReceiptsFragment", "Error showing transaction detail", e);
            Toast.makeText(getContext(), "打开交易详情失败", Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        binding = null;
    }

    @Override
    public void onClick(View v) {
        String receipt_tag = (String) v.getTag();
        switch (receipt_tag) {
            case "add_record":
                Intent start_add_record = new Intent(getActivity(), RecordingPageActivity.class);
                startActivity(start_add_record);
                break;
            case "receipt_setting":
                showSettingsMenu();
                break;
        }
    }
}