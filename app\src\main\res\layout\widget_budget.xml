<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="15dp"
    tools:context=".widgets.Widget_Budget">

    <!-- 剩余预算标题和金额 -->
    <TextView
        android:id="@+id/widget_budget_剩余预算"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/budget_剩余预算"
        android:textColor="@color/ChaHuaHong"
        android:textSize="18sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/widget_budget_剩余预算金额"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:text="￥1,000.00"
        android:textColor="@color/black"
        android:textSize="24sp"
        android:textStyle="bold" />

    <!-- 预算进度条 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:orientation="vertical">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/widget_budget_已用预算"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="@string/budget_已用预算"
                android:textColor="@color/black"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/widget_budget_使用百分比"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="60%"
                android:textColor="@color/black"
                android:textSize="12sp"
                android:textStyle="bold" />

        </LinearLayout>

        <ProgressBar
            android:id="@+id/widget_budget_进度条"
            style="?android:attr/progressBarStyleHorizontal"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:layout_marginTop="4dp"
            android:max="100"
            tools:progress="60"
            android:progressDrawable="@drawable/budget_remaining_progress_bar" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:orientation="horizontal">

            <TextView
                android:id="@+id/widget_budget_已用金额"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="￥1,500.00"
                android:textColor="@color/black"
                android:textSize="10sp" />

            <TextView
                android:id="@+id/widget_budget_总预算金额"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="￥2,500.00"
                android:textColor="@color/black"
                android:textSize="10sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 预算状态指示器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <View
            android:id="@+id/widget_budget_状态指示器"
            android:layout_width="8dp"
            android:layout_height="8dp"
            android:layout_marginEnd="6dp"
            android:background="@drawable/budget_status_indicator"
            android:backgroundTint="@color/HuaQing" />

        <TextView
            android:id="@+id/widget_budget_状态文本"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/budget_预算充足"
            android:textColor="@color/HuaQing"
            android:textSize="12sp"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>