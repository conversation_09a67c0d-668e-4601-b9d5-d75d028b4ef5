package com.example.likeqianwang.Repository;

import android.app.Application;

import androidx.lifecycle.LiveData;

import com.example.likeqianwang.Dao.BudgetDao;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.Budget;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class BudgetRepository {
    private final BudgetDao budgetDao;
    private final ExecutorService executor;

    public BudgetRepository(Application application) {
        AppDatabase database = AppDatabase.getInstance(application);
        budgetDao = database.budgetDao();
        executor = Executors.newFixedThreadPool(4);
    }

    public BudgetRepository(AppDatabase database) {
        budgetDao = database.budgetDao();
        executor = Executors.newFixedThreadPool(4);
    }

    // 插入或更新预算
    public void insertOrUpdateBudget(Budget budget, BudgetOperationCallback callback) {
        executor.execute(() -> {
            try {
                budget.setUpdateTime(new java.util.Date());
                long result = budgetDao.insert(budget);
                if (callback != null) {
                    callback.onSuccess(result);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError(e.getMessage());
                }
            }
        });
    }

    // 删除预算
    public void deleteBudget(Budget budget, BudgetOperationCallback callback) {
        executor.execute(() -> {
            try {
                budgetDao.delete(budget);
                if (callback != null) {
                    callback.onSuccess(0);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError(e.getMessage());
                }
            }
        });
    }

    // 获取当前月份的所有预算
    public LiveData<List<Budget>> getCurrentMonthBudgets() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH is 0-based
        return budgetDao.getBudgetsByPeriod("MONTHLY", year, month);
    }

    // 获取指定时期的预算
    public LiveData<List<Budget>> getBudgetsByPeriod(String period, int year, int month) {
        return budgetDao.getBudgetsByPeriod(period, year, month);
    }

    // 获取总预算
    public LiveData<Budget> getTotalBudget(String period, int year, int month) {
        return budgetDao.getTotalBudget(period, year, month);
    }

    // 获取当前月份的总预算
    public LiveData<Budget> getCurrentMonthTotalBudget() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        return budgetDao.getTotalBudget("MONTHLY", year, month);
    }

    // 获取分类预算
    public LiveData<Budget> getCategoryBudget(long categoryId, String period, int year, int month) {
        return budgetDao.getCategoryBudget(categoryId, period, year, month);
    }

    // 获取所有分类预算
    public LiveData<List<Budget>> getAllCategoryBudgets(String period, int year, int month) {
        return budgetDao.getAllCategoryBudgets(period, year, month);
    }

    // 获取当前月份的所有分类预算
    public LiveData<List<Budget>> getCurrentMonthCategoryBudgets() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        return budgetDao.getAllCategoryBudgets("MONTHLY", year, month);
    }

    // 获取预算使用情况
    public LiveData<List<BudgetDao.BudgetUsage>> getBudgetUsage(String period, int year, int month) {
        return budgetDao.getBudgetUsageByPeriod(period, year, month);
    }

    // 获取当前月份的预算使用情况
    public LiveData<List<BudgetDao.BudgetUsage>> getCurrentMonthBudgetUsage() {
        Calendar calendar = Calendar.getInstance();
        int year = calendar.get(Calendar.YEAR);
        int month = calendar.get(Calendar.MONTH) + 1;
        return budgetDao.getBudgetUsageByPeriod("MONTHLY", year, month);
    }

    // 检查预算是否存在
    public void checkBudgetExists(String budgetType, Long categoryId, Long subcategoryId, 
                                  String period, int year, int month, BudgetExistsCallback callback) {
        executor.execute(() -> {
            try {
                int count = budgetDao.checkBudgetExists(budgetType, categoryId, subcategoryId, period, year, month);
                if (callback != null) {
                    callback.onResult(count > 0);
                }
            } catch (Exception e) {
                if (callback != null) {
                    callback.onError(e.getMessage());
                }
            }
        });
    }

    // 创建总预算
    public void createTotalBudget(BigDecimal amount, String period, int year, int month, 
                                  double alertThreshold, BudgetOperationCallback callback) {
        Budget budget = new Budget();
        budget.setBudgetType("TOTAL");
        budget.setBudgetAmount(amount);
        budget.setBudgetPeriod(period);
        budget.setBudgetYear(year);
        budget.setBudgetMonth(month);
        budget.setAlertThreshold(alertThreshold);
        
        insertOrUpdateBudget(budget, callback);
    }

    // 创建分类预算
    public void createCategoryBudget(long categoryId, BigDecimal amount, String period, 
                                     int year, int month, double alertThreshold, BudgetOperationCallback callback) {
        Budget budget = new Budget();
        budget.setBudgetType("CATEGORY");
        budget.setCategoryId(categoryId);
        budget.setBudgetAmount(amount);
        budget.setBudgetPeriod(period);
        budget.setBudgetYear(year);
        budget.setBudgetMonth(month);
        budget.setAlertThreshold(alertThreshold);
        
        insertOrUpdateBudget(budget, callback);
    }

    // 获取所有预算
    public LiveData<List<Budget>> getAllBudgets() {
        return budgetDao.getAllBudgets();
    }

    // 同步方法（用于后台计算）
    public List<Budget> getBudgetsByPeriodSync(String period, int year, int month) {
        return budgetDao.getBudgetsByPeriodSync(period, year, month);
    }

    public Budget getTotalBudgetSync(String period, int year, int month) {
        return budgetDao.getTotalBudgetSync(period, year, month);
    }

    public List<BudgetDao.BudgetUsage> getBudgetUsageSync(String period, int year, int month) {
        return budgetDao.getBudgetUsageByPeriodSync(period, year, month);
    }

    // 回调接口
    public interface BudgetOperationCallback {
        void onSuccess(long result);
        void onError(String error);
    }

    public interface BudgetExistsCallback {
        void onResult(boolean exists);
        void onError(String error);
    }

    // 清理资源
    public void cleanup() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
