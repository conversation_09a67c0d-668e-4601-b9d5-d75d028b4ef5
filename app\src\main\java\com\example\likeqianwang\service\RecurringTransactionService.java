package com.example.likeqianwang.service;

import android.app.AlarmManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.IBinder;
import android.util.Log;

import androidx.annotation.Nullable;

import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Repository.RecurringTransactionRepository;

import java.util.Calendar;

public class RecurringTransactionService extends Service {
    
    private static final String TAG = "RecurringTransactionService";
    private static final String ACTION_EXECUTE_RECURRING_TRANSACTIONS = "com.example.likeqianwang.EXECUTE_RECURRING_TRANSACTIONS";
    private static final int ALARM_REQUEST_CODE = 1001;
    
    private RecurringTransactionRepository repository;

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "RecurringTransactionService created");
        
        AppDatabase database = AppDatabase.getInstance(this);
        repository = new RecurringTransactionRepository(database);
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "RecurringTransactionService started");
        
        if (intent != null && ACTION_EXECUTE_RECURRING_TRANSACTIONS.equals(intent.getAction())) {
            executeRecurringTransactions();
        }
        
        // 设置下次执行的闹钟
        scheduleNextExecution();
        
        return START_STICKY; // 服务被杀死后会重启
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null; // 不支持绑定
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "RecurringTransactionService destroyed");
    }

    /**
     * 执行到期的周期记账
     */
    private void executeRecurringTransactions() {
        Log.d(TAG, "Executing recurring transactions...");
        
        repository.executeRecurringTransactions(new RecurringTransactionRepository.RecurringTransactionExecutionCallback() {
            @Override
            public void onExecutionCompleted(int successCount, int failCount) {
                Log.d(TAG, "Recurring transaction execution completed. Success: " + successCount + ", Failed: " + failCount);
                
                if (successCount > 0) {
                    // 可以在这里发送通知告知用户有新的交易记录被创建
                    Log.i(TAG, "Created " + successCount + " new transactions from recurring transactions");
                }
                
                if (failCount > 0) {
                    Log.w(TAG, "Failed to create " + failCount + " transactions from recurring transactions");
                }
                
                // 执行完成后停止服务
                stopSelf();
            }
        });
    }

    /**
     * 安排下次执行
     */
    private void scheduleNextExecution() {
        AlarmManager alarmManager = (AlarmManager) getSystemService(Context.ALARM_SERVICE);
        if (alarmManager == null) {
            Log.e(TAG, "AlarmManager is null, cannot schedule next execution");
            return;
        }

        Intent intent = new Intent(this, RecurringTransactionService.class);
        intent.setAction(ACTION_EXECUTE_RECURRING_TRANSACTIONS);
        
        PendingIntent pendingIntent = PendingIntent.getService(
                this, 
                ALARM_REQUEST_CODE, 
                intent, 
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        // 设置下次执行时间为明天的同一时间
        Calendar nextExecution = Calendar.getInstance();
        nextExecution.add(Calendar.DAY_OF_MONTH, 1);
        
        long nextExecutionTime = nextExecution.getTimeInMillis();
        
        Log.d(TAG, "Scheduling next execution at: " + nextExecution.getTime());
        
        // 使用setExactAndAllowWhileIdle确保在设备休眠时也能执行
        alarmManager.setExactAndAllowWhileIdle(
                AlarmManager.RTC_WAKEUP,
                nextExecutionTime,
                pendingIntent
        );
    }

    /**
     * 启动周期记账服务
     */
    public static void startRecurringTransactionService(Context context) {
        Intent intent = new Intent(context, RecurringTransactionService.class);
        intent.setAction(ACTION_EXECUTE_RECURRING_TRANSACTIONS);
        context.startService(intent);
        
        Log.d(TAG, "RecurringTransactionService start requested");
    }

    /**
     * 停止周期记账服务
     */
    public static void stopRecurringTransactionService(Context context) {
        Intent intent = new Intent(context, RecurringTransactionService.class);
        context.stopService(intent);
        
        // 取消闹钟
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager != null) {
            Intent alarmIntent = new Intent(context, RecurringTransactionService.class);
            alarmIntent.setAction(ACTION_EXECUTE_RECURRING_TRANSACTIONS);
            
            PendingIntent pendingIntent = PendingIntent.getService(
                    context, 
                    ALARM_REQUEST_CODE, 
                    alarmIntent, 
                    PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
            );
            
            alarmManager.cancel(pendingIntent);
            Log.d(TAG, "RecurringTransactionService alarm cancelled");
        }
        
        Log.d(TAG, "RecurringTransactionService stop requested");
    }

    /**
     * 设置定期执行的闹钟（应用启动时调用）
     */
    public static void scheduleRecurringTransactionService(Context context) {
        AlarmManager alarmManager = (AlarmManager) context.getSystemService(Context.ALARM_SERVICE);
        if (alarmManager == null) {
            Log.e(TAG, "AlarmManager is null, cannot schedule recurring transaction service");
            return;
        }

        Intent intent = new Intent(context, RecurringTransactionService.class);
        intent.setAction(ACTION_EXECUTE_RECURRING_TRANSACTIONS);
        
        PendingIntent pendingIntent = PendingIntent.getService(
                context, 
                ALARM_REQUEST_CODE, 
                intent, 
                PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );

        // 设置每天执行一次，从明天开始
        Calendar firstExecution = Calendar.getInstance();
        firstExecution.add(Calendar.DAY_OF_MONTH, 1);
        firstExecution.set(Calendar.HOUR_OF_DAY, 9); // 每天上午9点执行
        firstExecution.set(Calendar.MINUTE, 0);
        firstExecution.set(Calendar.SECOND, 0);
        firstExecution.set(Calendar.MILLISECOND, 0);
        
        long firstExecutionTime = firstExecution.getTimeInMillis();
        long intervalMillis = AlarmManager.INTERVAL_DAY; // 24小时间隔
        
        Log.d(TAG, "Scheduling recurring transaction service to start at: " + firstExecution.getTime());
        
        // 使用setRepeating设置重复闹钟
        alarmManager.setRepeating(
                AlarmManager.RTC_WAKEUP,
                firstExecutionTime,
                intervalMillis,
                pendingIntent
        );
    }
}
