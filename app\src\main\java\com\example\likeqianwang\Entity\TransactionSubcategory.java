package com.example.likeqianwang.Entity;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;

@Entity(tableName = "transaction_subcategories",
        foreignKeys = {@ForeignKey(entity = TransactionCategory.class, parentColumns = "categoryId", childColumns = "parentCategoryId", onDelete = ForeignKey.CASCADE)},
        indices = {@Index("parentCategoryId")})
public class TransactionSubcategory {
    @PrimaryKey(autoGenerate = true)
    private long subcategoryId;

    @ColumnInfo
    private String subcategoryName;

    @ColumnInfo
    private int subcategoryIcon;

    @ColumnInfo
    private long parentCategoryId;

    @ColumnInfo
    private int orderIndex;

    public TransactionSubcategory(String subcategoryName, int subcategoryIcon, long parentCategoryId, int orderIndex) {
        this.subcategoryName = subcategoryName;
        this.subcategoryIcon = subcategoryIcon;
        this.parentCategoryId = parentCategoryId;
        this.orderIndex = orderIndex;
    }

    public long getSubcategoryId() {
        return subcategoryId;
    }

    public void setSubcategoryId(long subcategoryId) {
        this.subcategoryId = subcategoryId;
    }

    public String getSubcategoryName() {
        return subcategoryName;
    }

    public void setSubcategoryName(String subcategoryName) {
        this.subcategoryName = subcategoryName;
    }

    public int getSubcategoryIcon() {
        return subcategoryIcon;
    }

    public void setSubcategoryIcon(int subcategoryIcon) {
        this.subcategoryIcon = subcategoryIcon;
    }

    public long getParentCategoryId() {
        return parentCategoryId;
    }

    public void setParentCategoryId(long parentCategoryId) {
        this.parentCategoryId = parentCategoryId;
    }

    public int getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(int orderIndex) {
        this.orderIndex = orderIndex;
    }
}
