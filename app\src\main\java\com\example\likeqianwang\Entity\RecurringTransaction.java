package com.example.likeqianwang.Entity;

import androidx.annotation.NonNull;
import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;
import androidx.room.PrimaryKey;
import androidx.room.TypeConverters;

import com.example.likeqianwang.Utils.Converters;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

@Entity(
        tableName = "recurring_transactions",
        indices = {
                @Index(value = "nextExecutionDate"),
                @Index(value = "type"),
                @Index(value = "categoryId"),
                @Index(value = "fromAccountId"),
                @Index(value = "toAccountId"),
                @Index(value = "isActive")
        },
        foreignKeys = {
                @ForeignKey(
                        entity = TransactionCategory.class,
                        parentColumns = "categoryId",
                        childColumns = "categoryId",
                        onDelete = ForeignKey.CASCADE),
                @ForeignKey(
                        entity = Account.class,
                        parentColumns = "accountId",
                        childColumns = "fromAccountId",
                        onDelete = ForeignKey.CASCADE)
        }
)
@TypeConverters(Converters.class)
public class RecurringTransaction implements Serializable {
    @PrimaryKey
    @NonNull
    private String recurringId;

    // 记账基本信息
    @ColumnInfo(name = "name")
    private String name;  // 周期记账名称

    @ColumnInfo(name = "type")
    private String type;  // INCOME, EXPENSE, TRANSFER

    @ColumnInfo(name = "categoryId")
    private long categoryId;  // 收支类型

    @ColumnInfo(name = "amount")
    private BigDecimal amount;  // 金额

    @ColumnInfo(name = "currencySymbol")
    private String currencySymbol;

    @ColumnInfo(name = "fromAccountId")
    private String fromAccountId; // 收支账户 & 转账转出账户

    @ColumnInfo(name = "toAccountId")
    private String toAccountId; // 转入账户（仅转账时使用）

    @ColumnInfo(name = "include_in_stats")
    private boolean includeInStats;  // 是否计入收支

    @ColumnInfo(name = "include_in_budget")
    private boolean includeInBudget;  // 是否计入预算

    @ColumnInfo(name = "remark")
    private String remark;  // 备注

    // 时间设置
    @ColumnInfo(name = "startDate")
    private Date startDate;  // 开始时间

    @ColumnInfo(name = "endDate")
    private Date endDate;  // 结束时间（可选）

    @ColumnInfo(name = "nextExecutionDate")
    private Date nextExecutionDate;  // 下次执行时间

    // 重复周期设置
    @ColumnInfo(name = "repeatType")
    private String repeatType;  // DAILY, WEEKLY, MONTHLY, YEARLY

    @ColumnInfo(name = "repeatInterval")
    private int repeatInterval;  // 重复间隔（如每2天、每3周等）

    @ColumnInfo(name = "repeatDayOfWeek")
    private Integer repeatDayOfWeek;  // 每周的第几天（1-7，周一到周日）

    @ColumnInfo(name = "repeatDayOfMonth")
    private Integer repeatDayOfMonth;  // 每月的第几天（1-31）

    @ColumnInfo(name = "repeatMonthOfYear")
    private Integer repeatMonthOfYear;  // 每年的第几月（1-12）

    // 状态信息
    @ColumnInfo(name = "isActive")
    private boolean isActive;  // 是否启用

    @ColumnInfo(name = "lastExecutionDate")
    private Date lastExecutionDate;  // 最后执行时间

    @ColumnInfo(name = "executionCount")
    private int executionCount;  // 已执行次数

    // 记录信息
    @ColumnInfo(name = "createTime")
    private Date createTime;  // 创建时间

    @ColumnInfo(name = "updateTime")
    private Date updateTime;  // 更新时间

    public RecurringTransaction() {
        this.recurringId = UUID.randomUUID().toString();
        this.createTime = new Date();
        this.updateTime = new Date();
        this.currencySymbol = "CNY";
        this.includeInStats = true;
        this.includeInBudget = true;
        this.isActive = true;
        this.repeatInterval = 1;
        this.executionCount = 0;
    }

    @NonNull
    public String getRecurringId() {
        return recurringId;
    }

    public void setRecurringId(@NonNull String recurringId) {
        this.recurringId = recurringId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(long categoryId) {
        this.categoryId = categoryId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getCurrencySymbol() {
        return currencySymbol;
    }

    public void setCurrencySymbol(String currencySymbol) {
        this.currencySymbol = currencySymbol;
    }

    public String getFromAccountId() {
        return fromAccountId;
    }

    public void setFromAccountId(String fromAccountId) {
        this.fromAccountId = fromAccountId;
    }

    public String getToAccountId() {
        return toAccountId;
    }

    public void setToAccountId(String toAccountId) {
        this.toAccountId = toAccountId;
    }

    public boolean isIncludeInStats() {
        return includeInStats;
    }

    public void setIncludeInStats(boolean includeInStats) {
        this.includeInStats = includeInStats;
    }

    public boolean isIncludeInBudget() {
        return includeInBudget;
    }

    public void setIncludeInBudget(boolean includeInBudget) {
        this.includeInBudget = includeInBudget;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Date getNextExecutionDate() {
        return nextExecutionDate;
    }

    public void setNextExecutionDate(Date nextExecutionDate) {
        this.nextExecutionDate = nextExecutionDate;
    }

    public String getRepeatType() {
        return repeatType;
    }

    public void setRepeatType(String repeatType) {
        this.repeatType = repeatType;
    }

    public int getRepeatInterval() {
        return repeatInterval;
    }

    public void setRepeatInterval(int repeatInterval) {
        this.repeatInterval = repeatInterval;
    }

    public Integer getRepeatDayOfWeek() {
        return repeatDayOfWeek;
    }

    public void setRepeatDayOfWeek(Integer repeatDayOfWeek) {
        this.repeatDayOfWeek = repeatDayOfWeek;
    }

    public Integer getRepeatDayOfMonth() {
        return repeatDayOfMonth;
    }

    public void setRepeatDayOfMonth(Integer repeatDayOfMonth) {
        this.repeatDayOfMonth = repeatDayOfMonth;
    }

    public Integer getRepeatMonthOfYear() {
        return repeatMonthOfYear;
    }

    public void setRepeatMonthOfYear(Integer repeatMonthOfYear) {
        this.repeatMonthOfYear = repeatMonthOfYear;
    }

    public boolean isActive() {
        return isActive;
    }

    public void setActive(boolean active) {
        isActive = active;
    }

    public Date getLastExecutionDate() {
        return lastExecutionDate;
    }

    public void setLastExecutionDate(Date lastExecutionDate) {
        this.lastExecutionDate = lastExecutionDate;
    }

    public int getExecutionCount() {
        return executionCount;
    }

    public void setExecutionCount(int executionCount) {
        this.executionCount = executionCount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
