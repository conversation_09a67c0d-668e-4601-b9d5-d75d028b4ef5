package com.example.likeqianwang.ViewModel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;

import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.RecurringTransaction;
import com.example.likeqianwang.Repository.RecurringTransactionRepository;

import java.util.List;

public class RecurringTransactionViewModel extends AndroidViewModel {

    private final RecurringTransactionRepository repository;

    public RecurringTransactionViewModel(@NonNull Application application) {
        super(application);
        AppDatabase database = AppDatabase.getDatabase(application);
        repository = new RecurringTransactionRepository(database);
    }

    /**
     * 获取所有周期记账记录
     */
    public LiveData<List<RecurringTransaction>> getAllRecurringTransactions() {
        return repository.getAllRecurringTransactions();
    }

    /**
     * 获取启用的周期记账记录
     */
    public LiveData<List<RecurringTransaction>> getActiveRecurringTransactions() {
        return repository.getActiveRecurringTransactions();
    }

    /**
     * 根据ID获取周期记账记录
     */
    public RecurringTransaction getRecurringTransactionById(String recurringId) {
        return repository.getRecurringTransactionById(recurringId);
    }

    /**
     * 插入周期记账记录
     */
    public void insertRecurringTransaction(RecurringTransaction recurringTransaction, 
                                         RecurringTransactionOperationCallback callback) {
        repository.insertRecurringTransaction(recurringTransaction, 
                new RecurringTransactionRepository.RecurringTransactionOperationCallback() {
                    @Override
                    public void onSuccess() {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }

                    @Override
                    public void onError(String error) {
                        if (callback != null) {
                            callback.onError(error);
                        }
                    }
                });
    }

    /**
     * 更新周期记账记录
     */
    public void updateRecurringTransaction(RecurringTransaction recurringTransaction, 
                                         RecurringTransactionOperationCallback callback) {
        repository.updateRecurringTransaction(recurringTransaction, 
                new RecurringTransactionRepository.RecurringTransactionOperationCallback() {
                    @Override
                    public void onSuccess() {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }

                    @Override
                    public void onError(String error) {
                        if (callback != null) {
                            callback.onError(error);
                        }
                    }
                });
    }

    /**
     * 删除周期记账记录
     */
    public void deleteRecurringTransaction(RecurringTransaction recurringTransaction, 
                                         RecurringTransactionOperationCallback callback) {
        repository.deleteRecurringTransaction(recurringTransaction, 
                new RecurringTransactionRepository.RecurringTransactionOperationCallback() {
                    @Override
                    public void onSuccess() {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }

                    @Override
                    public void onError(String error) {
                        if (callback != null) {
                            callback.onError(error);
                        }
                    }
                });
    }

    /**
     * 启用/禁用周期记账
     */
    public void updateActiveStatus(String recurringId, boolean isActive, 
                                 RecurringTransactionOperationCallback callback) {
        repository.updateActiveStatus(recurringId, isActive, 
                new RecurringTransactionRepository.RecurringTransactionOperationCallback() {
                    @Override
                    public void onSuccess() {
                        if (callback != null) {
                            callback.onSuccess();
                        }
                    }

                    @Override
                    public void onError(String error) {
                        if (callback != null) {
                            callback.onError(error);
                        }
                    }
                });
    }

    /**
     * 执行到期的周期记账
     */
    public void executeRecurringTransactions(RecurringTransactionExecutionCallback callback) {
        repository.executeRecurringTransactions(
                new RecurringTransactionRepository.RecurringTransactionExecutionCallback() {
                    @Override
                    public void onExecutionCompleted(int successCount, int failCount) {
                        if (callback != null) {
                            callback.onExecutionCompleted(successCount, failCount);
                        }
                    }
                });
    }

    // 回调接口
    public interface RecurringTransactionOperationCallback {
        void onSuccess();
        void onError(String error);
    }

    public interface RecurringTransactionExecutionCallback {
        void onExecutionCompleted(int successCount, int failCount);
    }
}
