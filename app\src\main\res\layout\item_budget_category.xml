<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:padding="12dp">

    <ImageView
        android:id="@+id/budget_category_icon"
        android:layout_width="32dp"
        android:layout_height="32dp"
        android:layout_marginEnd="12dp"
        android:contentDescription="@string/budget_desc_分类图标"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@drawable/ic_category_food" />

    <TextView
        android:id="@+id/budget_category_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginEnd="12dp"
        android:textColor="@color/black"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintBottom_toTopOf="@id/budget_category_amount_layout"
        app:layout_constraintEnd_toStartOf="@id/budget_category_delete"
        app:layout_constraintStart_toEndOf="@id/budget_category_icon"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        tools:text="餐饮" />

    <LinearLayout
        android:id="@+id/budget_category_amount_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="4dp"
        android:layout_marginEnd="12dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/budget_category_delete"
        app:layout_constraintStart_toEndOf="@id/budget_category_icon"
        app:layout_constraintTop_toBottomOf="@id/budget_category_name">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="￥"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <EditText
            android:id="@+id/budget_category_amount"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="@null"
            android:hint="@string/budget_请输入预算金额"
            android:inputType="numberDecimal"
            android:padding="4dp"
            android:textColor="@color/black"
            android:textSize="14sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/budget_预警"
            android:textColor="@color/black"
            android:textSize="12sp" />

        <EditText
            android:id="@+id/budget_category_alert_threshold"
            android:layout_width="40dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:background="@null"
            android:hint="80"
            android:inputType="number"
            android:padding="4dp"
            android:textColor="@color/black"
            android:textSize="12sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="%"
            android:textColor="@color/black"
            android:textSize="12sp" />

    </LinearLayout>

    <ImageView
        android:id="@+id/budget_category_delete"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="@string/budget_desc_删除分类预算"
        android:src="@drawable/ic_delete"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:tint="@color/ChaHuaHong" />

</androidx.constraintlayout.widget.ConstraintLayout>
