package com.example.likeqianwang.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.Account;
import com.example.likeqianwang.Utils.CurrencyFormatter;
import com.example.likeqianwang.databinding.StyleAccountItemInWalletsBinding;
import com.google.android.material.imageview.ShapeableImageView;

import java.util.Calendar;
import java.util.List;

public class AccountItemInWalletAdapter extends RecyclerView.Adapter<AccountItemInWalletAdapter.AccountViewHolder> {
    private final Context context;
    private final List<Account> accounts;
    private final AccountCategoryInWalletAdapter.OnAccountClickListener clickListener;
    private OnAccountDeleteListener deleteListener;
    private int openItemPosition = RecyclerView.NO_POSITION;

    // 定义删除监听器接口
    public interface OnAccountDeleteListener {
        void onAccountDelete(Account account);
    }

    public AccountItemInWalletAdapter(Context context, List<Account> accounts,
                                      AccountCategoryInWalletAdapter.OnAccountClickListener clickListener, OnAccountDeleteListener deleteListener) {
        this.context = context;
        this.accounts = accounts;
        this.clickListener = clickListener;
        this.deleteListener = deleteListener;
    }

    @NonNull
    @Override
    public AccountViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        StyleAccountItemInWalletsBinding accountItemInWalletsBinding = StyleAccountItemInWalletsBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new AccountViewHolder(accountItemInWalletsBinding);
    }

    @Override
    public void onBindViewHolder(@NonNull AccountViewHolder holder, int position) {
        Account account = accounts.get(position);

        // 设置账户图标
        if (account.getBankIcon() != 0) {
            holder.accountIcon.setImageResource(account.getBankIcon());
        } else if (account.getAccountTypeIcon() != 0) {
            holder.accountIcon.setImageResource(account.getAccountTypeIcon());
        }

        // 设置账户名称
        holder.accountName.setText(account.getAccountName());

        // 设置账户备注（如果有）
        if (account.getAccountRemark() != null && !account.getAccountRemark().isEmpty()) {
            holder.accountRemark.setVisibility(View.VISIBLE);
            holder.accountRemark.setText(account.getAccountRemark());
        } else {
            holder.accountRemark.setVisibility(View.GONE);
        }

        // 设置是否计入总资产标签
        holder.includeInAsset.setVisibility(account.isIncludeInAsset() ? View.GONE : View.VISIBLE);

        // 根据账户类型设置不同的显示内容
        if (account.getAccountTypeDebitCredit() == 1) {
            // 借记卡/储蓄卡，显示余额
            holder.accountBalance.setText(CurrencyFormatter.format(account.getAccountBalance()));
            // 隐藏信用卡特有的视图
            holder.accountDueTimeRemained.setVisibility(View.GONE);
            holder.accountCreditRemained.setVisibility(View.GONE);
        } else {
            // 信用卡，显示欠款（负值）
            holder.accountBalance.setText(CurrencyFormatter.format(-account.getAccountBalance()));

            // 显示剩余可用额度
            double availableCredit = account.getTotalCredit() - account.getAccountBalance();
            holder.accountCreditRemained.setVisibility(View.VISIBLE);
            holder.accountCreditRemained.setText("可用额度：%s".formatted(CurrencyFormatter.format(availableCredit)));

            // 计算并显示还款日剩余天数
            if (account.getDueDate() > 0) {
                int daysUntilDue = calculateDaysUntilDueDate(account.getDueDate());
                holder.accountDueTimeRemained.setVisibility(View.VISIBLE);

                if (daysUntilDue == 0) {
                    holder.accountDueTimeRemained.setText("今天还款");
                } else if (daysUntilDue < 0) {
                    holder.accountDueTimeRemained.setText("已逾期%d天".formatted(Math.abs(daysUntilDue)));
                } else {
                    holder.accountDueTimeRemained.setText("%d天后还款".formatted(daysUntilDue));
                }
            } else {
                holder.accountDueTimeRemained.setVisibility(View.GONE);
            }
        }

        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            if (clickListener != null) {
                clickListener.onAccountClick(account.getAccountId());
            }
        });

        // 重置项目的平移状态
        holder.itemView.setTranslationX(0f);
    }

    /**
     * 计算距离下一个还款日的天数
     *
     * @param dueDay 每月的还款日
     * @return 距离下一个还款日的天数
     */
    private int calculateDaysUntilDueDate(int dueDay) {
        Calendar today = Calendar.getInstance();
        Calendar dueDate = Calendar.getInstance();

        // 设置还款日
        dueDate.set(Calendar.DAY_OF_MONTH, dueDay);

        // 如果当月的还款日已过，计算下个月的还款日
        if (today.get(Calendar.DAY_OF_MONTH) > dueDay) {
            dueDate.add(Calendar.MONTH, 1);
        }

        // 计算相差的天数
        long diffMillis = dueDate.getTimeInMillis() - today.getTimeInMillis();
        return (int) (diffMillis / (24 * 60 * 60 * 1000));
    }

    @Override
    public int getItemCount() {
        return accounts.size();
    }

    // 获取当前打开的项目位置
    public int getOpenPosition() {
        return openItemPosition;
    }

    // 设置当前打开的项目位置
    public void setOpenPosition(int position) {
        this.openItemPosition = position;
    }

    // 关闭指定位置的项目
    public void closeItem(int position, RecyclerView recyclerView) {
        if (position != RecyclerView.NO_POSITION && position < accounts.size()) {
            RecyclerView.ViewHolder viewHolder = recyclerView.findViewHolderForAdapterPosition(position);
            if (viewHolder != null) {
                viewHolder.itemView.animate().translationX(0).setDuration(200).start();
            }
            if (openItemPosition == position) {
                openItemPosition = RecyclerView.NO_POSITION;
            }
        }
    }

    // 关闭当前打开的项目
    public void closeOpenItem(RecyclerView recyclerView) {
        closeItem(openItemPosition, recyclerView);
    }

    public static class AccountViewHolder extends RecyclerView.ViewHolder {
        ShapeableImageView accountIcon;
        TextView accountName;
        TextView accountDueTimeRemained;
        TextView accountRemark;
        TextView includeInAsset;
        TextView accountBalance;
        TextView accountCreditRemained;

        public AccountViewHolder(@NonNull StyleAccountItemInWalletsBinding accountItemInWalletsBinding) {
            super(accountItemInWalletsBinding.getRoot());
            accountIcon = accountItemInWalletsBinding.ivAccountItemIcon;
            accountName = accountItemInWalletsBinding.tvAccountItemName;
            accountDueTimeRemained = accountItemInWalletsBinding.tvAccountItemDueTimeRemained;
            accountRemark = accountItemInWalletsBinding.tvAccountItemRemark;
            includeInAsset = accountItemInWalletsBinding.tvAccountItemIncludeInAsset;
            accountBalance = accountItemInWalletsBinding.tvAccountItemBalance;
            accountCreditRemained = accountItemInWalletsBinding.tvAccountItemCreditRemained;
        }
    }
}
