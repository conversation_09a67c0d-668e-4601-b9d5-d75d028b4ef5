package com.example.likeqianwang.Repository;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;

import androidx.lifecycle.LiveData;

import com.example.likeqianwang.Dao.NotebookDao;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.Notebook;

import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class NotebookRepository {
    private final NotebookDao notebookDao;
    private final ExecutorService executorService;

    public NotebookRepository(Context context) {
        AppDatabase database = AppDatabase.getInstance(context);
        notebookDao = database.notebookDao();
        executorService = Executors.newFixedThreadPool(4);
    }

    public interface NotebookOperationCallback {
        void onSuccess(long result);
        void onError(String error);
    }

    public interface NotebookQueryCallback {
        void onSuccess(Notebook notebook);
        void onError(String error);
    }

    // 插入或更新账本
    public void insertOrUpdateNotebook(Notebook notebook, NotebookOperationCallback callback) {
        executorService.execute(() -> {
            try {
                // 检查名称是否重复
                String notebookId = notebook.getNotebookId();
                int count = notebookDao.getNotebookCountByNameExcluding(
                        notebook.getNotebookName(), notebookId);
                
                if (count > 0) {
                    new Handler(Looper.getMainLooper()).post(() -> {
                        if (callback != null) {
                            callback.onError("账本名称已存在");
                        }
                    });
                    return;
                }

                notebook.setUpdateTime(new Date());
                long result = notebookDao.insert(notebook);
                
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (callback != null) {
                        callback.onSuccess(result);
                    }
                });
            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                });
            }
        });
    }

    // 删除账本
    public void deleteNotebook(Notebook notebook, NotebookOperationCallback callback) {
        executorService.execute(() -> {
            try {
                // 检查是否为默认账本
                if (notebook.isDefault()) {
                    new Handler(Looper.getMainLooper()).post(() -> {
                        if (callback != null) {
                            callback.onError("不能删除默认账本");
                        }
                    });
                    return;
                }

                // 检查是否为最后一个账本
                int activeCount = notebookDao.getActiveNotebookCount();
                if (activeCount <= 1) {
                    new Handler(Looper.getMainLooper()).post(() -> {
                        if (callback != null) {
                            callback.onError("至少需要保留一个账本");
                        }
                    });
                    return;
                }

                // 软删除
                notebookDao.softDelete(notebook.getNotebookId());
                
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (callback != null) {
                        callback.onSuccess(1);
                    }
                });
            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                });
            }
        });
    }

    // 设置为默认账本
    public void setAsDefaultNotebook(String notebookId, NotebookOperationCallback callback) {
        executorService.execute(() -> {
            try {
                notebookDao.clearAllDefaultFlags();
                notebookDao.setAsDefault(notebookId);
                
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (callback != null) {
                        callback.onSuccess(1);
                    }
                });
            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                });
            }
        });
    }

    // 切换到指定账本
    public void switchToNotebook(String notebookId, NotebookOperationCallback callback) {
        executorService.execute(() -> {
            try {
                notebookDao.updateLastUsedTime(notebookId, new Date());
                
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (callback != null) {
                        callback.onSuccess(1);
                    }
                });
            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                });
            }
        });
    }

    // 获取当前账本
    public void getCurrentNotebook(NotebookQueryCallback callback) {
        executorService.execute(() -> {
            try {
                Notebook notebook = notebookDao.getCurrentNotebookSync();
                if (notebook == null) {
                    // 如果没有当前账本，获取默认账本
                    notebook = notebookDao.getDefaultNotebookSync();
                }
                
                final Notebook result = notebook;
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (callback != null) {
                        if (result != null) {
                            callback.onSuccess(result);
                        } else {
                            callback.onError("未找到可用账本");
                        }
                    }
                });
            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                });
            }
        });
    }

    // 创建默认账本
    public void createDefaultNotebook(NotebookOperationCallback callback) {
        executorService.execute(() -> {
            try {
                Notebook defaultNotebook = new Notebook("默认账本", "系统默认账本");
                defaultNotebook.setDefault(true);
                defaultNotebook.setNotebookIcon(android.R.drawable.ic_menu_agenda);
                
                long result = notebookDao.insert(defaultNotebook);
                
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (callback != null) {
                        callback.onSuccess(result);
                    }
                });
            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (callback != null) {
                        callback.onError(e.getMessage());
                    }
                });
            }
        });
    }

    // LiveData方法
    public LiveData<List<Notebook>> getAllActiveNotebooks() {
        return notebookDao.getAllActiveNotebooks();
    }

    public LiveData<Notebook> getDefaultNotebook() {
        return notebookDao.getDefaultNotebook();
    }

    public LiveData<Notebook> getCurrentNotebook() {
        return notebookDao.getCurrentNotebook();
    }

    public LiveData<Notebook> getNotebookById(String notebookId) {
        return notebookDao.getNotebookById(notebookId);
    }

    public LiveData<List<Notebook>> searchNotebooks(String query) {
        return notebookDao.searchNotebooks(query);
    }
}
