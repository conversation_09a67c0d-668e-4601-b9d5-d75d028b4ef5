package com.example.likeqianwang;

import android.app.Application;

import com.example.likeqianwang.Utils.DatabaseInitializer;
import com.example.likeqianwang.service.RecurringTransactionService;

public class LikeQianWangApplication extends Application {
    @Override
    public void onCreate() {
        super.onCreate();

        // 初始化数据库
        DatabaseInitializer initializer = new DatabaseInitializer(this);
        initializer.initializeData();

        // 初始化周期记账服务
        RecurringTransactionService.scheduleRecurringTransactionService(this);
    }
}
