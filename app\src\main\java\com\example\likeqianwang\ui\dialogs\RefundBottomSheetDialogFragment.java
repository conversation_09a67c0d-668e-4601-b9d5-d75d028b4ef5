package com.example.likeqianwang.ui.dialogs;

import android.app.Dialog;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;

import com.example.likeqianwang.Entity.Transactions;
import com.example.likeqianwang.R;
import com.example.likeqianwang.ViewModel.ReceiptViewModel;
import com.example.likeqianwang.ViewModel.TransactionCategoryViewModel;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.Date;

public class RefundBottomSheetDialogFragment extends BottomSheetDialogFragment {

    private static final String ARG_TRANSACTION = "transaction";
    private static final String TAG = "RefundDialog";

    // UI组件
    private TextView cancelButton;
    private TextView confirmButton;
    private ImageView originalIcon;
    private TextView originalCategory;
    private TextView originalAmount;
    private EditText refundAmountInput;
    private TextView fullAmountButton;
    private EditText refundReasonInput;

    // 数据
    private Transactions originalTransaction;
    private ReceiptViewModel receiptViewModel;
    private TransactionCategoryViewModel categoryViewModel;

    // 格式化工具
    private final DecimalFormat amountFormatter = new DecimalFormat("¥#,##0.00");

    public static RefundBottomSheetDialogFragment newInstance(Transactions transaction) {
        RefundBottomSheetDialogFragment fragment = new RefundBottomSheetDialogFragment();
        Bundle args = new Bundle();
        args.putSerializable(ARG_TRANSACTION, transaction);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        if (getArguments() != null) {
            originalTransaction = (Transactions) getArguments().getSerializable(ARG_TRANSACTION);
        }
        
        // 初始化ViewModels
        receiptViewModel = new ViewModelProvider(requireActivity()).get(ReceiptViewModel.class);
        categoryViewModel = new ViewModelProvider(requireActivity()).get(TransactionCategoryViewModel.class);
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            setupFullHeight(bottomSheetDialog);
        });
        return dialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.layout_refund_bottom_sheet, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        
        initViews(view);
        setupClickListeners();
        loadOriginalTransactionData();
    }

    private void initViews(View view) {
        cancelButton = view.findViewById(R.id.refund_cancel);
        confirmButton = view.findViewById(R.id.refund_confirm);
        originalIcon = view.findViewById(R.id.refund_original_icon);
        originalCategory = view.findViewById(R.id.refund_original_category);
        originalAmount = view.findViewById(R.id.refund_original_amount);
        refundAmountInput = view.findViewById(R.id.refund_amount_input);
        fullAmountButton = view.findViewById(R.id.refund_full_amount_button);
        refundReasonInput = view.findViewById(R.id.refund_reason_input);
    }

    private void setupClickListeners() {
        cancelButton.setOnClickListener(v -> dismiss());
        confirmButton.setOnClickListener(v -> processRefund());
        fullAmountButton.setOnClickListener(v -> setFullAmount());
    }

    private void loadOriginalTransactionData() {
        if (originalTransaction == null) {
            Log.e(TAG, "Original transaction is null");
            dismiss();
            return;
        }

        try {
            // 设置原交易金额
            originalAmount.setText(amountFormatter.format(originalTransaction.getAmount()));
            
            // 设置默认退款金额为全额
            refundAmountInput.setText(originalTransaction.getAmount().toString());

            // 加载分类信息
            if (originalTransaction.getCategoryId() > 0) {
                categoryViewModel.getCategoryById(originalTransaction.getCategoryId())
                        .observe(getViewLifecycleOwner(), category -> {
                            if (category != null) {
                                originalCategory.setText(category.getCategoryName());
                                try {
                                    originalIcon.setImageResource(category.getCategoryIcon());
                                } catch (Exception e) {
                                    Log.e(TAG, "Error setting category icon", e);
                                    originalIcon.setImageResource(R.drawable.ic_category_food);
                                }
                            } else {
                                originalCategory.setText("未分类");
                                originalIcon.setImageResource(R.drawable.ic_category_food);
                            }
                        });
            } else {
                originalCategory.setText("未分类");
                originalIcon.setImageResource(R.drawable.ic_category_food);
            }

        } catch (Exception e) {
            Log.e(TAG, "Error loading original transaction data", e);
            Toast.makeText(getContext(), "加载原交易信息失败", Toast.LENGTH_SHORT).show();
        }
    }

    private void setFullAmount() {
        if (originalTransaction != null) {
            refundAmountInput.setText(originalTransaction.getAmount().toString());
        }
    }

    private void processRefund() {
        try {
            // 验证输入
            String refundAmountStr = refundAmountInput.getText().toString().trim();
            if (TextUtils.isEmpty(refundAmountStr)) {
                Toast.makeText(getContext(), "请输入退款金额", Toast.LENGTH_SHORT).show();
                return;
            }

            BigDecimal refundAmount;
            try {
                refundAmount = new BigDecimal(refundAmountStr);
            } catch (NumberFormatException e) {
                Toast.makeText(getContext(), "请输入有效的金额", Toast.LENGTH_SHORT).show();
                return;
            }

            if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
                Toast.makeText(getContext(), "退款金额必须大于0", Toast.LENGTH_SHORT).show();
                return;
            }

            if (refundAmount.compareTo(originalTransaction.getAmount()) > 0) {
                Toast.makeText(getContext(), "退款金额不能超过原交易金额", Toast.LENGTH_SHORT).show();
                return;
            }

            String refundReason = refundReasonInput.getText().toString().trim();
            if (TextUtils.isEmpty(refundReason)) {
                refundReason = "退款";
            }

            // 创建退款交易
            createRefundTransaction(refundAmount, refundReason);

        } catch (Exception e) {
            Log.e(TAG, "Error processing refund", e);
            Toast.makeText(getContext(), "处理退款失败", Toast.LENGTH_SHORT).show();
        }
    }

    private void createRefundTransaction(BigDecimal refundAmount, String refundReason) {
        try {
            // 创建退款交易（与原交易相反的类型）
            Transactions refundTransaction = new Transactions();
            
            // 设置基本信息
            refundTransaction.setAmount(refundAmount);
            refundTransaction.setTransactionDate(new Date());
            refundTransaction.setRemark("退款: " + refundReason);
            refundTransaction.setCategoryId(originalTransaction.getCategoryId());
            refundTransaction.setFromAccountId(originalTransaction.getFromAccountId());
            refundTransaction.setIncludeInStats(true);
            refundTransaction.setIncludeInBudget(true);

            // 设置交易类型（与原交易相反）
            if ("EXPENSE".equals(originalTransaction.getType())) {
                refundTransaction.setType("INCOME");
            } else if ("INCOME".equals(originalTransaction.getType())) {
                refundTransaction.setType("EXPENSE");
            } else {
                // 转账类型的退款比较复杂，暂时不支持
                Toast.makeText(getContext(), "暂不支持转账退款", Toast.LENGTH_SHORT).show();
                return;
            }

            // 保存退款交易
            receiptViewModel.insertTransaction(refundTransaction, new ReceiptViewModel.TransactionOperationCallback() {
                @Override
                public void onSuccess() {
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            Toast.makeText(getContext(), "退款成功", Toast.LENGTH_SHORT).show();
                            dismiss();
                        });
                    }
                }

                @Override
                public void onError(String error) {
                    if (getActivity() != null) {
                        getActivity().runOnUiThread(() -> {
                            Toast.makeText(getContext(), "退款失败: " + error, Toast.LENGTH_SHORT).show();
                        });
                    }
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error creating refund transaction", e);
            Toast.makeText(getContext(), "创建退款交易失败", Toast.LENGTH_SHORT).show();
        }
    }

    private void setupFullHeight(BottomSheetDialog bottomSheetDialog) {
        try {
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                bottomSheet.getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error setting up full height", e);
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 清理资源
        receiptViewModel = null;
        categoryViewModel = null;
    }
}
