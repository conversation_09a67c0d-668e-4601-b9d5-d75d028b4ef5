package com.example.likeqianwang.Dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.likeqianwang.Entity.RecurringTransaction;

import java.util.Date;
import java.util.List;

@Dao
public interface RecurringTransactionDao {

    /**
     * 插入周期记账记录
     */
    @Insert
    long insert(RecurringTransaction recurringTransaction);

    /**
     * 更新周期记账记录
     */
    @Update
    void update(RecurringTransaction recurringTransaction);

    /**
     * 删除周期记账记录
     */
    @Delete
    void delete(RecurringTransaction recurringTransaction);

    /**
     * 根据ID查询周期记账记录
     */
    @Query("SELECT * FROM recurring_transactions WHERE recurringId = :recurringId")
    RecurringTransaction getRecurringTransactionById(String recurringId);

    /**
     * 根据ID查询周期记账记录（同步）
     */
    @Query("SELECT * FROM recurring_transactions WHERE recurringId = :recurringId")
    RecurringTransaction getRecurringTransactionByIdSync(String recurringId);

    /**
     * 获取所有周期记账记录
     */
    @Query("SELECT * FROM recurring_transactions ORDER BY createTime DESC")
    LiveData<List<RecurringTransaction>> getAllRecurringTransactions();

    /**
     * 获取所有周期记账记录（同步）
     */
    @Query("SELECT * FROM recurring_transactions ORDER BY createTime DESC")
    List<RecurringTransaction> getAllRecurringTransactionsSync();

    /**
     * 获取所有启用的周期记账记录
     */
    @Query("SELECT * FROM recurring_transactions WHERE isActive = 1 ORDER BY nextExecutionDate ASC")
    LiveData<List<RecurringTransaction>> getActiveRecurringTransactions();

    /**
     * 获取所有启用的周期记账记录（同步）
     */
    @Query("SELECT * FROM recurring_transactions WHERE isActive = 1 ORDER BY nextExecutionDate ASC")
    List<RecurringTransaction> getActiveRecurringTransactionsSync();

    /**
     * 获取需要执行的周期记账记录（下次执行时间小于等于当前时间）
     */
    @Query("SELECT * FROM recurring_transactions WHERE isActive = 1 AND nextExecutionDate <= :currentTime ORDER BY nextExecutionDate ASC")
    List<RecurringTransaction> getRecurringTransactionsDue(Date currentTime);

    /**
     * 根据类型获取周期记账记录
     */
    @Query("SELECT * FROM recurring_transactions WHERE type = :type ORDER BY createTime DESC")
    LiveData<List<RecurringTransaction>> getRecurringTransactionsByType(String type);

    /**
     * 根据账户ID获取周期记账记录
     */
    @Query("SELECT * FROM recurring_transactions WHERE fromAccountId = :accountId OR toAccountId = :accountId ORDER BY createTime DESC")
    LiveData<List<RecurringTransaction>> getRecurringTransactionsByAccount(String accountId);

    /**
     * 根据分类ID获取周期记账记录
     */
    @Query("SELECT * FROM recurring_transactions WHERE categoryId = :categoryId ORDER BY createTime DESC")
    LiveData<List<RecurringTransaction>> getRecurringTransactionsByCategory(long categoryId);

    /**
     * 更新下次执行时间
     */
    @Query("UPDATE recurring_transactions SET nextExecutionDate = :nextExecutionDate, lastExecutionDate = :lastExecutionDate, executionCount = executionCount + 1, updateTime = :updateTime WHERE recurringId = :recurringId")
    void updateNextExecutionDate(String recurringId, Date nextExecutionDate, Date lastExecutionDate, Date updateTime);

    /**
     * 启用/禁用周期记账
     */
    @Query("UPDATE recurring_transactions SET isActive = :isActive, updateTime = :updateTime WHERE recurringId = :recurringId")
    void updateActiveStatus(String recurringId, boolean isActive, Date updateTime);

    /**
     * 获取周期记账记录总数
     */
    @Query("SELECT COUNT(*) FROM recurring_transactions")
    int getRecurringTransactionCount();

    /**
     * 获取启用的周期记账记录总数
     */
    @Query("SELECT COUNT(*) FROM recurring_transactions WHERE isActive = 1")
    int getActiveRecurringTransactionCount();

    /**
     * 删除所有周期记账记录
     */
    @Query("DELETE FROM recurring_transactions")
    void deleteAll();

    /**
     * 根据名称搜索周期记账记录
     */
    @Query("SELECT * FROM recurring_transactions WHERE name LIKE '%' || :searchText || '%' ORDER BY createTime DESC")
    LiveData<List<RecurringTransaction>> searchRecurringTransactionsByName(String searchText);

    /**
     * 获取即将到期的周期记账记录（未来7天内）
     */
    @Query("SELECT * FROM recurring_transactions WHERE isActive = 1 AND nextExecutionDate BETWEEN :startTime AND :endTime ORDER BY nextExecutionDate ASC")
    List<RecurringTransaction> getUpcomingRecurringTransactions(Date startTime, Date endTime);
}
