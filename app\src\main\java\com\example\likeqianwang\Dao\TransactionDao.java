package com.example.likeqianwang.Dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.likeqianwang.Entity.Transactions;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Dao
public interface TransactionDao {

    @Insert
    long insert(Transactions transactions);

    @Delete
    void delete(Transactions transactions);

    @Update
    void update(Transactions transactions);

    @Query("SELECT * FROM Transactions WHERE transactionId = :transactionId")
    Transactions getById(long transactionId);

    @Query("SELECT * FROM Transactions WHERE transactionDate BETWEEN :startDate AND :endDate")
    LiveData<List<Transactions>> getByDateRange(Date startDate, Date endDate);

    @Query("SELECT SUM(amount) FROM Transactions " +
            "WHERE type = :type AND transactionDate BETWEEN :startDate AND :endDate " +
            "AND include_in_stats = 1")
    BigDecimal getSum(String type, Date startDate, Date endDate);

    // 获取指定日期范围内的交易记录，按日期分组
    @Query("SELECT * FROM Transactions " +
            "WHERE transactionDate BETWEEN :startDate AND :endDate " +
            "ORDER BY transactionDate DESC, transactionId DESC")
    LiveData<List<Transactions>> getTransactionsByDateRange(Date startDate, Date endDate);

    // 获取指定日期的交易记录
    @Query("SELECT * FROM Transactions " +
            "WHERE DATE(transactionDate/1000, 'unixepoch') = DATE(:date/1000, 'unixepoch') " +
            "ORDER BY transactionDate DESC, transactionId DESC")
    LiveData<List<Transactions>> getTransactionsByDate(Date date);

    // 获取指定月份的交易记录统计
    @Query("SELECT SUM(CASE WHEN type = 'INCOME' THEN amount ELSE 0 END) as totalIncome, " +
            "SUM(CASE WHEN type = 'EXPENSE' THEN amount ELSE 0 END) as totalExpense " +
            "FROM Transactions " +
            "WHERE strftime('%Y-%m', transactionDate/1000, 'unixepoch') = :yearMonth " +
            "AND include_in_stats = 1")
    LiveData<TransactionSummary> getMonthlyTransactionSummary(String yearMonth);

    // 获取指定日期的交易统计
    @Query("SELECT SUM(CASE WHEN type = 'INCOME' THEN amount ELSE 0 END) as totalIncome, " +
            "SUM(CASE WHEN type = 'EXPENSE' THEN amount ELSE 0 END) as totalExpense " +
            "FROM Transactions " +
            "WHERE DATE(transactionDate/1000, 'unixepoch') = DATE(:date/1000, 'unixepoch') " +
            "AND include_in_stats = 1")
    TransactionSummary getDailyTransactionSummary(Date date);

    // 内部类用于返回统计数据
    class TransactionSummary {
        public BigDecimal totalIncome;
        public BigDecimal totalExpense;

        public TransactionSummary() {
            this.totalIncome = BigDecimal.ZERO;
            this.totalExpense = BigDecimal.ZERO;
        }
    }
}

