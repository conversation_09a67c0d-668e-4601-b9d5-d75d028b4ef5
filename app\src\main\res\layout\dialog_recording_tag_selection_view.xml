<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/recording_page_tagSelectionView"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_bottom_sheet_background"
    android:paddingStart="15dp"
    android:paddingTop="10dp"
    android:paddingEnd="15dp"
    android:paddingBottom="25dp">

    <!-- 标题栏 -->
    <View
        android:id="@+id/drag_handle"
        android:layout_width="40dp"
        android:layout_height="4dp"
        android:layout_marginBottom="16dp"
        android:background="#DDDDDD"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/TagSelection_confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="5dp"
        android:background="?attr/selectableItemBackground"
        android:padding="10dp"
        android:text="@string/确定"
        android:textColor="@color/HuaQing"
        android:textSize="16sp"
        android:textStyle="bold"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/drag_handle" />

    <TextView
        android:id="@+id/TagSelection_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="5dp"
        android:background="?attr/selectableItemBackground"
        android:padding="10dp"
        android:text="@string/取消"
        android:textColor="#666666"
        android:textSize="16sp"
        app:layout_constraintBottom_toBottomOf="@id/TagSelection_confirm"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/TagSelection_confirm" />

    <!-- 已选择标签显示区域 -->
    <LinearLayout
        android:id="@+id/TagSelection_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/TagSelection_confirm">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="已选择："
            android:textSize="14sp"
            android:textColor="@color/grey"
            android:layout_marginBottom="8dp" />

        <com.google.android.flexbox.FlexboxLayout
            android:id="@+id/flexbox_selected_tags"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:flexWrap="wrap"
            app:alignItems="flex_start"
            app:justifyContent="flex_start"
            android:layout_marginBottom="16dp" />

    </LinearLayout>

    <!-- 标签列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_TagSelection_categories"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:maxHeight="400dp"
        tools:listitem="@layout/item_recording_tag_category"
        app:layout_constraintTop_toBottomOf="@id/TagSelection_container" />

    <TextView
        android:id="@+id/tv_TagSelection_management"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/标签管理"
        android:textSize="13sp"
        android:textColor="@color/grey"
        android:gravity="center"
        android:clickable="true"
        android:focusable="true"
        app:drawableEndCompat="@drawable/icon_arrow_right"
        app:layout_constraintTop_toBottomOf="@id/rv_TagSelection_categories"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>