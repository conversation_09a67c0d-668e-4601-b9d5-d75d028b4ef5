package com.example.likeqianwang.adapters;

import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.R;
import com.example.likeqianwang.ui.budget_settings.BudgetSettingsActivity;

import java.math.BigDecimal;
import java.util.List;

public class BudgetCategoryAdapter extends RecyclerView.Adapter<BudgetCategoryAdapter.ViewHolder> {
    
    private final List<BudgetSettingsActivity.BudgetCategoryItem> items;
    private final OnBudgetCategoryListener listener;
    
    public interface OnBudgetCategoryListener {
        void onBudgetCategoryAmountChanged(int position, BigDecimal amount);
        void onBudgetCategoryAlertThresholdChanged(int position, double threshold);
        void onBudgetCategoryDelete(int position);
    }
    
    public BudgetCategoryAdapter(List<BudgetSettingsActivity.BudgetCategoryItem> items, 
                                OnBudgetCategoryListener listener) {
        this.items = items;
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_budget_category, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        BudgetSettingsActivity.BudgetCategoryItem item = items.get(position);
        
        // 设置分类图标和名称
        holder.categoryIcon.setImageResource(item.category.getCategoryIcon());
        holder.categoryName.setText(item.category.getCategoryName());
        
        // 设置预算金额
        holder.budgetAmount.removeTextChangedListener(holder.amountWatcher);
        holder.budgetAmount.setText(item.budgetAmount.compareTo(BigDecimal.ZERO) == 0 ? 
                "" : item.budgetAmount.toString());
        holder.amountWatcher = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
            
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}
            
            @Override
            public void afterTextChanged(Editable s) {
                try {
                    String amountStr = s.toString().trim();
                    BigDecimal amount = amountStr.isEmpty() ? BigDecimal.ZERO : new BigDecimal(amountStr);
                    if (listener != null) {
                        listener.onBudgetCategoryAmountChanged(holder.getAdapterPosition(), amount);
                    }
                } catch (NumberFormatException e) {
                    // 忽略无效输入
                }
            }
        };
        holder.budgetAmount.addTextChangedListener(holder.amountWatcher);
        
        // 设置预警阈值
        holder.alertThreshold.removeTextChangedListener(holder.thresholdWatcher);
        holder.alertThreshold.setText(String.valueOf((int)(item.alertThreshold * 100)));
        holder.thresholdWatcher = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}
            
            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {}
            
            @Override
            public void afterTextChanged(Editable s) {
                try {
                    String thresholdStr = s.toString().trim();
                    if (!thresholdStr.isEmpty()) {
                        int threshold = Integer.parseInt(thresholdStr);
                        if (threshold >= 0 && threshold <= 100) {
                            if (listener != null) {
                                listener.onBudgetCategoryAlertThresholdChanged(
                                        holder.getAdapterPosition(), threshold / 100.0);
                            }
                        }
                    }
                } catch (NumberFormatException e) {
                    // 忽略无效输入
                }
            }
        };
        holder.alertThreshold.addTextChangedListener(holder.thresholdWatcher);
        
        // 删除按钮
        holder.deleteButton.setOnClickListener(v -> {
            if (listener != null) {
                listener.onBudgetCategoryDelete(holder.getAdapterPosition());
            }
        });
    }
    
    @Override
    public int getItemCount() {
        return items.size();
    }
    
    public static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView categoryIcon;
        TextView categoryName;
        EditText budgetAmount;
        EditText alertThreshold;
        ImageView deleteButton;
        TextWatcher amountWatcher;
        TextWatcher thresholdWatcher;
        
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            categoryIcon = itemView.findViewById(R.id.budget_category_icon);
            categoryName = itemView.findViewById(R.id.budget_category_name);
            budgetAmount = itemView.findViewById(R.id.budget_category_amount);
            alertThreshold = itemView.findViewById(R.id.budget_category_alert_threshold);
            deleteButton = itemView.findViewById(R.id.budget_category_delete);
        }
    }
}
