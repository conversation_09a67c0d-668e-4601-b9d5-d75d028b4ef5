package com.example.likeqianwang.Dao;

import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.Query;
import androidx.room.Update;

import com.example.likeqianwang.Entity.TransactionTag;
import com.example.likeqianwang.Entity.TransactionTagCrossRef;

import java.util.List;

@Dao
public interface TransactionTagCrossRefDao {
    @Insert
    void insert(TransactionTagCrossRef join);

    @Update
    void update(TransactionTagCrossRef join);

    @Delete
    void delete(TransactionTagCrossRef join);

    @Query("DELETE FROM transaction_tag_cross_ref WHERE transactionId = :transactionId")
    void deleteTagsForRecord(int transactionId);

    @Query("DELETE FROM transaction_tag_cross_ref WHERE transactionId = :transactionId")
    void deleteByTransactionId(long transactionId);

    @Query("SELECT t.* FROM transaction_tags t " +
            "INNER JOIN transaction_tag_cross_ref tc ON t.tagId = tc.tagId " +
            "WHERE tc.transactionId = :transactionId " +
            "ORDER BY t.order_index ASC, t.tag_name ASC")
    List<TransactionTag> getTagsForTransaction(long transactionId);

}
