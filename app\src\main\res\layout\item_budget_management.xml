<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:background="?attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 预算标题行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- 预算类型图标 -->
            <ImageView
                android:id="@+id/budget_item_type_icon"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="12dp"
                android:contentDescription="@string/budget_desc_分类图标"
                app:tint="@color/HuaQing"
                tools:src="@drawable/ic_budget" />

            <!-- 预算名称和类型 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/budget_item_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="总预算" />

                <TextView
                    android:id="@+id/budget_item_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textColor="@color/grey"
                    android:textSize="12sp"
                    tools:text="月度预算" />

            </LinearLayout>

            <!-- 预算状态 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/budget_item_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="¥5000.00" />

                <TextView
                    android:id="@+id/budget_item_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:layout_marginTop="2dp"
                    android:textSize="12sp"
                    tools:text="已用 60%" />

            </LinearLayout>

            <!-- 更多操作按钮 -->
            <ImageView
                android:id="@+id/budget_item_more"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginStart="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="更多操作"
                android:src="@drawable/ic_more_vert"
                app:tint="@color/grey" />

        </LinearLayout>

        <!-- 预算进度条 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:orientation="vertical">

            <!-- 使用情况文字 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/budget_item_used_amount"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:textColor="@color/grey"
                    android:textSize="12sp"
                    tools:text="已用: ¥3000.00" />

                <TextView
                    android:id="@+id/budget_item_remaining_amount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/grey"
                    android:textSize="12sp"
                    tools:text="剩余: ¥2000.00" />

            </LinearLayout>

            <!-- 进度条 -->
            <ProgressBar
                android:id="@+id/budget_item_progress"
                style="?android:attr/progressBarStyleHorizontal"
                android:layout_width="match_parent"
                android:layout_height="8dp"
                android:layout_marginTop="4dp"
                android:progressDrawable="@drawable/budget_progress_drawable"
                tools:progress="60" />

        </LinearLayout>

        <!-- 备注信息 -->
        <TextView
            android:id="@+id/budget_item_remark"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:textColor="@color/grey"
            android:textSize="12sp"
            android:visibility="gone"
            tools:text="这是一个备注信息"
            tools:visibility="visible" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
