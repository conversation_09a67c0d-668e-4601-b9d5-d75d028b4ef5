{"checksum": "1aeacb9a52f475b9c5c6c84e0d9231a6217ca3fa8ac83528b787318c11291751", "data": {"id": "033e1d52-b044-4263-aadd-fc4d227bd08d", "createdAt": 1748706218844, "updatedAt": *************, "title": "Adjusting Drawable in TextView", "messages": [{"id": "354c3b9f-bf9a-45e9-93ff-ebb61387ad85", "role": "user", "content": [{"type": "codeSnippet", "text": "app:drawableEndCompat=\"@drawable/icon_arrow_right\"", "language": "xml"}, {"type": "text", "text": "如何调整textview里边插入的drawable的大小和位置"}], "context": {"currentFile": {"path": "app\\src\\main\\res\\layout\\dialog_recording_tag_selection_view.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\dialog_recording_tag_selection_view.xml", "sources": ["opened_tab"], "content": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<androidx.constraintlayout.widget.ConstraintLayout xmlns:android=\"http://schemas.android.com/apk/res/android\"\r\n    xmlns:app=\"http://schemas.android.com/apk/res-auto\"\r\n    xmlns:tools=\"http://schemas.android.com/tools\"\r\n    android:id=\"@+id/recording_page_tagSelectionView\"\r\n    android:layout_width=\"match_parent\"\r\n    android:layout_height=\"wrap_content\"\r\n    android:background=\"@drawable/dialog_bottom_sheet_background\"\r\n    android:paddingStart=\"15dp\"\r\n    android:paddingTop=\"10dp\"\r\n    android:paddingEnd=\"15dp\"\r\n    android:paddingBottom=\"25dp\">\r\n\r\n    <!-- 标题栏 -->\r\n    <View\r\n        android:id=\"@+id/drag_handle\"\r\n        android:layout_width=\"40dp\"\r\n        android:layout_height=\"4dp\"\r\n        android:layout_marginBottom=\"16dp\"\r\n        android:background=\"#DDDDDD\"\r\n        app:layout_constraintLeft_toLeftOf=\"parent\"\r\n        app:layout_constraintRight_toRightOf=\"parent\"\r\n        app:layout_constraintTop_toTopOf=\"parent\" />\r\n\r\n    <TextView\r\n        android:id=\"@+id/TagSelection_confirm\"\r\n        android:layout_width=\"wrap_content\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:layout_marginEnd=\"5dp\"\r\n        android:background=\"?attr/selectableItemBackground\"\r\n        android:padding=\"10dp\"\r\n        android:text=\"@string/确定\"\r\n        android:textColor=\"@color/HuaQing\"\r\n        android:textSize=\"16sp\"\r\n        android:textStyle=\"bold\"\r\n        app:layout_constraintRight_toRightOf=\"parent\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/drag_handle\" />\r\n\r\n    <TextView\r\n        android:id=\"@+id/TagSelection_cancel\"\r\n        android:layout_width=\"wrap_content\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:layout_marginStart=\"5dp\"\r\n        android:background=\"?attr/selectableItemBackground\"\r\n        android:padding=\"10dp\"\r\n        android:text=\"@string/取消\"\r\n        android:textColor=\"#666666\"\r\n        android:textSize=\"16sp\"\r\n        app:layout_constraintBottom_toBottomOf=\"@id/TagSelection_confirm\"\r\n        app:layout_constraintLeft_toLeftOf=\"parent\"\r\n        app:layout_constraintTop_toTopOf=\"@id/TagSelection_confirm\" />\r\n\r\n    <!-- 已选择标签显示区域 -->\r\n    <LinearLayout\r\n        android:id=\"@+id/TagSelection_container\"\r\n        android:layout_width=\"match_parent\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:orientation=\"vertical\"\r\n        android:visibility=\"gone\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/TagSelection_confirm\">\r\n\r\n        <TextView\r\n            android:layout_width=\"wrap_content\"\r\n            android:layout_height=\"wrap_content\"\r\n            android:text=\"已选择：\"\r\n            android:textSize=\"14sp\"\r\n            android:textColor=\"@color/grey\"\r\n            android:layout_marginBottom=\"8dp\" />\r\n\r\n        <com.google.android.flexbox.FlexboxLayout\r\n            android:id=\"@+id/flexbox_selected_tags\"\r\n            android:layout_width=\"match_parent\"\r\n            android:layout_height=\"wrap_content\"\r\n            app:flexWrap=\"wrap\"\r\n            app:alignItems=\"flex_start\"\r\n            app:justifyContent=\"flex_start\"\r\n            android:layout_marginBottom=\"16dp\" />\r\n\r\n    </LinearLayout>\r\n\r\n    <!-- 标签列表 -->\r\n    <androidx.recyclerview.widget.RecyclerView\r\n        android:id=\"@+id/rv_TagSelection_categories\"\r\n        android:layout_width=\"match_parent\"\r\n        android:layout_height=\"0dp\"\r\n        android:layout_weight=\"1\"\r\n        android:maxHeight=\"400dp\"\r\n        tools:listitem=\"@layout/item_tag_category\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/TagSelection_container\" />\r\n\r\n    <TextView\r\n        android:id=\"@+id/tv_TagSelection_management\"\r\n        android:layout_width=\"wrap_content\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:text=\"@string/管理标签\"\r\n        android:textSize=\"14sp\"\r\n        android:textColor=\"@color/grey\"\r\n        android:gravity=\"center_horizontal\"\r\n        android:clickable=\"true\"\r\n        android:focusable=\"true\"\r\n        android:onClick=\"onManagementClick\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/rv_TagSelection_categories\"\r\n        app:layout_constraintStart_toStartOf=\"parent\"\r\n        app:layout_constraintEnd_toEndOf=\"parent\"/>\r\n\r\n\r\n</androidx.constraintlayout.widget.ConstraintLayout>"}, "codebaseEnabled": true, "ragFiles": [{"path": "app/src/main/java/com/example/likeqianwang/DataModel/AccountCategory.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\DataModel\\AccountCategory.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ViewModel/TransactionTagViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionTagViewModel.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/main_wallets/WalletsFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\main_wallets\\WalletsFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/recording_date_time_picker/RecordingDateTimePickerDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\recording_date_time_picker\\RecordingDateTimePickerDialog.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/Utils/SwipeToDeleteCallback.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Utils\\SwipeToDeleteCallback.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/wallets_bank_seleciton/WalletsBankSelectionDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\wallets_bank_seleciton\\WalletsBankSelectionDialog.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/Utils/DatabaseInitializer.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Utils\\DatabaseInitializer.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/recording_in/RecordingInFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\recording_in\\RecordingInFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/recording_out/RecordingOutFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\recording_out\\RecordingOutFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/Utils/InnerDividerItemDecoration.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Utils\\InnerDividerItemDecoration.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/RecordingPageActivity.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\RecordingPageActivity.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/adapters/AccountBankItem_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\AccountBankItem_adapter.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/RecordingTransferAccountSelectionDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\RecordingTransferAccountSelectionDialog.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/adapters/InOut_Transfer_ViewPager_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\InOut_Transfer_ViewPager_adapter.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/adapters/InOut_Budget_ViewPager_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\InOut_Budget_ViewPager_adapter.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/adapters/initViewPager_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\initViewPager_adapter.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/adapters/AccountTypeItem_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\AccountTypeItem_adapter.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/adapters/AccountItemInWallet_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\AccountItemInWallet_adapter.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/wallets_add_new_account/WalletsAddNewAccountDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\wallets_add_new_account\\WalletsAddNewAccountDialog.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/adapters/RecordingDialogSubcategory_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\RecordingDialogSubcategory_adapter.java", "sources": ["keyword_search"]}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\FillNewAccountInfoActivity.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\FillNewAccountInfoActivity.java", "sources": []}, {"path": "app\\build.gradle.kts", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\build.gradle.kts", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\MainActivity.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\MainActivity.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\recording_transer\\RecordingTransferFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\recording_transer\\RecordingTransferFragment.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\Utils\\SwipeToDeleteCallback.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Utils\\SwipeToDeleteCallback.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\wallets_add_new_account\\WalletsAddNewAccountDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\wallets_add_new_account\\WalletsAddNewAccountDialog.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\RecordingTransferAccountSelectionDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\RecordingTransferAccountSelectionDialog.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\RecordingInOutCategory_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\RecordingInOutCategory_adapter.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\AccountItemInWallet_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\AccountItemInWallet_adapter.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\wallets_bank_seleciton\\WalletsBankSelectionDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\wallets_bank_seleciton\\WalletsBankSelectionDialog.java", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\widget_icon_dot_combine.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\widget_icon_dot_combine.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\icon_recording_airplane.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\icon_recording_airplane.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\icon_recording_mobile_credit.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\icon_recording_mobile_credit.xml", "sources": []}, {"path": "app\\src\\main\\res\\layout\\style_account_type_item_view.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\style_account_type_item_view.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\frag_receipt_add_image.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\frag_receipt_add_image.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\account_icon_credit_card.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\account_icon_credit_card.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\icon_recording_fruit.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\icon_recording_fruit.xml", "sources": []}, {"path": "app\\src\\main\\res\\layout\\style_navbar_item_view.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\style_navbar_item_view.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\icon_arrow_back.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\icon_arrow_back.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\icon_recording_education.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\icon_recording_education.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\icon_arrow_right.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\icon_arrow_right.xml", "sources": []}, {"path": "app/src/main/java/com/example/likeqianwang/ViewModel/TransactionTagViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionTagViewModel.java", "sources": ["opened_tab"]}, {"path": "app/src/main/res/layout/dialog_recording_tag_selection_view.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\dialog_recording_tag_selection_view.xml", "sources": ["opened_tab"]}], "rerankedFiles": [{"path": "app\\src\\main\\res\\layout\\dialog_recording_tag_selection_view.xml", "fsPath": "app\\src\\main\\res\\layout\\dialog_recording_tag_selection_view.xml"}, {"path": "app\\src\\main\\res\\layout\\style_account_type_item_view.xml", "fsPath": "app\\src\\main\\res\\layout\\style_account_type_item_view.xml"}], "usedFilePaths": [{"path": "app\\src\\main\\res\\layout\\dialog_recording_tag_selection_view.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\dialog_recording_tag_selection_view.xml", "sources": ["opened_tab"], "content": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<androidx.constraintlayout.widget.ConstraintLayout xmlns:android=\"http://schemas.android.com/apk/res/android\"\r\n    xmlns:app=\"http://schemas.android.com/apk/res-auto\"\r\n    xmlns:tools=\"http://schemas.android.com/tools\"\r\n    android:id=\"@+id/recording_page_tagSelectionView\"\r\n    android:layout_width=\"match_parent\"\r\n    android:layout_height=\"wrap_content\"\r\n    android:background=\"@drawable/dialog_bottom_sheet_background\"\r\n    android:paddingStart=\"15dp\"\r\n    android:paddingTop=\"10dp\"\r\n    android:paddingEnd=\"15dp\"\r\n    android:paddingBottom=\"25dp\">\r\n\r\n    <!-- 标题栏 -->\r\n    <View\r\n        android:id=\"@+id/drag_handle\"\r\n        android:layout_width=\"40dp\"\r\n        android:layout_height=\"4dp\"\r\n        android:layout_marginBottom=\"16dp\"\r\n        android:background=\"#DDDDDD\"\r\n        app:layout_constraintLeft_toLeftOf=\"parent\"\r\n        app:layout_constraintRight_toRightOf=\"parent\"\r\n        app:layout_constraintTop_toTopOf=\"parent\" />\r\n\r\n    <TextView\r\n        android:id=\"@+id/TagSelection_confirm\"\r\n        android:layout_width=\"wrap_content\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:layout_marginEnd=\"5dp\"\r\n        android:background=\"?attr/selectableItemBackground\"\r\n        android:padding=\"10dp\"\r\n        android:text=\"@string/确定\"\r\n        android:textColor=\"@color/HuaQing\"\r\n        android:textSize=\"16sp\"\r\n        android:textStyle=\"bold\"\r\n        app:layout_constraintRight_toRightOf=\"parent\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/drag_handle\" />\r\n\r\n    <TextView\r\n        android:id=\"@+id/TagSelection_cancel\"\r\n        android:layout_width=\"wrap_content\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:layout_marginStart=\"5dp\"\r\n        android:background=\"?attr/selectableItemBackground\"\r\n        android:padding=\"10dp\"\r\n        android:text=\"@string/取消\"\r\n        android:textColor=\"#666666\"\r\n        android:textSize=\"16sp\"\r\n        app:layout_constraintBottom_toBottomOf=\"@id/TagSelection_confirm\"\r\n        app:layout_constraintLeft_toLeftOf=\"parent\"\r\n        app:layout_constraintTop_toTopOf=\"@id/TagSelection_confirm\" />\r\n\r\n    <!-- 已选择标签显示区域 -->\r\n    <LinearLayout\r\n        android:id=\"@+id/TagSelection_container\"\r\n        android:layout_width=\"match_parent\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:orientation=\"vertical\"\r\n        android:visibility=\"gone\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/TagSelection_confirm\">\r\n\r\n        <TextView\r\n            android:layout_width=\"wrap_content\"\r\n            android:layout_height=\"wrap_content\"\r\n            android:text=\"已选择：\"\r\n            android:textSize=\"14sp\"\r\n            android:textColor=\"@color/grey\"\r\n            android:layout_marginBottom=\"8dp\" />\r\n\r\n        <com.google.android.flexbox.FlexboxLayout\r\n            android:id=\"@+id/flexbox_selected_tags\"\r\n            android:layout_width=\"match_parent\"\r\n            android:layout_height=\"wrap_content\"\r\n            app:flexWrap=\"wrap\"\r\n            app:alignItems=\"flex_start\"\r\n            app:justifyContent=\"flex_start\"\r\n            android:layout_marginBottom=\"16dp\" />\r\n\r\n    </LinearLayout>\r\n\r\n    <!-- 标签列表 -->\r\n    <androidx.recyclerview.widget.RecyclerView\r\n        android:id=\"@+id/rv_TagSelection_categories\"\r\n        android:layout_width=\"match_parent\"\r\n        android:layout_height=\"0dp\"\r\n        android:layout_weight=\"1\"\r\n        android:maxHeight=\"400dp\"\r\n        tools:listitem=\"@layout/item_tag_category\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/TagSelection_container\" />\r\n\r\n    <TextView\r\n        android:id=\"@+id/tv_TagSelection_management\"\r\n        android:layout_width=\"wrap_content\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:text=\"@string/管理标签\"\r\n        android:textSize=\"14sp\"\r\n        android:textColor=\"@color/grey\"\r\n        android:gravity=\"center_horizontal\"\r\n        android:clickable=\"true\"\r\n        android:focusable=\"true\"\r\n        android:onClick=\"onManagementClick\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/rv_TagSelection_categories\"\r\n        app:layout_constraintStart_toStartOf=\"parent\"\r\n        app:layout_constraintEnd_toEndOf=\"parent\"/>\r\n\r\n\r\n</androidx.constraintlayout.widget.ConstraintLayout>"}, {"path": "app\\src\\main\\res\\layout\\style_account_type_item_view.xml", "fsPath": "app\\src\\main\\res\\layout\\style_account_type_item_view.xml"}]}, "createdAt": *************, "isInternal": false, "rawContent": {"type": "doc", "content": [{"type": "codeBlock", "attrs": {"language": "xml"}, "content": [{"type": "text", "text": "app:drawableEndCompat=\"@drawable/icon_arrow_right\""}]}, {"type": "paragraph", "content": [{"type": "text", "text": "如何调整textview里边插入的drawable的大小和位置"}]}]}}, {"id": "********-7ad8-479f-9911-c2c5517df332", "role": "assistant", "content": [{"type": "text", "text": "To adjust the size and position of a drawable inserted in a `TextView` in Android, you can use the following approach:\n\n1. **Adjust Drawable Size:**\n   You can define the size of the drawable in the drawable XML file itself or programmatically set the size in your Java/Kotlin code. If you want to set it in XML, you can create a new drawable resource file and specify the size using `android:width` and `android:height`.\n\n   Example of a drawable XML file (`res/drawable/icon_arrow_right.xml`):\n   ```xml\n   <vector xmlns:android=\"http://schemas.android.com/apk/res/android\"\n       android:width=\"24dp\"\n       android:height=\"24dp\"\n       android:viewportWidth=\"24\"\n       android:viewportHeight=\"24\">\n       <!-- Your vector path here -->\n   </vector>\n   ```\n\n2. **Adjust Drawable Position:**\n   You can use the `drawablePadding` attribute in the `TextView` to adjust the space between the text and the drawable.\n\n   Example:\n   ```xml\n   <TextView\n       android:id=\"@+id/tv_TagSelection_management\"\n       android:layout_width=\"wrap_content\"\n       android:layout_height=\"wrap_content\"\n       android:text=\"@string/管理标签\"\n       android:textSize=\"14sp\"\n       android:textColor=\"@color/grey\"\n       android:gravity=\"center\"\n       android:clickable=\"true\"\n       android:focusable=\"true\"\n       app:drawableEndCompat=\"@drawable/icon_arrow_right\"\n       android:drawablePadding=\"8dp\"\n       app:layout_constraintTop_toBottomOf=\"@id/rv_TagSelection_categories\"\n       app:layout_constraintStart_toStartOf=\"parent\"\n       app:layout_constraintEnd_toEndOf=\"parent\"/>\n   ```\n\n3. **Programmatically Adjust Drawable:**\n   If you need more control, you can adjust the drawable size programmatically in your activity or fragment:\n\n   ```java\n   TextView textView = findViewById(R.id.tv_TagSelection_management);\n   Drawable drawable = getResources().getDrawable(R.drawable.icon_arrow_right);\n   drawable.setBounds(0, 0, 24, 24); // Set the size here\n   textView.setCompoundDrawables(null, null, drawable, null);\n   ```\n\nBy following these steps, you can effectively control the size and position of the drawable within your `TextView`."}], "context": {"currentFile": {"path": "app\\src\\main\\res\\layout\\dialog_recording_tag_selection_view.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\dialog_recording_tag_selection_view.xml", "sources": ["opened_tab"], "content": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<androidx.constraintlayout.widget.ConstraintLayout xmlns:android=\"http://schemas.android.com/apk/res/android\"\r\n    xmlns:app=\"http://schemas.android.com/apk/res-auto\"\r\n    xmlns:tools=\"http://schemas.android.com/tools\"\r\n    android:id=\"@+id/recording_page_tagSelectionView\"\r\n    android:layout_width=\"match_parent\"\r\n    android:layout_height=\"wrap_content\"\r\n    android:background=\"@drawable/dialog_bottom_sheet_background\"\r\n    android:paddingStart=\"15dp\"\r\n    android:paddingTop=\"10dp\"\r\n    android:paddingEnd=\"15dp\"\r\n    android:paddingBottom=\"25dp\">\r\n\r\n    <!-- 标题栏 -->\r\n    <View\r\n        android:id=\"@+id/drag_handle\"\r\n        android:layout_width=\"40dp\"\r\n        android:layout_height=\"4dp\"\r\n        android:layout_marginBottom=\"16dp\"\r\n        android:background=\"#DDDDDD\"\r\n        app:layout_constraintLeft_toLeftOf=\"parent\"\r\n        app:layout_constraintRight_toRightOf=\"parent\"\r\n        app:layout_constraintTop_toTopOf=\"parent\" />\r\n\r\n    <TextView\r\n        android:id=\"@+id/TagSelection_confirm\"\r\n        android:layout_width=\"wrap_content\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:layout_marginEnd=\"5dp\"\r\n        android:background=\"?attr/selectableItemBackground\"\r\n        android:padding=\"10dp\"\r\n        android:text=\"@string/确定\"\r\n        android:textColor=\"@color/HuaQing\"\r\n        android:textSize=\"16sp\"\r\n        android:textStyle=\"bold\"\r\n        app:layout_constraintRight_toRightOf=\"parent\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/drag_handle\" />\r\n\r\n    <TextView\r\n        android:id=\"@+id/TagSelection_cancel\"\r\n        android:layout_width=\"wrap_content\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:layout_marginStart=\"5dp\"\r\n        android:background=\"?attr/selectableItemBackground\"\r\n        android:padding=\"10dp\"\r\n        android:text=\"@string/取消\"\r\n        android:textColor=\"#666666\"\r\n        android:textSize=\"16sp\"\r\n        app:layout_constraintBottom_toBottomOf=\"@id/TagSelection_confirm\"\r\n        app:layout_constraintLeft_toLeftOf=\"parent\"\r\n        app:layout_constraintTop_toTopOf=\"@id/TagSelection_confirm\" />\r\n\r\n    <!-- 已选择标签显示区域 -->\r\n    <LinearLayout\r\n        android:id=\"@+id/TagSelection_container\"\r\n        android:layout_width=\"match_parent\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:orientation=\"vertical\"\r\n        android:visibility=\"gone\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/TagSelection_confirm\">\r\n\r\n        <TextView\r\n            android:layout_width=\"wrap_content\"\r\n            android:layout_height=\"wrap_content\"\r\n            android:text=\"已选择：\"\r\n            android:textSize=\"14sp\"\r\n            android:textColor=\"@color/grey\"\r\n            android:layout_marginBottom=\"8dp\" />\r\n\r\n        <com.google.android.flexbox.FlexboxLayout\r\n            android:id=\"@+id/flexbox_selected_tags\"\r\n            android:layout_width=\"match_parent\"\r\n            android:layout_height=\"wrap_content\"\r\n            app:flexWrap=\"wrap\"\r\n            app:alignItems=\"flex_start\"\r\n            app:justifyContent=\"flex_start\"\r\n            android:layout_marginBottom=\"16dp\" />\r\n\r\n    </LinearLayout>\r\n\r\n    <!-- 标签列表 -->\r\n    <androidx.recyclerview.widget.RecyclerView\r\n        android:id=\"@+id/rv_TagSelection_categories\"\r\n        android:layout_width=\"match_parent\"\r\n        android:layout_height=\"0dp\"\r\n        android:layout_weight=\"1\"\r\n        android:maxHeight=\"400dp\"\r\n        tools:listitem=\"@layout/item_tag_category\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/TagSelection_container\" />\r\n\r\n    <TextView\r\n        android:id=\"@+id/tv_TagSelection_management\"\r\n        android:layout_width=\"wrap_content\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:text=\"@string/管理标签\"\r\n        android:textSize=\"14sp\"\r\n        android:textColor=\"@color/grey\"\r\n        android:gravity=\"center_horizontal\"\r\n        android:clickable=\"true\"\r\n        android:focusable=\"true\"\r\n        android:onClick=\"onManagementClick\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/rv_TagSelection_categories\"\r\n        app:layout_constraintStart_toStartOf=\"parent\"\r\n        app:layout_constraintEnd_toEndOf=\"parent\"/>\r\n\r\n\r\n</androidx.constraintlayout.widget.ConstraintLayout>"}, "codebaseEnabled": true, "ragFiles": [{"path": "app/src/main/java/com/example/likeqianwang/DataModel/AccountCategory.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\DataModel\\AccountCategory.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ViewModel/TransactionTagViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionTagViewModel.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/main_wallets/WalletsFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\main_wallets\\WalletsFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/recording_date_time_picker/RecordingDateTimePickerDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\recording_date_time_picker\\RecordingDateTimePickerDialog.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/Utils/SwipeToDeleteCallback.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Utils\\SwipeToDeleteCallback.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/wallets_bank_seleciton/WalletsBankSelectionDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\wallets_bank_seleciton\\WalletsBankSelectionDialog.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/Utils/DatabaseInitializer.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Utils\\DatabaseInitializer.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/recording_in/RecordingInFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\recording_in\\RecordingInFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/recording_out/RecordingOutFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\recording_out\\RecordingOutFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/Utils/InnerDividerItemDecoration.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Utils\\InnerDividerItemDecoration.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/RecordingPageActivity.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\RecordingPageActivity.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/adapters/AccountBankItem_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\AccountBankItem_adapter.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/RecordingTransferAccountSelectionDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\RecordingTransferAccountSelectionDialog.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/adapters/InOut_Transfer_ViewPager_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\InOut_Transfer_ViewPager_adapter.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/adapters/InOut_Budget_ViewPager_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\InOut_Budget_ViewPager_adapter.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/adapters/initViewPager_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\initViewPager_adapter.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/adapters/AccountTypeItem_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\AccountTypeItem_adapter.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/adapters/AccountItemInWallet_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\AccountItemInWallet_adapter.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/wallets_add_new_account/WalletsAddNewAccountDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\wallets_add_new_account\\WalletsAddNewAccountDialog.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/adapters/RecordingDialogSubcategory_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\RecordingDialogSubcategory_adapter.java", "sources": ["keyword_search"]}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\FillNewAccountInfoActivity.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\FillNewAccountInfoActivity.java", "sources": []}, {"path": "app\\build.gradle.kts", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\build.gradle.kts", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\MainActivity.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\MainActivity.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\recording_transer\\RecordingTransferFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\recording_transer\\RecordingTransferFragment.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\Utils\\SwipeToDeleteCallback.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Utils\\SwipeToDeleteCallback.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\wallets_add_new_account\\WalletsAddNewAccountDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\wallets_add_new_account\\WalletsAddNewAccountDialog.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\RecordingTransferAccountSelectionDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\RecordingTransferAccountSelectionDialog.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\RecordingInOutCategory_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\RecordingInOutCategory_adapter.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\AccountItemInWallet_adapter.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\adapters\\AccountItemInWallet_adapter.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\wallets_bank_seleciton\\WalletsBankSelectionDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\wallets_bank_seleciton\\WalletsBankSelectionDialog.java", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\widget_icon_dot_combine.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\widget_icon_dot_combine.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\icon_recording_airplane.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\icon_recording_airplane.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\icon_recording_mobile_credit.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\icon_recording_mobile_credit.xml", "sources": []}, {"path": "app\\src\\main\\res\\layout\\style_account_type_item_view.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\style_account_type_item_view.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\frag_receipt_add_image.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\frag_receipt_add_image.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\account_icon_credit_card.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\account_icon_credit_card.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\icon_recording_fruit.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\icon_recording_fruit.xml", "sources": []}, {"path": "app\\src\\main\\res\\layout\\style_navbar_item_view.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\style_navbar_item_view.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\icon_arrow_back.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\icon_arrow_back.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\icon_recording_education.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\icon_recording_education.xml", "sources": []}, {"path": "app\\src\\main\\res\\drawable\\icon_arrow_right.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\icon_arrow_right.xml", "sources": []}, {"path": "app/src/main/java/com/example/likeqianwang/ViewModel/TransactionTagViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionTagViewModel.java", "sources": ["opened_tab"]}, {"path": "app/src/main/res/layout/dialog_recording_tag_selection_view.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\dialog_recording_tag_selection_view.xml", "sources": ["opened_tab"]}], "rerankedFiles": [{"path": "app\\src\\main\\res\\layout\\dialog_recording_tag_selection_view.xml", "fsPath": "app\\src\\main\\res\\layout\\dialog_recording_tag_selection_view.xml"}, {"path": "app\\src\\main\\res\\layout\\style_account_type_item_view.xml", "fsPath": "app\\src\\main\\res\\layout\\style_account_type_item_view.xml"}], "usedFilePaths": [{"path": "app\\src\\main\\res\\layout\\dialog_recording_tag_selection_view.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\dialog_recording_tag_selection_view.xml", "sources": ["opened_tab"], "content": "<?xml version=\"1.0\" encoding=\"utf-8\"?>\r\n<androidx.constraintlayout.widget.ConstraintLayout xmlns:android=\"http://schemas.android.com/apk/res/android\"\r\n    xmlns:app=\"http://schemas.android.com/apk/res-auto\"\r\n    xmlns:tools=\"http://schemas.android.com/tools\"\r\n    android:id=\"@+id/recording_page_tagSelectionView\"\r\n    android:layout_width=\"match_parent\"\r\n    android:layout_height=\"wrap_content\"\r\n    android:background=\"@drawable/dialog_bottom_sheet_background\"\r\n    android:paddingStart=\"15dp\"\r\n    android:paddingTop=\"10dp\"\r\n    android:paddingEnd=\"15dp\"\r\n    android:paddingBottom=\"25dp\">\r\n\r\n    <!-- 标题栏 -->\r\n    <View\r\n        android:id=\"@+id/drag_handle\"\r\n        android:layout_width=\"40dp\"\r\n        android:layout_height=\"4dp\"\r\n        android:layout_marginBottom=\"16dp\"\r\n        android:background=\"#DDDDDD\"\r\n        app:layout_constraintLeft_toLeftOf=\"parent\"\r\n        app:layout_constraintRight_toRightOf=\"parent\"\r\n        app:layout_constraintTop_toTopOf=\"parent\" />\r\n\r\n    <TextView\r\n        android:id=\"@+id/TagSelection_confirm\"\r\n        android:layout_width=\"wrap_content\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:layout_marginEnd=\"5dp\"\r\n        android:background=\"?attr/selectableItemBackground\"\r\n        android:padding=\"10dp\"\r\n        android:text=\"@string/确定\"\r\n        android:textColor=\"@color/HuaQing\"\r\n        android:textSize=\"16sp\"\r\n        android:textStyle=\"bold\"\r\n        app:layout_constraintRight_toRightOf=\"parent\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/drag_handle\" />\r\n\r\n    <TextView\r\n        android:id=\"@+id/TagSelection_cancel\"\r\n        android:layout_width=\"wrap_content\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:layout_marginStart=\"5dp\"\r\n        android:background=\"?attr/selectableItemBackground\"\r\n        android:padding=\"10dp\"\r\n        android:text=\"@string/取消\"\r\n        android:textColor=\"#666666\"\r\n        android:textSize=\"16sp\"\r\n        app:layout_constraintBottom_toBottomOf=\"@id/TagSelection_confirm\"\r\n        app:layout_constraintLeft_toLeftOf=\"parent\"\r\n        app:layout_constraintTop_toTopOf=\"@id/TagSelection_confirm\" />\r\n\r\n    <!-- 已选择标签显示区域 -->\r\n    <LinearLayout\r\n        android:id=\"@+id/TagSelection_container\"\r\n        android:layout_width=\"match_parent\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:orientation=\"vertical\"\r\n        android:visibility=\"gone\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/TagSelection_confirm\">\r\n\r\n        <TextView\r\n            android:layout_width=\"wrap_content\"\r\n            android:layout_height=\"wrap_content\"\r\n            android:text=\"已选择：\"\r\n            android:textSize=\"14sp\"\r\n            android:textColor=\"@color/grey\"\r\n            android:layout_marginBottom=\"8dp\" />\r\n\r\n        <com.google.android.flexbox.FlexboxLayout\r\n            android:id=\"@+id/flexbox_selected_tags\"\r\n            android:layout_width=\"match_parent\"\r\n            android:layout_height=\"wrap_content\"\r\n            app:flexWrap=\"wrap\"\r\n            app:alignItems=\"flex_start\"\r\n            app:justifyContent=\"flex_start\"\r\n            android:layout_marginBottom=\"16dp\" />\r\n\r\n    </LinearLayout>\r\n\r\n    <!-- 标签列表 -->\r\n    <androidx.recyclerview.widget.RecyclerView\r\n        android:id=\"@+id/rv_TagSelection_categories\"\r\n        android:layout_width=\"match_parent\"\r\n        android:layout_height=\"0dp\"\r\n        android:layout_weight=\"1\"\r\n        android:maxHeight=\"400dp\"\r\n        tools:listitem=\"@layout/item_tag_category\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/TagSelection_container\" />\r\n\r\n    <TextView\r\n        android:id=\"@+id/tv_TagSelection_management\"\r\n        android:layout_width=\"wrap_content\"\r\n        android:layout_height=\"wrap_content\"\r\n        android:text=\"@string/管理标签\"\r\n        android:textSize=\"14sp\"\r\n        android:textColor=\"@color/grey\"\r\n        android:gravity=\"center_horizontal\"\r\n        android:clickable=\"true\"\r\n        android:focusable=\"true\"\r\n        android:onClick=\"onManagementClick\"\r\n        app:layout_constraintTop_toBottomOf=\"@id/rv_TagSelection_categories\"\r\n        app:layout_constraintStart_toStartOf=\"parent\"\r\n        app:layout_constraintEnd_toEndOf=\"parent\"/>\r\n\r\n\r\n</androidx.constraintlayout.widget.ConstraintLayout>"}, {"path": "app\\src\\main\\res\\layout\\style_account_type_item_view.xml", "fsPath": "app\\src\\main\\res\\layout\\style_account_type_item_view.xml"}]}, "createdAt": *************, "isInternal": false}], "isAgent": false, "isNameGenerated": true, "isCustomAgent": false, "isUnitTestsAgent": false, "autoApply": false}}