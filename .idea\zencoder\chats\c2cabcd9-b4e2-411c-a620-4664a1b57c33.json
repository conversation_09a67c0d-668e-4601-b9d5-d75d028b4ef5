{"checksum": "56e500ab4ebe10961e3c908804548c89bca57a8b534625dd17efcbdf60b29ccd", "data": {"id": "c2cabcd9-b4e2-411c-a620-4664a1b57c33", "createdAt": 1750581713526, "updatedAt": 1750581757146, "title": "LiveData 线程错误解决", "messages": [{"id": "ecad9812-2a21-4016-80f2-51dcbfc91062", "role": "user", "content": [{"type": "text", "text": "运行报错FATAL EXCEPTION: pool-11-thread-1"}, {"type": "text", "text": "                                                                                                    Process: com.example.likeqianwang, PID: 3305"}, {"type": "text", "text": "                                                                                                    java.lang.IllegalStateException: Cannot invoke setValue on a background thread"}, {"type": "text", "text": "                                                                                                    \tat androidx.lifecycle.LiveData.assertMainThread(LiveData.java:502)"}, {"type": "text", "text": "                                                                                                    \tat androidx.lifecycle.LiveData.setValue(LiveData.java:306)"}, {"type": "text", "text": "                                                                                                    \tat androidx.lifecycle.MutableLiveData.setValue(MutableLiveData.java:50)"}, {"type": "text", "text": "                                                                                                    \tat com.example.likeqianwang.ViewModel.TransactionCategoryViewModel.$r8$lambda$Q4DMi4RH6SC6nRYcsFZPCyEjTlY(Unknown Source:0)"}, {"type": "text", "text": "                                                                                                    \tat com.example.likeqianwang.ViewModel.TransactionCategoryViewModel$$ExternalSyntheticLambda0.onCategoryLoaded(D8$$SyntheticClass:0)"}, {"type": "text", "text": "                                                                                                    \tat com.example.likeqianwang.Repository.TransactionCategoryRepository.lambda$getCategoryById$0(TransactionCategoryRepository.java:43)"}, {"type": "text", "text": "                                                                                                    \tat com.example.likeqianwang.Repository.TransactionCategoryRepository.$r8$lambda$u7QZ2qcY_N_7XcWGzPnNTgqAmUY(Unknown Source:0)"}, {"type": "text", "text": "                                                                                                    \tat com.example.likeqianwang.Repository.TransactionCategoryRepository$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)"}, {"type": "text", "text": "                                                                                                    \tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)"}, {"type": "text", "text": "                                                                                                    \tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)"}, {"type": "text", "text": "                                                                                                    \tat java.lang.Thread.run(Thread.java:1012)"}], "context": {"currentFile": {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\widgets\\Widget_Budget.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\widgets\\Widget_Budget.java", "sources": ["opened_tab"], "content": "package com.example.likeqianwang.widgets;\n\nimport android.graphics.Color;\nimport android.os.Bundle;\nimport android.util.Log;\nimport android.view.LayoutInflater;\nimport android.view.View;\nimport android.view.ViewGroup;\nimport android.widget.ProgressBar;\nimport android.widget.TextView;\n\nimport androidx.annotation.NonNull;\nimport androidx.annotation.Nullable;\nimport androidx.fragment.app.Fragment;\nimport androidx.lifecycle.ViewModelProvider;\n\nimport com.example.likeqianwang.Dao.BudgetDao;\nimport com.example.likeqianwang.Entity.Budget;\nimport com.example.likeqianwang.R;\nimport com.example.likeqianwang.ViewModel.BudgetViewModel;\n\nimport java.math.BigDecimal;\nimport java.text.DecimalFormat;\nimport java.util.Calendar;\nimport java.util.List;\n\npublic class Widget_Budget extends Fragment {\n    private BudgetViewModel budgetViewModel;\n    private DecimalFormat amountFormatter;\n\n    // UI组件\n    private TextView remainingBudgetLabel;\n    private TextView remainingBudgetAmount;\n    private TextView usedBudgetLabel;\n    private TextView usagePercentage;\n    private ProgressBar budgetProgressBar;\n    private TextView usedAmount;\n    private TextView totalBudgetAmount;\n    private View statusIndicator;\n    private TextView statusText;\n\n    @Override\n    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,\n                             Bundle savedInstanceState) {\n        return inflater.inflate(R.layout.widget_budget, container, false);\n    }\n\n    @Override\n    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {\n        super.onViewCreated(view, savedInstanceState);\n\n        initViews(view);\n        initViewModel();\n        observeBudgetData();\n    }\n\n    private void initViews(View view) {\n        remainingBudgetLabel = view.findViewById(R.id.widget_budget_剩余预算);\n        remainingBudgetAmount = view.findViewById(R.id.widget_budget_剩余预算金额);\n        usedBudgetLabel = view.findViewById(R.id.widget_budget_已用预算);\n        usagePercentage = view.findViewById(R.id.widget_budget_使用百分比);\n        budgetProgressBar = view.findViewById(R.id.widget_budget_进度条);\n        usedAmount = view.findViewById(R.id.widget_budget_已用金额);\n        totalBudgetAmount = view.findViewById(R.id.widget_budget_总预算金额);\n        statusIndicator = view.findViewById(R.id.widget_budget_状态指示器);\n        statusText = view.findViewById(R.id.widget_budget_状态文本);\n\n        // 初始化格式化工具\n        amountFormatter = new DecimalFormat(\"¥#,##0.00\");\n\n        // 初始化进度条\n        initProgressBar();\n    }\n\n    private void initViewModel() {\n        budgetViewModel = new ViewModelProvider(requireActivity()).get(BudgetViewModel.class);\n    }\n\n    private void initProgressBar() {\n        // 设置进度条的基本属性\n        budgetProgressBar.setMax(100);\n        budgetProgressBar.setProgress(0);\n\n        // 决定是否使用自定义drawable\n        boolean useCustomDrawable = true; // 设置为false以使用默认样式\n\n        if (useCustomDrawable) {\n            // 使用兼容的方式设置自定义drawable\n            try {\n                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {\n                    budgetProgressBar.setProgressDrawable(getResources().getDrawable(R.drawable.budget_progress_bar, getContext().getTheme()));\n                } else {\n                    budgetProgressBar.setProgressDrawable(getResources().getDrawable(R.drawable.budget_progress_bar));\n                }\n                Log.d(\"BudgetWidget\", \"Custom progress bar drawable set successfully\");\n            } catch (Exception e) {\n                Log.e(\"BudgetWidget\", \"Failed to set custom drawable, using default\", e);\n                // 如果设置自定义drawable失败，保持默认样式\n            }\n        } else {\n            Log.d(\"BudgetWidget\", \"Using default progress bar style\");\n        }\n\n        Log.d(\"BudgetWidget\", \"Progress bar initialized\");\n    }\n\n    private void observeBudgetData() {\n        // 获取当前时期\n        BudgetViewModel.CurrentPeriod currentPeriod = BudgetViewModel.getCurrentPeriod();\n\n        // 观察总预算\n        budgetViewModel.getTotalBudget(\"MONTHLY\", currentPeriod.year, currentPeriod.month)\n                .observe(getViewLifecycleOwner(), this::updateTotalBudgetDisplay);\n\n        // 观察预算使用情况\n        budgetViewModel.getBudgetUsage(\"MONTHLY\", currentPeriod.year, currentPeriod.month)\n                .observe(getViewLifecycleOwner(), this::updateBudgetUsageDisplay);\n    }\n\n    private void updateTotalBudgetDisplay(Budget totalBudget) {\n        if (totalBudget != null) {\n            totalBudgetAmount.setText(amountFormatter.format(totalBudget.getBudgetAmount()));\n        } else {\n            // 没有设置总预算时显示默认信息\n            totalBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            remainingBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usedAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usagePercentage.setText(\"0%\");\n\n            // 重置进度条\n            budgetProgressBar.setProgress(0);\n            budgetProgressBar.getProgressDrawable().setColorFilter(Color.GRAY, android.graphics.PorterDuff.Mode.SRC_IN);\n\n            updateStatusDisplay(\"未设置预算\", Color.GRAY);\n        }\n    }\n\n    private void updateBudgetUsageDisplay(List<BudgetDao.BudgetUsage> budgetUsages) {\n        if (budgetUsages == null || budgetUsages.isEmpty()) {\n            // 没有预算使用数据\n            remainingBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usedAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usagePercentage.setText(\"0%\");\n            budgetProgressBar.setProgress(0);\n            updateStatusDisplay(\"无数据\", Color.GRAY);\n            return;\n        }\n\n        // 查找总预算的使用情况\n        BudgetDao.BudgetUsage totalBudgetUsage = null;\n        for (BudgetDao.BudgetUsage usage : budgetUsages) {\n            if (\"TOTAL\".equals(usage.budgetType)) {\n                totalBudgetUsage = usage;\n                break;\n            }\n        }\n\n        if (totalBudgetUsage != null) {\n            updateBudgetDisplay(totalBudgetUsage);\n        } else {\n            // 如果没有总预算，计算所有分类预算的总和\n            calculateCombinedBudgetUsage(budgetUsages);\n        }\n    }\n\n    private void updateBudgetDisplay(BudgetDao.BudgetUsage budgetUsage) {\n        BigDecimal budgetAmount = budgetUsage.budgetAmount != null ? budgetUsage.budgetAmount : BigDecimal.ZERO;\n        BigDecimal usedAmountValue = budgetUsage.usedAmount != null ? budgetUsage.usedAmount : BigDecimal.ZERO;\n        BigDecimal remainingAmount = budgetAmount.subtract(usedAmountValue);\n\n        // 更新金额显示\n        remainingBudgetAmount.setText(amountFormatter.format(remainingAmount));\n        usedAmount.setText(amountFormatter.format(usedAmountValue));\n        totalBudgetAmount.setText(amountFormatter.format(budgetAmount));\n\n        // 更新百分比和进度条\n        double remainingPercentage = 100 - Math.min(budgetUsage.usagePercentage, 100);\n        int progressValue = (int) remainingPercentage;\n        Log.d(\"BudgetWidget\", \"Remaining percentage: \" + remainingPercentage + \", Progress value: \" + progressValue);\n        usagePercentage.setText(String.format(\"%.0f%%\", remainingPercentage));\n\n        // 更新进度条 - 使用更可靠的方法\n        updateProgressBar(progressValue, budgetUsage);\n\n        // 将\"已用预算\"改为\"剩余预算百分比\"\n        usedBudgetLabel.setText(\"剩余预算百分比\");\n\n        // 更新状态显示\n        BudgetViewModel.BudgetStatus status = BudgetViewModel.calculateBudgetStatus(\n                budgetAmount, usedAmountValue, budgetUsage.alertThreshold);\n        updateStatusDisplay(status.statusText, status.statusColor);\n    }\n\n    private void calculateCombinedBudgetUsage(List<BudgetDao.BudgetUsage> budgetUsages) {\n        BigDecimal totalBudgetAmount = BigDecimal.ZERO;\n        BigDecimal totalUsedAmount = BigDecimal.ZERO;\n        double averageAlertThreshold = 0.8; // 默认预警阈值\n\n        for (BudgetDao.BudgetUsage usage : budgetUsages) {\n            if (\"CATEGORY\".equals(usage.budgetType)) {\n                totalBudgetAmount = totalBudgetAmount.add(usage.budgetAmount != null ? usage.budgetAmount : BigDecimal.ZERO);\n                totalUsedAmount = totalUsedAmount.add(usage.usedAmount != null ? usage.usedAmount : BigDecimal.ZERO);\n            }\n        }\n\n        if (totalBudgetAmount.compareTo(BigDecimal.ZERO) > 0) {\n            // 创建虚拟的总预算使用情况\n            BudgetDao.BudgetUsage combinedUsage = new BudgetDao.BudgetUsage();\n            combinedUsage.budgetAmount = totalBudgetAmount;\n            combinedUsage.usedAmount = totalUsedAmount;\n            combinedUsage.usagePercentage = totalUsedAmount.divide(totalBudgetAmount, 4, BigDecimal.ROUND_HALF_UP)\n                    .multiply(new BigDecimal(\"100\")).doubleValue();\n            combinedUsage.alertThreshold = averageAlertThreshold;\n            combinedUsage.isOverBudget = totalUsedAmount.compareTo(totalBudgetAmount) > 0;\n\n            updateBudgetDisplay(combinedUsage);\n        } else {\n            // 没有任何预算设置\n            updateTotalBudgetDisplay(null);\n        }\n    }\n\n    private void updateProgressBar(int progressValue, BudgetDao.BudgetUsage budgetUsage) {\n        // 确保进度值在有效范围内\n        progressValue = Math.max(0, Math.min(100, progressValue));\n\n        Log.d(\"BudgetWidget\", \"Updating progress bar to: \" + progressValue);\n\n        // 更新颜色（仅当有自定义drawable时）\n        updateProgressBarColor(budgetUsage);\n\n        // 直接设置进度值\n        budgetProgressBar.setProgress(progressValue);\n\n        Log.d(\"BudgetWidget\", \"Progress bar updated successfully\");\n    }\n\n    private void updateProgressBarColor(BudgetDao.BudgetUsage budgetUsage) {\n        int color;\n        double remainingPercentage = 100 - Math.min(budgetUsage.usagePercentage, 100);\n\n        if (budgetUsage.isOverBudget) {\n            color = Color.RED;\n        } else if (remainingPercentage <= ((1 - budgetUsage.alertThreshold) * 100)) {\n            // 当剩余预算低于警戒线时（例如剩余不足20%）\n            color = Color.parseColor(\"#FF9800\"); // 橙色\n        } else {\n            color = Color.parseColor(\"#4CAF50\"); // 绿色\n        }\n\n        // 安全地更新进度条颜色\n        try {\n            if (budgetProgressBar.getProgressDrawable() != null) {\n                // 使用颜色过滤器更新颜色\n                budgetProgressBar.getProgressDrawable().setColorFilter(color, android.graphics.PorterDuff.Mode.SRC_IN);\n                Log.d(\"BudgetWidget\", \"Progress bar color updated to: \" + Integer.toHexString(color));\n            } else {\n                Log.w(\"BudgetWidget\", \"Progress drawable is null, cannot update color\");\n            }\n        } catch (Exception e) {\n            Log.e(\"BudgetWidget\", \"Failed to update progress bar color\", e);\n        }\n    }\n\n    private void createProgressBarDrawable(int progressColor) {\n        try {\n            // 获取屏幕密度用于dp转px\n            float density = getResources().getDisplayMetrics().density;\n            int cornerRadius = (int) (4 * density); // 4dp转换为像素\n\n            // 创建背景drawable\n            android.graphics.drawable.GradientDrawable backgroundDrawable = new android.graphics.drawable.GradientDrawable();\n            backgroundDrawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);\n            backgroundDrawable.setCornerRadius(cornerRadius);\n            backgroundDrawable.setColor(Color.parseColor(\"#E0E0E0\"));\n\n            // 创建进度drawable\n            android.graphics.drawable.GradientDrawable progressDrawable = new android.graphics.drawable.GradientDrawable();\n            progressDrawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);\n            progressDrawable.setCornerRadius(cornerRadius);\n            progressDrawable.setColor(progressColor);\n\n            // 创建ClipDrawable用于进度效果\n            android.graphics.drawable.ClipDrawable clipDrawable = new android.graphics.drawable.ClipDrawable(\n                    progressDrawable, android.view.Gravity.LEFT, android.graphics.drawable.ClipDrawable.HORIZONTAL);\n\n            // 创建LayerDrawable\n            android.graphics.drawable.LayerDrawable layerDrawable = new android.graphics.drawable.LayerDrawable(\n                    new android.graphics.drawable.Drawable[]{backgroundDrawable, clipDrawable});\n\n            layerDrawable.setId(0, android.R.id.background);\n            layerDrawable.setId(1, android.R.id.progress);\n\n            // 设置到进度条\n            budgetProgressBar.setProgressDrawable(layerDrawable);\n\n        } catch (Exception e) {\n            // 如果创建drawable失败，使用简单的颜色过滤器\n            Log.e(\"BudgetWidget\", \"Failed to create progress drawable\", e);\n            if (budgetProgressBar.getProgressDrawable() != null) {\n                budgetProgressBar.getProgressDrawable().setColorFilter(progressColor, android.graphics.PorterDuff.Mode.SRC_IN);\n            }\n        }\n    }\n\n    private void updateStatusDisplay(String status, int color) {\n        statusText.setText(status);\n        statusText.setTextColor(color);\n        statusIndicator.getBackground().setColorFilter(color, android.graphics.PorterDuff.Mode.SRC_IN);\n    }\n\n    @Override\n    public void onDestroyView() {\n        super.onDestroyView();\n    }\n}"}, "codebaseEnabled": true, "ragFiles": [{"path": "app/src/main/java/com/example/likeqianwang/Utils/SwipeToDeleteCallback.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Utils\\SwipeToDeleteCallback.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/widgets/Widget_Budget.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\widgets\\Widget_Budget.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ViewModel/TransactionCategoryViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionCategoryViewModel.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/dialogs/YearMonthPickerBottomSheetDialogFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\dialogs\\YearMonthPickerBottomSheetDialogFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/fill_new_account_info_credit_date_picker/CreditDatePickerDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\fill_new_account_info_credit_date_picker\\CreditDatePickerDialog.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ViewModel/TransactionInputViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionInputViewModel.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/main_stats/StatsViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\main_stats\\StatsViewModel.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/dialogs/RefundBottomSheetDialogFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\dialogs\\RefundBottomSheetDialogFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/dialogs/TransactionDetailBottomSheetDialogFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\dialogs\\TransactionDetailBottomSheetDialogFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ViewModel/ReceiptViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\ReceiptViewModel.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/recording_in/RecordingInFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\recording_in\\RecordingInFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/recording_out/RecordingOutFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\recording_out\\RecordingOutFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ViewModel/BudgetViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\BudgetViewModel.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/Repository/TransactionCategoryRepository.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Repository\\TransactionCategoryRepository.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/budget_settings/BudgetSettingsActivity.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\budget_settings\\BudgetSettingsActivity.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/Dao/TransactionCategoryDao.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Dao\\TransactionCategoryDao.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/Dao/TransactionSubcategoryDao.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Dao\\TransactionSubcategoryDao.java", "sources": ["keyword_search"]}, {"path": "app/build/generated/ap_generated_sources/debug/out/com/example/likeqianwang/Dao/TransactionSubcategoryDao_Impl.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\build\\generated\\ap_generated_sources\\debug\\out\\com\\example\\likeqianwang\\Dao\\TransactionSubcategoryDao_Impl.java", "sources": ["keyword_search"]}, {"path": "app/build/generated/ap_generated_sources/debug/out/com/example/likeqianwang/Dao/TransactionCategoryDao_Impl.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\build\\generated\\ap_generated_sources\\debug\\out\\com\\example\\likeqianwang\\Dao\\TransactionCategoryDao_Impl.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/Dao/TransactionDao.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Dao\\TransactionDao.java", "sources": ["keyword_search"]}, {"path": "FINAL_COMPILATION_SUCCESS.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\FINAL_COMPILATION_SUCCESS.md", "sources": []}, {"path": "DAO_METHOD_FIX.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\DAO_METHOD_FIX.md", "sources": []}, {"path": "COMPILATION_FIX_SUMMARY.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\COMPILATION_FIX_SUMMARY.md", "sources": []}, {"path": "TRANSFER_SAVE_SOLUTION.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\TRANSFER_SAVE_SOLUTION.md", "sources": []}, {"path": "INITIALIZER_VERIFICATION_GUIDE.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\INITIALIZER_VERIFICATION_GUIDE.md", "sources": []}, {"path": "DATABASE_INITIALIZER_SOLUTION.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\DATABASE_INITIALIZER_SOLUTION.md", "sources": []}, {"path": "TRANSFER_CATEGORY_FIX.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\TRANSFER_CATEGORY_FIX.md", "sources": []}, {"path": "TROUBLESHOOTING_GUIDE.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\TROUBLESHOOTING_GUIDE.md", "sources": []}, {"path": "TRANSFER_FIX_VERIFICATION.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\TRANSFER_FIX_VERIFICATION.md", "sources": []}, {"path": "TRANSFER_DEBUG_CHECKLIST.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\TRANSFER_DEBUG_CHECKLIST.md", "sources": []}, {"path": "app\\src\\main\\res\\layout\\item_account_category_in_wallets.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\item_account_category_in_wallets.xml", "sources": []}, {"path": "FOREIG<PERSON>_KEY_CONSTRAINT_FIX.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\FOREIGN_KEY_CONSTRAINT_FIX.md", "sources": []}, {"path": "app\\src\\main\\res\\layout\\fragment_wallet.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\fragment_wallet.xml", "sources": []}, {"path": "app\\src\\main\\res\\layout\\item_recording_in_out_category_list_view.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\item_recording_in_out_category_list_view.xml", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionCategoryViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionCategoryViewModel.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\Repository\\TransactionCategoryRepository.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Repository\\TransactionCategoryRepository.java", "sources": []}, {"path": "app/src/main/java/com/example/likeqianwang/widgets/Widget_Budget.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\widgets\\Widget_Budget.java", "sources": ["opened_tab"]}, {"path": "app/src/main/res/drawable/budget_progress_bar.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\budget_progress_bar.xml", "sources": ["opened_tab"]}, {"path": "app/src/main/res/layout/widget_budget.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\widget_budget.xml", "sources": ["opened_tab"]}, {"path": "app/src/main/res/drawable/budget_remaining_progress_bar.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\budget_remaining_progress_bar.xml", "sources": ["opened_tab"]}, {"path": "app/src/main/java/com/example/likeqianwang/Entity/Budget.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Entity\\Budget.java", "sources": ["opened_tab"]}, {"path": "app/src/main/java/com/example/likeqianwang/Dao/BudgetDao.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Dao\\BudgetDao.java", "sources": ["opened_tab"]}], "rerankedFiles": [{"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\Repository\\TransactionCategoryRepository.java", "fsPath": "app\\src\\main\\java\\com\\example\\likeqianwang\\Repository\\TransactionCategoryRepository.java"}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionCategoryViewModel.java", "fsPath": "app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionCategoryViewModel.java"}], "usedFilePaths": [{"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\widgets\\Widget_Budget.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\widgets\\Widget_Budget.java", "sources": ["opened_tab"], "content": "package com.example.likeqianwang.widgets;\n\nimport android.graphics.Color;\nimport android.os.Bundle;\nimport android.util.Log;\nimport android.view.LayoutInflater;\nimport android.view.View;\nimport android.view.ViewGroup;\nimport android.widget.ProgressBar;\nimport android.widget.TextView;\n\nimport androidx.annotation.NonNull;\nimport androidx.annotation.Nullable;\nimport androidx.fragment.app.Fragment;\nimport androidx.lifecycle.ViewModelProvider;\n\nimport com.example.likeqianwang.Dao.BudgetDao;\nimport com.example.likeqianwang.Entity.Budget;\nimport com.example.likeqianwang.R;\nimport com.example.likeqianwang.ViewModel.BudgetViewModel;\n\nimport java.math.BigDecimal;\nimport java.text.DecimalFormat;\nimport java.util.Calendar;\nimport java.util.List;\n\npublic class Widget_Budget extends Fragment {\n    private BudgetViewModel budgetViewModel;\n    private DecimalFormat amountFormatter;\n\n    // UI组件\n    private TextView remainingBudgetLabel;\n    private TextView remainingBudgetAmount;\n    private TextView usedBudgetLabel;\n    private TextView usagePercentage;\n    private ProgressBar budgetProgressBar;\n    private TextView usedAmount;\n    private TextView totalBudgetAmount;\n    private View statusIndicator;\n    private TextView statusText;\n\n    @Override\n    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,\n                             Bundle savedInstanceState) {\n        return inflater.inflate(R.layout.widget_budget, container, false);\n    }\n\n    @Override\n    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {\n        super.onViewCreated(view, savedInstanceState);\n\n        initViews(view);\n        initViewModel();\n        observeBudgetData();\n    }\n\n    private void initViews(View view) {\n        remainingBudgetLabel = view.findViewById(R.id.widget_budget_剩余预算);\n        remainingBudgetAmount = view.findViewById(R.id.widget_budget_剩余预算金额);\n        usedBudgetLabel = view.findViewById(R.id.widget_budget_已用预算);\n        usagePercentage = view.findViewById(R.id.widget_budget_使用百分比);\n        budgetProgressBar = view.findViewById(R.id.widget_budget_进度条);\n        usedAmount = view.findViewById(R.id.widget_budget_已用金额);\n        totalBudgetAmount = view.findViewById(R.id.widget_budget_总预算金额);\n        statusIndicator = view.findViewById(R.id.widget_budget_状态指示器);\n        statusText = view.findViewById(R.id.widget_budget_状态文本);\n\n        // 初始化格式化工具\n        amountFormatter = new DecimalFormat(\"¥#,##0.00\");\n\n        // 初始化进度条\n        initProgressBar();\n    }\n\n    private void initViewModel() {\n        budgetViewModel = new ViewModelProvider(requireActivity()).get(BudgetViewModel.class);\n    }\n\n    private void initProgressBar() {\n        // 设置进度条的基本属性\n        budgetProgressBar.setMax(100);\n        budgetProgressBar.setProgress(0);\n\n        // 决定是否使用自定义drawable\n        boolean useCustomDrawable = true; // 设置为false以使用默认样式\n\n        if (useCustomDrawable) {\n            // 使用兼容的方式设置自定义drawable\n            try {\n                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {\n                    budgetProgressBar.setProgressDrawable(getResources().getDrawable(R.drawable.budget_progress_bar, getContext().getTheme()));\n                } else {\n                    budgetProgressBar.setProgressDrawable(getResources().getDrawable(R.drawable.budget_progress_bar));\n                }\n                Log.d(\"BudgetWidget\", \"Custom progress bar drawable set successfully\");\n            } catch (Exception e) {\n                Log.e(\"BudgetWidget\", \"Failed to set custom drawable, using default\", e);\n                // 如果设置自定义drawable失败，保持默认样式\n            }\n        } else {\n            Log.d(\"BudgetWidget\", \"Using default progress bar style\");\n        }\n\n        Log.d(\"BudgetWidget\", \"Progress bar initialized\");\n    }\n\n    private void observeBudgetData() {\n        // 获取当前时期\n        BudgetViewModel.CurrentPeriod currentPeriod = BudgetViewModel.getCurrentPeriod();\n\n        // 观察总预算\n        budgetViewModel.getTotalBudget(\"MONTHLY\", currentPeriod.year, currentPeriod.month)\n                .observe(getViewLifecycleOwner(), this::updateTotalBudgetDisplay);\n\n        // 观察预算使用情况\n        budgetViewModel.getBudgetUsage(\"MONTHLY\", currentPeriod.year, currentPeriod.month)\n                .observe(getViewLifecycleOwner(), this::updateBudgetUsageDisplay);\n    }\n\n    private void updateTotalBudgetDisplay(Budget totalBudget) {\n        if (totalBudget != null) {\n            totalBudgetAmount.setText(amountFormatter.format(totalBudget.getBudgetAmount()));\n        } else {\n            // 没有设置总预算时显示默认信息\n            totalBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            remainingBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usedAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usagePercentage.setText(\"0%\");\n\n            // 重置进度条\n            budgetProgressBar.setProgress(0);\n            budgetProgressBar.getProgressDrawable().setColorFilter(Color.GRAY, android.graphics.PorterDuff.Mode.SRC_IN);\n\n            updateStatusDisplay(\"未设置预算\", Color.GRAY);\n        }\n    }\n\n    private void updateBudgetUsageDisplay(List<BudgetDao.BudgetUsage> budgetUsages) {\n        if (budgetUsages == null || budgetUsages.isEmpty()) {\n            // 没有预算使用数据\n            remainingBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usedAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usagePercentage.setText(\"0%\");\n            budgetProgressBar.setProgress(0);\n            updateStatusDisplay(\"无数据\", Color.GRAY);\n            return;\n        }\n\n        // 查找总预算的使用情况\n        BudgetDao.BudgetUsage totalBudgetUsage = null;\n        for (BudgetDao.BudgetUsage usage : budgetUsages) {\n            if (\"TOTAL\".equals(usage.budgetType)) {\n                totalBudgetUsage = usage;\n                break;\n            }\n        }\n\n        if (totalBudgetUsage != null) {\n            updateBudgetDisplay(totalBudgetUsage);\n        } else {\n            // 如果没有总预算，计算所有分类预算的总和\n            calculateCombinedBudgetUsage(budgetUsages);\n        }\n    }\n\n    private void updateBudgetDisplay(BudgetDao.BudgetUsage budgetUsage) {\n        BigDecimal budgetAmount = budgetUsage.budgetAmount != null ? budgetUsage.budgetAmount : BigDecimal.ZERO;\n        BigDecimal usedAmountValue = budgetUsage.usedAmount != null ? budgetUsage.usedAmount : BigDecimal.ZERO;\n        BigDecimal remainingAmount = budgetAmount.subtract(usedAmountValue);\n\n        // 更新金额显示\n        remainingBudgetAmount.setText(amountFormatter.format(remainingAmount));\n        usedAmount.setText(amountFormatter.format(usedAmountValue));\n        totalBudgetAmount.setText(amountFormatter.format(budgetAmount));\n\n        // 更新百分比和进度条\n        double remainingPercentage = 100 - Math.min(budgetUsage.usagePercentage, 100);\n        int progressValue = (int) remainingPercentage;\n        Log.d(\"BudgetWidget\", \"Remaining percentage: \" + remainingPercentage + \", Progress value: \" + progressValue);\n        usagePercentage.setText(String.format(\"%.0f%%\", remainingPercentage));\n\n        // 更新进度条 - 使用更可靠的方法\n        updateProgressBar(progressValue, budgetUsage);\n\n        // 将\"已用预算\"改为\"剩余预算百分比\"\n        usedBudgetLabel.setText(\"剩余预算百分比\");\n\n        // 更新状态显示\n        BudgetViewModel.BudgetStatus status = BudgetViewModel.calculateBudgetStatus(\n                budgetAmount, usedAmountValue, budgetUsage.alertThreshold);\n        updateStatusDisplay(status.statusText, status.statusColor);\n    }\n\n    private void calculateCombinedBudgetUsage(List<BudgetDao.BudgetUsage> budgetUsages) {\n        BigDecimal totalBudgetAmount = BigDecimal.ZERO;\n        BigDecimal totalUsedAmount = BigDecimal.ZERO;\n        double averageAlertThreshold = 0.8; // 默认预警阈值\n\n        for (BudgetDao.BudgetUsage usage : budgetUsages) {\n            if (\"CATEGORY\".equals(usage.budgetType)) {\n                totalBudgetAmount = totalBudgetAmount.add(usage.budgetAmount != null ? usage.budgetAmount : BigDecimal.ZERO);\n                totalUsedAmount = totalUsedAmount.add(usage.usedAmount != null ? usage.usedAmount : BigDecimal.ZERO);\n            }\n        }\n\n        if (totalBudgetAmount.compareTo(BigDecimal.ZERO) > 0) {\n            // 创建虚拟的总预算使用情况\n            BudgetDao.BudgetUsage combinedUsage = new BudgetDao.BudgetUsage();\n            combinedUsage.budgetAmount = totalBudgetAmount;\n            combinedUsage.usedAmount = totalUsedAmount;\n            combinedUsage.usagePercentage = totalUsedAmount.divide(totalBudgetAmount, 4, BigDecimal.ROUND_HALF_UP)\n                    .multiply(new BigDecimal(\"100\")).doubleValue();\n            combinedUsage.alertThreshold = averageAlertThreshold;\n            combinedUsage.isOverBudget = totalUsedAmount.compareTo(totalBudgetAmount) > 0;\n\n            updateBudgetDisplay(combinedUsage);\n        } else {\n            // 没有任何预算设置\n            updateTotalBudgetDisplay(null);\n        }\n    }\n\n    private void updateProgressBar(int progressValue, BudgetDao.BudgetUsage budgetUsage) {\n        // 确保进度值在有效范围内\n        progressValue = Math.max(0, Math.min(100, progressValue));\n\n        Log.d(\"BudgetWidget\", \"Updating progress bar to: \" + progressValue);\n\n        // 更新颜色（仅当有自定义drawable时）\n        updateProgressBarColor(budgetUsage);\n\n        // 直接设置进度值\n        budgetProgressBar.setProgress(progressValue);\n\n        Log.d(\"BudgetWidget\", \"Progress bar updated successfully\");\n    }\n\n    private void updateProgressBarColor(BudgetDao.BudgetUsage budgetUsage) {\n        int color;\n        double remainingPercentage = 100 - Math.min(budgetUsage.usagePercentage, 100);\n\n        if (budgetUsage.isOverBudget) {\n            color = Color.RED;\n        } else if (remainingPercentage <= ((1 - budgetUsage.alertThreshold) * 100)) {\n            // 当剩余预算低于警戒线时（例如剩余不足20%）\n            color = Color.parseColor(\"#FF9800\"); // 橙色\n        } else {\n            color = Color.parseColor(\"#4CAF50\"); // 绿色\n        }\n\n        // 安全地更新进度条颜色\n        try {\n            if (budgetProgressBar.getProgressDrawable() != null) {\n                // 使用颜色过滤器更新颜色\n                budgetProgressBar.getProgressDrawable().setColorFilter(color, android.graphics.PorterDuff.Mode.SRC_IN);\n                Log.d(\"BudgetWidget\", \"Progress bar color updated to: \" + Integer.toHexString(color));\n            } else {\n                Log.w(\"BudgetWidget\", \"Progress drawable is null, cannot update color\");\n            }\n        } catch (Exception e) {\n            Log.e(\"BudgetWidget\", \"Failed to update progress bar color\", e);\n        }\n    }\n\n    private void createProgressBarDrawable(int progressColor) {\n        try {\n            // 获取屏幕密度用于dp转px\n            float density = getResources().getDisplayMetrics().density;\n            int cornerRadius = (int) (4 * density); // 4dp转换为像素\n\n            // 创建背景drawable\n            android.graphics.drawable.GradientDrawable backgroundDrawable = new android.graphics.drawable.GradientDrawable();\n            backgroundDrawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);\n            backgroundDrawable.setCornerRadius(cornerRadius);\n            backgroundDrawable.setColor(Color.parseColor(\"#E0E0E0\"));\n\n            // 创建进度drawable\n            android.graphics.drawable.GradientDrawable progressDrawable = new android.graphics.drawable.GradientDrawable();\n            progressDrawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);\n            progressDrawable.setCornerRadius(cornerRadius);\n            progressDrawable.setColor(progressColor);\n\n            // 创建ClipDrawable用于进度效果\n            android.graphics.drawable.ClipDrawable clipDrawable = new android.graphics.drawable.ClipDrawable(\n                    progressDrawable, android.view.Gravity.LEFT, android.graphics.drawable.ClipDrawable.HORIZONTAL);\n\n            // 创建LayerDrawable\n            android.graphics.drawable.LayerDrawable layerDrawable = new android.graphics.drawable.LayerDrawable(\n                    new android.graphics.drawable.Drawable[]{backgroundDrawable, clipDrawable});\n\n            layerDrawable.setId(0, android.R.id.background);\n            layerDrawable.setId(1, android.R.id.progress);\n\n            // 设置到进度条\n            budgetProgressBar.setProgressDrawable(layerDrawable);\n\n        } catch (Exception e) {\n            // 如果创建drawable失败，使用简单的颜色过滤器\n            Log.e(\"BudgetWidget\", \"Failed to create progress drawable\", e);\n            if (budgetProgressBar.getProgressDrawable() != null) {\n                budgetProgressBar.getProgressDrawable().setColorFilter(progressColor, android.graphics.PorterDuff.Mode.SRC_IN);\n            }\n        }\n    }\n\n    private void updateStatusDisplay(String status, int color) {\n        statusText.setText(status);\n        statusText.setTextColor(color);\n        statusIndicator.getBackground().setColorFilter(color, android.graphics.PorterDuff.Mode.SRC_IN);\n    }\n\n    @Override\n    public void onDestroyView() {\n        super.onDestroyView();\n    }\n}"}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\Repository\\TransactionCategoryRepository.java", "fsPath": "app\\src\\main\\java\\com\\example\\likeqianwang\\Repository\\TransactionCategoryRepository.java"}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionCategoryViewModel.java", "fsPath": "app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionCategoryViewModel.java"}]}, "createdAt": 1750581713529, "isInternal": false, "rawContent": {"type": "doc", "content": [{"type": "paragraph", "content": [{"type": "text", "text": "运行报错FATAL EXCEPTION: pool-11-thread-1"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "                                                                                                    Process: com.example.likeqianwang, PID: 3305"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "                                                                                                    java.lang.IllegalStateException: Cannot invoke setValue on a background thread"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "                                                                                                    \tat androidx.lifecycle.LiveData.assertMainThread(LiveData.java:502)"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "                                                                                                    \tat androidx.lifecycle.LiveData.setValue(LiveData.java:306)"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "                                                                                                    \tat androidx.lifecycle.MutableLiveData.setValue(MutableLiveData.java:50)"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "                                                                                                    \tat com.example.likeqianwang.ViewModel.TransactionCategoryViewModel.$r8$lambda$Q4DMi4RH6SC6nRYcsFZPCyEjTlY(Unknown Source:0)"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "                                                                                                    \tat com.example.likeqianwang.ViewModel.TransactionCategoryViewModel$$ExternalSyntheticLambda0.onCategoryLoaded(D8$$SyntheticClass:0)"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "                                                                                                    \tat com.example.likeqianwang.Repository.TransactionCategoryRepository.lambda$getCategoryById$0(TransactionCategoryRepository.java:43)"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "                                                                                                    \tat com.example.likeqianwang.Repository.TransactionCategoryRepository.$r8$lambda$u7QZ2qcY_N_7XcWGzPnNTgqAmUY(Unknown Source:0)"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "                                                                                                    \tat com.example.likeqianwang.Repository.TransactionCategoryRepository$$ExternalSyntheticLambda0.run(D8$$SyntheticClass:0)"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "                                                                                                    \tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1145)"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "                                                                                                    \tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:644)"}]}, {"type": "paragraph", "content": [{"type": "text", "text": "                                                                                                    \tat java.lang.Thread.run(Thread.java:1012)"}]}]}}, {"id": "f58857de-0b34-426f-8f10-cb967aa6a839", "role": "assistant", "content": [{"type": "text", "text": "# 错误分析：在后台线程上调用 LiveData.setValue\n\n## 问题描述\n\n您遇到的错误是：\n\n```\njava.lang.IllegalStateException: Cannot invoke setValue on a background thread\n```\n\n这个错误发生在 `TransactionCategoryViewModel` 类中，当您尝试在后台线程上调用 `LiveData.setValue()` 方法时。\n\n## 错误原因\n\n根据错误堆栈，问题出在以下位置：\n\n1. `TransactionCategoryViewModel.getCategoryById()` 方法中，您直接将 `categoryLiveData::setValue` 作为回调传递给了 `repository.getCategoryById()`\n2. 在 `TransactionCategoryRepository.getCategoryById()` 方法中，您在后台线程（`executorService`）中执行查询并调用了回调\n3. 这导致 `setValue()` 方法在后台线程上被调用，而 LiveData 的 `setValue()` 方法必须在主线程上调用\n\n## 解决方案\n\n您需要将 `setValue()` 调用替换为 `postValue()`，或者使用 `Handler` 将操作切换到主线程。以下是两种解决方案：\n\n### 方案1：使用 postValue() 代替 setValue()\n\n修改 `TransactionCategoryViewModel.java` 中的 `getCategoryById()` 方法：\n\n```java\npublic LiveData<TransactionCategory> getCategoryById(long categoryId) {\n    MutableLiveData<TransactionCategory> categoryLiveData = new MutableLiveData<>();\n    repository.getCategoryById(categoryId, categoryLiveData::postValue);  // 使用 postValue 代替 setValue\n    return categoryLiveData;\n}\n```\n\n### 方案2：在 Repository 中使用 Handler 切换到主线程\n\n修改 `TransactionCategoryRepository.java` 中的 `getCategoryById()` 方法：\n\n```java\npublic void getCategoryById(long categoryId, OnCategoryLoadedListener listener) {\n    executorService.execute(() -> {\n        TransactionCategory category = categoryDao.getCategoryByIdSync(categoryId);\n        // 使用 Handler 切换到主线程\n        new Handler(Looper.getMainLooper()).post(() -> {\n            listener.onCategoryLoaded(category);\n        });\n    });\n}\n```\n\n## 最佳实践建议\n\n1. 对于 LiveData 操作，记住：\n   - `setValue()` 必须在主线程调用\n   - `postValue()` 可以在任何线程调用\n\n2. 在 ViewModel 中处理线程切换是一个好习惯，这样 Repository 层可以专注于数据操作而不必关心线程问题\n\n3. 考虑使用 Kotlin 协程或 RxJava 来简化异步操作和线程切换\n\n## 其他可能的改进\n\n您可以考虑重构 Repository 和 ViewModel 之间的交互方式，例如：\n\n```java\n// 在 Repository 中直接返回 LiveData\npublic LiveData<TransactionCategory> getCategoryById(long categoryId) {\n    MutableLiveData<TransactionCategory> result = new MutableLiveData<>();\n    executorService.execute(() -> {\n        TransactionCategory category = categoryDao.getCategoryByIdSync(categoryId);\n        result.postValue(category);\n    });\n    return result;\n}\n\n// 在 ViewModel 中简化调用\npublic LiveData<TransactionCategory> getCategoryById(long categoryId) {\n    return repository.getCategoryById(categoryId);\n}\n```\n\n希望这些解决方案能帮助您解决问题！"}], "context": {"currentFile": {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\widgets\\Widget_Budget.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\widgets\\Widget_Budget.java", "sources": ["opened_tab"], "content": "package com.example.likeqianwang.widgets;\n\nimport android.graphics.Color;\nimport android.os.Bundle;\nimport android.util.Log;\nimport android.view.LayoutInflater;\nimport android.view.View;\nimport android.view.ViewGroup;\nimport android.widget.ProgressBar;\nimport android.widget.TextView;\n\nimport androidx.annotation.NonNull;\nimport androidx.annotation.Nullable;\nimport androidx.fragment.app.Fragment;\nimport androidx.lifecycle.ViewModelProvider;\n\nimport com.example.likeqianwang.Dao.BudgetDao;\nimport com.example.likeqianwang.Entity.Budget;\nimport com.example.likeqianwang.R;\nimport com.example.likeqianwang.ViewModel.BudgetViewModel;\n\nimport java.math.BigDecimal;\nimport java.text.DecimalFormat;\nimport java.util.Calendar;\nimport java.util.List;\n\npublic class Widget_Budget extends Fragment {\n    private BudgetViewModel budgetViewModel;\n    private DecimalFormat amountFormatter;\n\n    // UI组件\n    private TextView remainingBudgetLabel;\n    private TextView remainingBudgetAmount;\n    private TextView usedBudgetLabel;\n    private TextView usagePercentage;\n    private ProgressBar budgetProgressBar;\n    private TextView usedAmount;\n    private TextView totalBudgetAmount;\n    private View statusIndicator;\n    private TextView statusText;\n\n    @Override\n    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,\n                             Bundle savedInstanceState) {\n        return inflater.inflate(R.layout.widget_budget, container, false);\n    }\n\n    @Override\n    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {\n        super.onViewCreated(view, savedInstanceState);\n\n        initViews(view);\n        initViewModel();\n        observeBudgetData();\n    }\n\n    private void initViews(View view) {\n        remainingBudgetLabel = view.findViewById(R.id.widget_budget_剩余预算);\n        remainingBudgetAmount = view.findViewById(R.id.widget_budget_剩余预算金额);\n        usedBudgetLabel = view.findViewById(R.id.widget_budget_已用预算);\n        usagePercentage = view.findViewById(R.id.widget_budget_使用百分比);\n        budgetProgressBar = view.findViewById(R.id.widget_budget_进度条);\n        usedAmount = view.findViewById(R.id.widget_budget_已用金额);\n        totalBudgetAmount = view.findViewById(R.id.widget_budget_总预算金额);\n        statusIndicator = view.findViewById(R.id.widget_budget_状态指示器);\n        statusText = view.findViewById(R.id.widget_budget_状态文本);\n\n        // 初始化格式化工具\n        amountFormatter = new DecimalFormat(\"¥#,##0.00\");\n\n        // 初始化进度条\n        initProgressBar();\n    }\n\n    private void initViewModel() {\n        budgetViewModel = new ViewModelProvider(requireActivity()).get(BudgetViewModel.class);\n    }\n\n    private void initProgressBar() {\n        // 设置进度条的基本属性\n        budgetProgressBar.setMax(100);\n        budgetProgressBar.setProgress(0);\n\n        // 决定是否使用自定义drawable\n        boolean useCustomDrawable = true; // 设置为false以使用默认样式\n\n        if (useCustomDrawable) {\n            // 使用兼容的方式设置自定义drawable\n            try {\n                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {\n                    budgetProgressBar.setProgressDrawable(getResources().getDrawable(R.drawable.budget_progress_bar, getContext().getTheme()));\n                } else {\n                    budgetProgressBar.setProgressDrawable(getResources().getDrawable(R.drawable.budget_progress_bar));\n                }\n                Log.d(\"BudgetWidget\", \"Custom progress bar drawable set successfully\");\n            } catch (Exception e) {\n                Log.e(\"BudgetWidget\", \"Failed to set custom drawable, using default\", e);\n                // 如果设置自定义drawable失败，保持默认样式\n            }\n        } else {\n            Log.d(\"BudgetWidget\", \"Using default progress bar style\");\n        }\n\n        Log.d(\"BudgetWidget\", \"Progress bar initialized\");\n    }\n\n    private void observeBudgetData() {\n        // 获取当前时期\n        BudgetViewModel.CurrentPeriod currentPeriod = BudgetViewModel.getCurrentPeriod();\n\n        // 观察总预算\n        budgetViewModel.getTotalBudget(\"MONTHLY\", currentPeriod.year, currentPeriod.month)\n                .observe(getViewLifecycleOwner(), this::updateTotalBudgetDisplay);\n\n        // 观察预算使用情况\n        budgetViewModel.getBudgetUsage(\"MONTHLY\", currentPeriod.year, currentPeriod.month)\n                .observe(getViewLifecycleOwner(), this::updateBudgetUsageDisplay);\n    }\n\n    private void updateTotalBudgetDisplay(Budget totalBudget) {\n        if (totalBudget != null) {\n            totalBudgetAmount.setText(amountFormatter.format(totalBudget.getBudgetAmount()));\n        } else {\n            // 没有设置总预算时显示默认信息\n            totalBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            remainingBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usedAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usagePercentage.setText(\"0%\");\n\n            // 重置进度条\n            budgetProgressBar.setProgress(0);\n            budgetProgressBar.getProgressDrawable().setColorFilter(Color.GRAY, android.graphics.PorterDuff.Mode.SRC_IN);\n\n            updateStatusDisplay(\"未设置预算\", Color.GRAY);\n        }\n    }\n\n    private void updateBudgetUsageDisplay(List<BudgetDao.BudgetUsage> budgetUsages) {\n        if (budgetUsages == null || budgetUsages.isEmpty()) {\n            // 没有预算使用数据\n            remainingBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usedAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usagePercentage.setText(\"0%\");\n            budgetProgressBar.setProgress(0);\n            updateStatusDisplay(\"无数据\", Color.GRAY);\n            return;\n        }\n\n        // 查找总预算的使用情况\n        BudgetDao.BudgetUsage totalBudgetUsage = null;\n        for (BudgetDao.BudgetUsage usage : budgetUsages) {\n            if (\"TOTAL\".equals(usage.budgetType)) {\n                totalBudgetUsage = usage;\n                break;\n            }\n        }\n\n        if (totalBudgetUsage != null) {\n            updateBudgetDisplay(totalBudgetUsage);\n        } else {\n            // 如果没有总预算，计算所有分类预算的总和\n            calculateCombinedBudgetUsage(budgetUsages);\n        }\n    }\n\n    private void updateBudgetDisplay(BudgetDao.BudgetUsage budgetUsage) {\n        BigDecimal budgetAmount = budgetUsage.budgetAmount != null ? budgetUsage.budgetAmount : BigDecimal.ZERO;\n        BigDecimal usedAmountValue = budgetUsage.usedAmount != null ? budgetUsage.usedAmount : BigDecimal.ZERO;\n        BigDecimal remainingAmount = budgetAmount.subtract(usedAmountValue);\n\n        // 更新金额显示\n        remainingBudgetAmount.setText(amountFormatter.format(remainingAmount));\n        usedAmount.setText(amountFormatter.format(usedAmountValue));\n        totalBudgetAmount.setText(amountFormatter.format(budgetAmount));\n\n        // 更新百分比和进度条\n        double remainingPercentage = 100 - Math.min(budgetUsage.usagePercentage, 100);\n        int progressValue = (int) remainingPercentage;\n        Log.d(\"BudgetWidget\", \"Remaining percentage: \" + remainingPercentage + \", Progress value: \" + progressValue);\n        usagePercentage.setText(String.format(\"%.0f%%\", remainingPercentage));\n\n        // 更新进度条 - 使用更可靠的方法\n        updateProgressBar(progressValue, budgetUsage);\n\n        // 将\"已用预算\"改为\"剩余预算百分比\"\n        usedBudgetLabel.setText(\"剩余预算百分比\");\n\n        // 更新状态显示\n        BudgetViewModel.BudgetStatus status = BudgetViewModel.calculateBudgetStatus(\n                budgetAmount, usedAmountValue, budgetUsage.alertThreshold);\n        updateStatusDisplay(status.statusText, status.statusColor);\n    }\n\n    private void calculateCombinedBudgetUsage(List<BudgetDao.BudgetUsage> budgetUsages) {\n        BigDecimal totalBudgetAmount = BigDecimal.ZERO;\n        BigDecimal totalUsedAmount = BigDecimal.ZERO;\n        double averageAlertThreshold = 0.8; // 默认预警阈值\n\n        for (BudgetDao.BudgetUsage usage : budgetUsages) {\n            if (\"CATEGORY\".equals(usage.budgetType)) {\n                totalBudgetAmount = totalBudgetAmount.add(usage.budgetAmount != null ? usage.budgetAmount : BigDecimal.ZERO);\n                totalUsedAmount = totalUsedAmount.add(usage.usedAmount != null ? usage.usedAmount : BigDecimal.ZERO);\n            }\n        }\n\n        if (totalBudgetAmount.compareTo(BigDecimal.ZERO) > 0) {\n            // 创建虚拟的总预算使用情况\n            BudgetDao.BudgetUsage combinedUsage = new BudgetDao.BudgetUsage();\n            combinedUsage.budgetAmount = totalBudgetAmount;\n            combinedUsage.usedAmount = totalUsedAmount;\n            combinedUsage.usagePercentage = totalUsedAmount.divide(totalBudgetAmount, 4, BigDecimal.ROUND_HALF_UP)\n                    .multiply(new BigDecimal(\"100\")).doubleValue();\n            combinedUsage.alertThreshold = averageAlertThreshold;\n            combinedUsage.isOverBudget = totalUsedAmount.compareTo(totalBudgetAmount) > 0;\n\n            updateBudgetDisplay(combinedUsage);\n        } else {\n            // 没有任何预算设置\n            updateTotalBudgetDisplay(null);\n        }\n    }\n\n    private void updateProgressBar(int progressValue, BudgetDao.BudgetUsage budgetUsage) {\n        // 确保进度值在有效范围内\n        progressValue = Math.max(0, Math.min(100, progressValue));\n\n        Log.d(\"BudgetWidget\", \"Updating progress bar to: \" + progressValue);\n\n        // 更新颜色（仅当有自定义drawable时）\n        updateProgressBarColor(budgetUsage);\n\n        // 直接设置进度值\n        budgetProgressBar.setProgress(progressValue);\n\n        Log.d(\"BudgetWidget\", \"Progress bar updated successfully\");\n    }\n\n    private void updateProgressBarColor(BudgetDao.BudgetUsage budgetUsage) {\n        int color;\n        double remainingPercentage = 100 - Math.min(budgetUsage.usagePercentage, 100);\n\n        if (budgetUsage.isOverBudget) {\n            color = Color.RED;\n        } else if (remainingPercentage <= ((1 - budgetUsage.alertThreshold) * 100)) {\n            // 当剩余预算低于警戒线时（例如剩余不足20%）\n            color = Color.parseColor(\"#FF9800\"); // 橙色\n        } else {\n            color = Color.parseColor(\"#4CAF50\"); // 绿色\n        }\n\n        // 安全地更新进度条颜色\n        try {\n            if (budgetProgressBar.getProgressDrawable() != null) {\n                // 使用颜色过滤器更新颜色\n                budgetProgressBar.getProgressDrawable().setColorFilter(color, android.graphics.PorterDuff.Mode.SRC_IN);\n                Log.d(\"BudgetWidget\", \"Progress bar color updated to: \" + Integer.toHexString(color));\n            } else {\n                Log.w(\"BudgetWidget\", \"Progress drawable is null, cannot update color\");\n            }\n        } catch (Exception e) {\n            Log.e(\"BudgetWidget\", \"Failed to update progress bar color\", e);\n        }\n    }\n\n    private void createProgressBarDrawable(int progressColor) {\n        try {\n            // 获取屏幕密度用于dp转px\n            float density = getResources().getDisplayMetrics().density;\n            int cornerRadius = (int) (4 * density); // 4dp转换为像素\n\n            // 创建背景drawable\n            android.graphics.drawable.GradientDrawable backgroundDrawable = new android.graphics.drawable.GradientDrawable();\n            backgroundDrawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);\n            backgroundDrawable.setCornerRadius(cornerRadius);\n            backgroundDrawable.setColor(Color.parseColor(\"#E0E0E0\"));\n\n            // 创建进度drawable\n            android.graphics.drawable.GradientDrawable progressDrawable = new android.graphics.drawable.GradientDrawable();\n            progressDrawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);\n            progressDrawable.setCornerRadius(cornerRadius);\n            progressDrawable.setColor(progressColor);\n\n            // 创建ClipDrawable用于进度效果\n            android.graphics.drawable.ClipDrawable clipDrawable = new android.graphics.drawable.ClipDrawable(\n                    progressDrawable, android.view.Gravity.LEFT, android.graphics.drawable.ClipDrawable.HORIZONTAL);\n\n            // 创建LayerDrawable\n            android.graphics.drawable.LayerDrawable layerDrawable = new android.graphics.drawable.LayerDrawable(\n                    new android.graphics.drawable.Drawable[]{backgroundDrawable, clipDrawable});\n\n            layerDrawable.setId(0, android.R.id.background);\n            layerDrawable.setId(1, android.R.id.progress);\n\n            // 设置到进度条\n            budgetProgressBar.setProgressDrawable(layerDrawable);\n\n        } catch (Exception e) {\n            // 如果创建drawable失败，使用简单的颜色过滤器\n            Log.e(\"BudgetWidget\", \"Failed to create progress drawable\", e);\n            if (budgetProgressBar.getProgressDrawable() != null) {\n                budgetProgressBar.getProgressDrawable().setColorFilter(progressColor, android.graphics.PorterDuff.Mode.SRC_IN);\n            }\n        }\n    }\n\n    private void updateStatusDisplay(String status, int color) {\n        statusText.setText(status);\n        statusText.setTextColor(color);\n        statusIndicator.getBackground().setColorFilter(color, android.graphics.PorterDuff.Mode.SRC_IN);\n    }\n\n    @Override\n    public void onDestroyView() {\n        super.onDestroyView();\n    }\n}"}, "codebaseEnabled": true, "ragFiles": [{"path": "app/src/main/java/com/example/likeqianwang/Utils/SwipeToDeleteCallback.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Utils\\SwipeToDeleteCallback.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/widgets/Widget_Budget.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\widgets\\Widget_Budget.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ViewModel/TransactionCategoryViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionCategoryViewModel.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/dialogs/YearMonthPickerBottomSheetDialogFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\dialogs\\YearMonthPickerBottomSheetDialogFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/fill_new_account_info_credit_date_picker/CreditDatePickerDialog.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\fill_new_account_info_credit_date_picker\\CreditDatePickerDialog.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ViewModel/TransactionInputViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionInputViewModel.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/main_stats/StatsViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\main_stats\\StatsViewModel.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/dialogs/RefundBottomSheetDialogFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\dialogs\\RefundBottomSheetDialogFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/dialogs/TransactionDetailBottomSheetDialogFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\dialogs\\TransactionDetailBottomSheetDialogFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ViewModel/ReceiptViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\ReceiptViewModel.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/recording_in/RecordingInFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\recording_in\\RecordingInFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/recording_out/RecordingOutFragment.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\recording_out\\RecordingOutFragment.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ViewModel/BudgetViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\BudgetViewModel.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/Repository/TransactionCategoryRepository.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Repository\\TransactionCategoryRepository.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/ui/budget_settings/BudgetSettingsActivity.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ui\\budget_settings\\BudgetSettingsActivity.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/Dao/TransactionCategoryDao.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Dao\\TransactionCategoryDao.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/Dao/TransactionSubcategoryDao.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Dao\\TransactionSubcategoryDao.java", "sources": ["keyword_search"]}, {"path": "app/build/generated/ap_generated_sources/debug/out/com/example/likeqianwang/Dao/TransactionSubcategoryDao_Impl.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\build\\generated\\ap_generated_sources\\debug\\out\\com\\example\\likeqianwang\\Dao\\TransactionSubcategoryDao_Impl.java", "sources": ["keyword_search"]}, {"path": "app/build/generated/ap_generated_sources/debug/out/com/example/likeqianwang/Dao/TransactionCategoryDao_Impl.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\build\\generated\\ap_generated_sources\\debug\\out\\com\\example\\likeqianwang\\Dao\\TransactionCategoryDao_Impl.java", "sources": ["keyword_search"]}, {"path": "app/src/main/java/com/example/likeqianwang/Dao/TransactionDao.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Dao\\TransactionDao.java", "sources": ["keyword_search"]}, {"path": "FINAL_COMPILATION_SUCCESS.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\FINAL_COMPILATION_SUCCESS.md", "sources": []}, {"path": "DAO_METHOD_FIX.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\DAO_METHOD_FIX.md", "sources": []}, {"path": "COMPILATION_FIX_SUMMARY.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\COMPILATION_FIX_SUMMARY.md", "sources": []}, {"path": "TRANSFER_SAVE_SOLUTION.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\TRANSFER_SAVE_SOLUTION.md", "sources": []}, {"path": "INITIALIZER_VERIFICATION_GUIDE.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\INITIALIZER_VERIFICATION_GUIDE.md", "sources": []}, {"path": "DATABASE_INITIALIZER_SOLUTION.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\DATABASE_INITIALIZER_SOLUTION.md", "sources": []}, {"path": "TRANSFER_CATEGORY_FIX.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\TRANSFER_CATEGORY_FIX.md", "sources": []}, {"path": "TROUBLESHOOTING_GUIDE.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\TROUBLESHOOTING_GUIDE.md", "sources": []}, {"path": "TRANSFER_FIX_VERIFICATION.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\TRANSFER_FIX_VERIFICATION.md", "sources": []}, {"path": "TRANSFER_DEBUG_CHECKLIST.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\TRANSFER_DEBUG_CHECKLIST.md", "sources": []}, {"path": "app\\src\\main\\res\\layout\\item_account_category_in_wallets.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\item_account_category_in_wallets.xml", "sources": []}, {"path": "FOREIG<PERSON>_KEY_CONSTRAINT_FIX.md", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\FOREIGN_KEY_CONSTRAINT_FIX.md", "sources": []}, {"path": "app\\src\\main\\res\\layout\\fragment_wallet.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\fragment_wallet.xml", "sources": []}, {"path": "app\\src\\main\\res\\layout\\item_recording_in_out_category_list_view.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\item_recording_in_out_category_list_view.xml", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionCategoryViewModel.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionCategoryViewModel.java", "sources": []}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\Repository\\TransactionCategoryRepository.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Repository\\TransactionCategoryRepository.java", "sources": []}, {"path": "app/src/main/java/com/example/likeqianwang/widgets/Widget_Budget.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\widgets\\Widget_Budget.java", "sources": ["opened_tab"]}, {"path": "app/src/main/res/drawable/budget_progress_bar.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\budget_progress_bar.xml", "sources": ["opened_tab"]}, {"path": "app/src/main/res/layout/widget_budget.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\layout\\widget_budget.xml", "sources": ["opened_tab"]}, {"path": "app/src/main/res/drawable/budget_remaining_progress_bar.xml", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\res\\drawable\\budget_remaining_progress_bar.xml", "sources": ["opened_tab"]}, {"path": "app/src/main/java/com/example/likeqianwang/Entity/Budget.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Entity\\Budget.java", "sources": ["opened_tab"]}, {"path": "app/src/main/java/com/example/likeqianwang/Dao/BudgetDao.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\Dao\\BudgetDao.java", "sources": ["opened_tab"]}], "rerankedFiles": [{"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\Repository\\TransactionCategoryRepository.java", "fsPath": "app\\src\\main\\java\\com\\example\\likeqianwang\\Repository\\TransactionCategoryRepository.java"}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionCategoryViewModel.java", "fsPath": "app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionCategoryViewModel.java"}], "usedFilePaths": [{"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\widgets\\Widget_Budget.java", "fsPath": "C:\\Users\\<USER>\\Desktop\\LikeQianWang\\app\\src\\main\\java\\com\\example\\likeqianwang\\widgets\\Widget_Budget.java", "sources": ["opened_tab"], "content": "package com.example.likeqianwang.widgets;\n\nimport android.graphics.Color;\nimport android.os.Bundle;\nimport android.util.Log;\nimport android.view.LayoutInflater;\nimport android.view.View;\nimport android.view.ViewGroup;\nimport android.widget.ProgressBar;\nimport android.widget.TextView;\n\nimport androidx.annotation.NonNull;\nimport androidx.annotation.Nullable;\nimport androidx.fragment.app.Fragment;\nimport androidx.lifecycle.ViewModelProvider;\n\nimport com.example.likeqianwang.Dao.BudgetDao;\nimport com.example.likeqianwang.Entity.Budget;\nimport com.example.likeqianwang.R;\nimport com.example.likeqianwang.ViewModel.BudgetViewModel;\n\nimport java.math.BigDecimal;\nimport java.text.DecimalFormat;\nimport java.util.Calendar;\nimport java.util.List;\n\npublic class Widget_Budget extends Fragment {\n    private BudgetViewModel budgetViewModel;\n    private DecimalFormat amountFormatter;\n\n    // UI组件\n    private TextView remainingBudgetLabel;\n    private TextView remainingBudgetAmount;\n    private TextView usedBudgetLabel;\n    private TextView usagePercentage;\n    private ProgressBar budgetProgressBar;\n    private TextView usedAmount;\n    private TextView totalBudgetAmount;\n    private View statusIndicator;\n    private TextView statusText;\n\n    @Override\n    public View onCreateView(@NonNull LayoutInflater inflater, ViewGroup container,\n                             Bundle savedInstanceState) {\n        return inflater.inflate(R.layout.widget_budget, container, false);\n    }\n\n    @Override\n    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {\n        super.onViewCreated(view, savedInstanceState);\n\n        initViews(view);\n        initViewModel();\n        observeBudgetData();\n    }\n\n    private void initViews(View view) {\n        remainingBudgetLabel = view.findViewById(R.id.widget_budget_剩余预算);\n        remainingBudgetAmount = view.findViewById(R.id.widget_budget_剩余预算金额);\n        usedBudgetLabel = view.findViewById(R.id.widget_budget_已用预算);\n        usagePercentage = view.findViewById(R.id.widget_budget_使用百分比);\n        budgetProgressBar = view.findViewById(R.id.widget_budget_进度条);\n        usedAmount = view.findViewById(R.id.widget_budget_已用金额);\n        totalBudgetAmount = view.findViewById(R.id.widget_budget_总预算金额);\n        statusIndicator = view.findViewById(R.id.widget_budget_状态指示器);\n        statusText = view.findViewById(R.id.widget_budget_状态文本);\n\n        // 初始化格式化工具\n        amountFormatter = new DecimalFormat(\"¥#,##0.00\");\n\n        // 初始化进度条\n        initProgressBar();\n    }\n\n    private void initViewModel() {\n        budgetViewModel = new ViewModelProvider(requireActivity()).get(BudgetViewModel.class);\n    }\n\n    private void initProgressBar() {\n        // 设置进度条的基本属性\n        budgetProgressBar.setMax(100);\n        budgetProgressBar.setProgress(0);\n\n        // 决定是否使用自定义drawable\n        boolean useCustomDrawable = true; // 设置为false以使用默认样式\n\n        if (useCustomDrawable) {\n            // 使用兼容的方式设置自定义drawable\n            try {\n                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {\n                    budgetProgressBar.setProgressDrawable(getResources().getDrawable(R.drawable.budget_progress_bar, getContext().getTheme()));\n                } else {\n                    budgetProgressBar.setProgressDrawable(getResources().getDrawable(R.drawable.budget_progress_bar));\n                }\n                Log.d(\"BudgetWidget\", \"Custom progress bar drawable set successfully\");\n            } catch (Exception e) {\n                Log.e(\"BudgetWidget\", \"Failed to set custom drawable, using default\", e);\n                // 如果设置自定义drawable失败，保持默认样式\n            }\n        } else {\n            Log.d(\"BudgetWidget\", \"Using default progress bar style\");\n        }\n\n        Log.d(\"BudgetWidget\", \"Progress bar initialized\");\n    }\n\n    private void observeBudgetData() {\n        // 获取当前时期\n        BudgetViewModel.CurrentPeriod currentPeriod = BudgetViewModel.getCurrentPeriod();\n\n        // 观察总预算\n        budgetViewModel.getTotalBudget(\"MONTHLY\", currentPeriod.year, currentPeriod.month)\n                .observe(getViewLifecycleOwner(), this::updateTotalBudgetDisplay);\n\n        // 观察预算使用情况\n        budgetViewModel.getBudgetUsage(\"MONTHLY\", currentPeriod.year, currentPeriod.month)\n                .observe(getViewLifecycleOwner(), this::updateBudgetUsageDisplay);\n    }\n\n    private void updateTotalBudgetDisplay(Budget totalBudget) {\n        if (totalBudget != null) {\n            totalBudgetAmount.setText(amountFormatter.format(totalBudget.getBudgetAmount()));\n        } else {\n            // 没有设置总预算时显示默认信息\n            totalBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            remainingBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usedAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usagePercentage.setText(\"0%\");\n\n            // 重置进度条\n            budgetProgressBar.setProgress(0);\n            budgetProgressBar.getProgressDrawable().setColorFilter(Color.GRAY, android.graphics.PorterDuff.Mode.SRC_IN);\n\n            updateStatusDisplay(\"未设置预算\", Color.GRAY);\n        }\n    }\n\n    private void updateBudgetUsageDisplay(List<BudgetDao.BudgetUsage> budgetUsages) {\n        if (budgetUsages == null || budgetUsages.isEmpty()) {\n            // 没有预算使用数据\n            remainingBudgetAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usedAmount.setText(amountFormatter.format(BigDecimal.ZERO));\n            usagePercentage.setText(\"0%\");\n            budgetProgressBar.setProgress(0);\n            updateStatusDisplay(\"无数据\", Color.GRAY);\n            return;\n        }\n\n        // 查找总预算的使用情况\n        BudgetDao.BudgetUsage totalBudgetUsage = null;\n        for (BudgetDao.BudgetUsage usage : budgetUsages) {\n            if (\"TOTAL\".equals(usage.budgetType)) {\n                totalBudgetUsage = usage;\n                break;\n            }\n        }\n\n        if (totalBudgetUsage != null) {\n            updateBudgetDisplay(totalBudgetUsage);\n        } else {\n            // 如果没有总预算，计算所有分类预算的总和\n            calculateCombinedBudgetUsage(budgetUsages);\n        }\n    }\n\n    private void updateBudgetDisplay(BudgetDao.BudgetUsage budgetUsage) {\n        BigDecimal budgetAmount = budgetUsage.budgetAmount != null ? budgetUsage.budgetAmount : BigDecimal.ZERO;\n        BigDecimal usedAmountValue = budgetUsage.usedAmount != null ? budgetUsage.usedAmount : BigDecimal.ZERO;\n        BigDecimal remainingAmount = budgetAmount.subtract(usedAmountValue);\n\n        // 更新金额显示\n        remainingBudgetAmount.setText(amountFormatter.format(remainingAmount));\n        usedAmount.setText(amountFormatter.format(usedAmountValue));\n        totalBudgetAmount.setText(amountFormatter.format(budgetAmount));\n\n        // 更新百分比和进度条\n        double remainingPercentage = 100 - Math.min(budgetUsage.usagePercentage, 100);\n        int progressValue = (int) remainingPercentage;\n        Log.d(\"BudgetWidget\", \"Remaining percentage: \" + remainingPercentage + \", Progress value: \" + progressValue);\n        usagePercentage.setText(String.format(\"%.0f%%\", remainingPercentage));\n\n        // 更新进度条 - 使用更可靠的方法\n        updateProgressBar(progressValue, budgetUsage);\n\n        // 将\"已用预算\"改为\"剩余预算百分比\"\n        usedBudgetLabel.setText(\"剩余预算百分比\");\n\n        // 更新状态显示\n        BudgetViewModel.BudgetStatus status = BudgetViewModel.calculateBudgetStatus(\n                budgetAmount, usedAmountValue, budgetUsage.alertThreshold);\n        updateStatusDisplay(status.statusText, status.statusColor);\n    }\n\n    private void calculateCombinedBudgetUsage(List<BudgetDao.BudgetUsage> budgetUsages) {\n        BigDecimal totalBudgetAmount = BigDecimal.ZERO;\n        BigDecimal totalUsedAmount = BigDecimal.ZERO;\n        double averageAlertThreshold = 0.8; // 默认预警阈值\n\n        for (BudgetDao.BudgetUsage usage : budgetUsages) {\n            if (\"CATEGORY\".equals(usage.budgetType)) {\n                totalBudgetAmount = totalBudgetAmount.add(usage.budgetAmount != null ? usage.budgetAmount : BigDecimal.ZERO);\n                totalUsedAmount = totalUsedAmount.add(usage.usedAmount != null ? usage.usedAmount : BigDecimal.ZERO);\n            }\n        }\n\n        if (totalBudgetAmount.compareTo(BigDecimal.ZERO) > 0) {\n            // 创建虚拟的总预算使用情况\n            BudgetDao.BudgetUsage combinedUsage = new BudgetDao.BudgetUsage();\n            combinedUsage.budgetAmount = totalBudgetAmount;\n            combinedUsage.usedAmount = totalUsedAmount;\n            combinedUsage.usagePercentage = totalUsedAmount.divide(totalBudgetAmount, 4, BigDecimal.ROUND_HALF_UP)\n                    .multiply(new BigDecimal(\"100\")).doubleValue();\n            combinedUsage.alertThreshold = averageAlertThreshold;\n            combinedUsage.isOverBudget = totalUsedAmount.compareTo(totalBudgetAmount) > 0;\n\n            updateBudgetDisplay(combinedUsage);\n        } else {\n            // 没有任何预算设置\n            updateTotalBudgetDisplay(null);\n        }\n    }\n\n    private void updateProgressBar(int progressValue, BudgetDao.BudgetUsage budgetUsage) {\n        // 确保进度值在有效范围内\n        progressValue = Math.max(0, Math.min(100, progressValue));\n\n        Log.d(\"BudgetWidget\", \"Updating progress bar to: \" + progressValue);\n\n        // 更新颜色（仅当有自定义drawable时）\n        updateProgressBarColor(budgetUsage);\n\n        // 直接设置进度值\n        budgetProgressBar.setProgress(progressValue);\n\n        Log.d(\"BudgetWidget\", \"Progress bar updated successfully\");\n    }\n\n    private void updateProgressBarColor(BudgetDao.BudgetUsage budgetUsage) {\n        int color;\n        double remainingPercentage = 100 - Math.min(budgetUsage.usagePercentage, 100);\n\n        if (budgetUsage.isOverBudget) {\n            color = Color.RED;\n        } else if (remainingPercentage <= ((1 - budgetUsage.alertThreshold) * 100)) {\n            // 当剩余预算低于警戒线时（例如剩余不足20%）\n            color = Color.parseColor(\"#FF9800\"); // 橙色\n        } else {\n            color = Color.parseColor(\"#4CAF50\"); // 绿色\n        }\n\n        // 安全地更新进度条颜色\n        try {\n            if (budgetProgressBar.getProgressDrawable() != null) {\n                // 使用颜色过滤器更新颜色\n                budgetProgressBar.getProgressDrawable().setColorFilter(color, android.graphics.PorterDuff.Mode.SRC_IN);\n                Log.d(\"BudgetWidget\", \"Progress bar color updated to: \" + Integer.toHexString(color));\n            } else {\n                Log.w(\"BudgetWidget\", \"Progress drawable is null, cannot update color\");\n            }\n        } catch (Exception e) {\n            Log.e(\"BudgetWidget\", \"Failed to update progress bar color\", e);\n        }\n    }\n\n    private void createProgressBarDrawable(int progressColor) {\n        try {\n            // 获取屏幕密度用于dp转px\n            float density = getResources().getDisplayMetrics().density;\n            int cornerRadius = (int) (4 * density); // 4dp转换为像素\n\n            // 创建背景drawable\n            android.graphics.drawable.GradientDrawable backgroundDrawable = new android.graphics.drawable.GradientDrawable();\n            backgroundDrawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);\n            backgroundDrawable.setCornerRadius(cornerRadius);\n            backgroundDrawable.setColor(Color.parseColor(\"#E0E0E0\"));\n\n            // 创建进度drawable\n            android.graphics.drawable.GradientDrawable progressDrawable = new android.graphics.drawable.GradientDrawable();\n            progressDrawable.setShape(android.graphics.drawable.GradientDrawable.RECTANGLE);\n            progressDrawable.setCornerRadius(cornerRadius);\n            progressDrawable.setColor(progressColor);\n\n            // 创建ClipDrawable用于进度效果\n            android.graphics.drawable.ClipDrawable clipDrawable = new android.graphics.drawable.ClipDrawable(\n                    progressDrawable, android.view.Gravity.LEFT, android.graphics.drawable.ClipDrawable.HORIZONTAL);\n\n            // 创建LayerDrawable\n            android.graphics.drawable.LayerDrawable layerDrawable = new android.graphics.drawable.LayerDrawable(\n                    new android.graphics.drawable.Drawable[]{backgroundDrawable, clipDrawable});\n\n            layerDrawable.setId(0, android.R.id.background);\n            layerDrawable.setId(1, android.R.id.progress);\n\n            // 设置到进度条\n            budgetProgressBar.setProgressDrawable(layerDrawable);\n\n        } catch (Exception e) {\n            // 如果创建drawable失败，使用简单的颜色过滤器\n            Log.e(\"BudgetWidget\", \"Failed to create progress drawable\", e);\n            if (budgetProgressBar.getProgressDrawable() != null) {\n                budgetProgressBar.getProgressDrawable().setColorFilter(progressColor, android.graphics.PorterDuff.Mode.SRC_IN);\n            }\n        }\n    }\n\n    private void updateStatusDisplay(String status, int color) {\n        statusText.setText(status);\n        statusText.setTextColor(color);\n        statusIndicator.getBackground().setColorFilter(color, android.graphics.PorterDuff.Mode.SRC_IN);\n    }\n\n    @Override\n    public void onDestroyView() {\n        super.onDestroyView();\n    }\n}"}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\Repository\\TransactionCategoryRepository.java", "fsPath": "app\\src\\main\\java\\com\\example\\likeqianwang\\Repository\\TransactionCategoryRepository.java"}, {"path": "app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionCategoryViewModel.java", "fsPath": "app\\src\\main\\java\\com\\example\\likeqianwang\\ViewModel\\TransactionCategoryViewModel.java"}]}, "createdAt": 1750581757146, "isInternal": false}], "isAgent": false, "isNameGenerated": true, "isCustomAgent": false, "isUnitTestsAgent": false, "isE2EAgent": false, "autoApply": false}}