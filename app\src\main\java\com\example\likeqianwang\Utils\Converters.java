package com.example.likeqianwang.Utils;

import androidx.room.TypeConverter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.Date;

public class Converters {
    @TypeConverter
    public static Date fromTimestampToDate(Long timestamp) {
        return timestamp == null ? null : new Date(timestamp);
    }

    @TypeConverter
    public static Long dateToTimestamp(Date date) {
        return date == null ? null : date.getTime();
    }

    @TypeConverter
    public static LocalTime fromMinutes(Integer minutes) {
        return minutes == null ? null : LocalTime.of(minutes / 60, minutes % 60);
    }

    @TypeConverter
    public static Integer timeToMinutes(LocalTime time) {
        return time == null ? null : time.getHour() * 60 + time.getMinute();
    }

    @TypeConverter
    public static BigDecimal fromString(String value) {
        return value == null ? null : new BigDecimal(value);
    }

    @TypeConverter
    public static String bigDecimalToString(BigDecimal value) {
        return value == null ? null : value.toString();
    }
}
