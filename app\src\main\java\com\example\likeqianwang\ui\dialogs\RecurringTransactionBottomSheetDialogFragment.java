package com.example.likeqianwang.ui.dialogs;

import android.app.Dialog;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.RadioGroup;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModelProvider;

import com.example.likeqianwang.Entity.RecurringTransaction;
import com.example.likeqianwang.R;
import com.example.likeqianwang.ViewModel.RecurringTransactionViewModel;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Locale;

public class RecurringTransactionBottomSheetDialogFragment extends BottomSheetDialogFragment {

    private static final String TAG = "RecurringTransactionDialog";
    private static final String ARG_RECURRING_TRANSACTION = "recurring_transaction";

    // UI组件
    private ImageView closeButton;
    private TextView titleText;
    private EditText nameEditText;
    private EditText amountEditText;
    private RadioGroup typeRadioGroup;
    private EditText remarkEditText;
    private TextView startDateText;
    private TextView endDateText;
    private Spinner repeatTypeSpinner;
    private EditText repeatIntervalEditText;
    private Button saveButton;

    // 数据
    private RecurringTransaction recurringTransaction;
    private boolean isEditMode = false;
    private RecurringTransactionViewModel viewModel;
    private Date selectedStartDate;
    private Date selectedEndDate;

    public static RecurringTransactionBottomSheetDialogFragment newInstance(RecurringTransaction recurringTransaction) {
        RecurringTransactionBottomSheetDialogFragment fragment = new RecurringTransactionBottomSheetDialogFragment();
        Bundle args = new Bundle();
        if (recurringTransaction != null) {
            args.putSerializable(ARG_RECURRING_TRANSACTION, recurringTransaction);
        }
        fragment.setArguments(args);
        return fragment;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            setupFullHeight(bottomSheetDialog);
        });
        return dialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.layout_recurring_transaction_bottom_sheet, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        initData();
        initViews(view);
        initViewModel();
        setupSpinners();
        setupClickListeners();
        populateData();
    }

    private void initData() {
        Bundle args = getArguments();
        if (args != null) {
            recurringTransaction = (RecurringTransaction) args.getSerializable(ARG_RECURRING_TRANSACTION);
            isEditMode = recurringTransaction != null;
        }

        if (!isEditMode) {
            recurringTransaction = new RecurringTransaction();
            // 设置默认值
            recurringTransaction.setType("EXPENSE");
            recurringTransaction.setRepeatType("MONTHLY");
            recurringTransaction.setRepeatInterval(1);
            selectedStartDate = new Date();
            recurringTransaction.setStartDate(selectedStartDate);
        } else {
            selectedStartDate = recurringTransaction.getStartDate();
            selectedEndDate = recurringTransaction.getEndDate();
        }
    }

    private void initViews(View view) {
        closeButton = view.findViewById(R.id.recurring_transaction_close);
        titleText = view.findViewById(R.id.recurring_transaction_title);
        nameEditText = view.findViewById(R.id.recurring_transaction_name);
        amountEditText = view.findViewById(R.id.recurring_transaction_amount);
        typeRadioGroup = view.findViewById(R.id.recurring_transaction_type_group);
        remarkEditText = view.findViewById(R.id.recurring_transaction_remark);
        startDateText = view.findViewById(R.id.recurring_transaction_start_date);
        endDateText = view.findViewById(R.id.recurring_transaction_end_date);
        repeatTypeSpinner = view.findViewById(R.id.recurring_transaction_repeat_type);
        repeatIntervalEditText = view.findViewById(R.id.recurring_transaction_repeat_interval);
        saveButton = view.findViewById(R.id.recurring_transaction_save);

        // 设置标题
        titleText.setText(isEditMode ? "编辑周期记账" : "新增周期记账");
    }

    private void initViewModel() {
        viewModel = new ViewModelProvider(requireActivity()).get(RecurringTransactionViewModel.class);
    }

    private void setupSpinners() {
        // 设置重复类型下拉框
        String[] repeatTypes = {"每日", "每周", "每月", "每年"};
        ArrayAdapter<String> repeatTypeAdapter = new ArrayAdapter<>(
                requireContext(), android.R.layout.simple_spinner_item, repeatTypes);
        repeatTypeAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        repeatTypeSpinner.setAdapter(repeatTypeAdapter);
    }

    private void setupClickListeners() {
        closeButton.setOnClickListener(v -> dismiss());

        startDateText.setOnClickListener(v -> showDateTimePicker(true));
        endDateText.setOnClickListener(v -> showDateTimePicker(false));

        // 长按结束时间可以清除
        endDateText.setOnLongClickListener(v -> {
            if (selectedEndDate != null) {
                new androidx.appcompat.app.AlertDialog.Builder(requireContext())
                        .setTitle("清除结束时间")
                        .setMessage("确定要清除结束时间吗？清除后周期记账将无限期执行。")
                        .setPositiveButton("清除", (dialog, which) -> {
                            selectedEndDate = null;
                            updateDateTexts();
                        })
                        .setNegativeButton("取消", null)
                        .show();
            }
            return true;
        });

        saveButton.setOnClickListener(v -> saveRecurringTransaction());
    }

    private void populateData() {
        if (isEditMode && recurringTransaction != null) {
            nameEditText.setText(recurringTransaction.getName());
            
            if (recurringTransaction.getAmount() != null) {
                amountEditText.setText(recurringTransaction.getAmount().toString());
            }

            // 设置类型
            String type = recurringTransaction.getType();
            switch (type) {
                case "INCOME":
                    typeRadioGroup.check(R.id.recurring_transaction_type_income);
                    break;
                case "EXPENSE":
                    typeRadioGroup.check(R.id.recurring_transaction_type_expense);
                    break;
                case "TRANSFER":
                    typeRadioGroup.check(R.id.recurring_transaction_type_transfer);
                    break;
            }

            remarkEditText.setText(recurringTransaction.getRemark());

            // 设置重复类型
            String repeatType = recurringTransaction.getRepeatType();
            int spinnerPosition = 0;
            switch (repeatType) {
                case "DAILY": spinnerPosition = 0; break;
                case "WEEKLY": spinnerPosition = 1; break;
                case "MONTHLY": spinnerPosition = 2; break;
                case "YEARLY": spinnerPosition = 3; break;
            }
            repeatTypeSpinner.setSelection(spinnerPosition);

            repeatIntervalEditText.setText(String.valueOf(recurringTransaction.getRepeatInterval()));
        }

        updateDateTexts();
    }

    private void updateDateTexts() {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault());
        
        if (selectedStartDate != null) {
            startDateText.setText(dateFormat.format(selectedStartDate));
        } else {
            startDateText.setText("选择开始时间");
        }

        if (selectedEndDate != null) {
            endDateText.setText(dateFormat.format(selectedEndDate));
        } else {
            endDateText.setText("选择结束时间（可选）");
        }
    }

    private void showDateTimePicker(boolean isStartDate) {
        Calendar calendar = Calendar.getInstance();

        // 如果是编辑模式且有现有日期，使用现有日期作为初始值
        if (isStartDate && selectedStartDate != null) {
            calendar.setTime(selectedStartDate);
        } else if (!isStartDate && selectedEndDate != null) {
            calendar.setTime(selectedEndDate);
        }

        // 显示日期选择器
        android.app.DatePickerDialog datePickerDialog = new android.app.DatePickerDialog(
                requireContext(),
                (view, year, month, dayOfMonth) -> {
                    // 日期选择完成后，显示时间选择器
                    showTimePicker(isStartDate, year, month, dayOfMonth);
                },
                calendar.get(Calendar.YEAR),
                calendar.get(Calendar.MONTH),
                calendar.get(Calendar.DAY_OF_MONTH)
        );

        datePickerDialog.show();
    }

    private void showTimePicker(boolean isStartDate, int year, int month, int dayOfMonth) {
        Calendar calendar = Calendar.getInstance();

        // 如果是编辑模式且有现有时间，使用现有时间作为初始值
        if (isStartDate && selectedStartDate != null) {
            calendar.setTime(selectedStartDate);
        } else if (!isStartDate && selectedEndDate != null) {
            calendar.setTime(selectedEndDate);
        }

        android.app.TimePickerDialog timePickerDialog = new android.app.TimePickerDialog(
                requireContext(),
                (view, hourOfDay, minute) -> {
                    // 创建选择的日期时间
                    Calendar selectedCalendar = Calendar.getInstance();
                    selectedCalendar.set(year, month, dayOfMonth, hourOfDay, minute, 0);
                    selectedCalendar.set(Calendar.MILLISECOND, 0);

                    Date selectedDateTime = selectedCalendar.getTime();

                    // 验证日期逻辑
                    if (isStartDate) {
                        // 开始时间不能晚于结束时间
                        if (selectedEndDate != null && selectedDateTime.after(selectedEndDate)) {
                            Toast.makeText(getContext(), "开始时间不能晚于结束时间", Toast.LENGTH_SHORT).show();
                            return;
                        }
                        selectedStartDate = selectedDateTime;
                    } else {
                        // 结束时间不能早于开始时间
                        if (selectedStartDate != null && selectedDateTime.before(selectedStartDate)) {
                            Toast.makeText(getContext(), "结束时间不能早于开始时间", Toast.LENGTH_SHORT).show();
                            return;
                        }
                        selectedEndDate = selectedDateTime;
                    }

                    updateDateTexts();
                },
                calendar.get(Calendar.HOUR_OF_DAY),
                calendar.get(Calendar.MINUTE),
                true // 24小时制
        );

        timePickerDialog.show();
    }

    private void saveRecurringTransaction() {
        try {
            // 验证输入
            if (!validateInput()) {
                return;
            }

            // 收集数据
            collectData();

            // 保存或更新
            if (isEditMode) {
                updateRecurringTransaction();
            } else {
                insertRecurringTransaction();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error saving recurring transaction", e);
            Toast.makeText(getContext(), "保存失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    private boolean validateInput() {
        String name = nameEditText.getText().toString().trim();
        if (name.isEmpty()) {
            Toast.makeText(getContext(), "请输入周期记账名称", Toast.LENGTH_SHORT).show();
            return false;
        }

        String amountStr = amountEditText.getText().toString().trim();
        if (amountStr.isEmpty()) {
            Toast.makeText(getContext(), "请输入金额", Toast.LENGTH_SHORT).show();
            return false;
        }

        try {
            BigDecimal amount = new BigDecimal(amountStr);
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                Toast.makeText(getContext(), "金额必须大于0", Toast.LENGTH_SHORT).show();
                return false;
            }
        } catch (NumberFormatException e) {
            Toast.makeText(getContext(), "请输入有效的金额", Toast.LENGTH_SHORT).show();
            return false;
        }

        if (selectedStartDate == null) {
            Toast.makeText(getContext(), "请选择开始时间", Toast.LENGTH_SHORT).show();
            return false;
        }

        String intervalStr = repeatIntervalEditText.getText().toString().trim();
        if (intervalStr.isEmpty()) {
            Toast.makeText(getContext(), "请输入重复间隔", Toast.LENGTH_SHORT).show();
            return false;
        }

        try {
            int interval = Integer.parseInt(intervalStr);
            if (interval <= 0) {
                Toast.makeText(getContext(), "重复间隔必须大于0", Toast.LENGTH_SHORT).show();
                return false;
            }
        } catch (NumberFormatException e) {
            Toast.makeText(getContext(), "请输入有效的重复间隔", Toast.LENGTH_SHORT).show();
            return false;
        }

        return true;
    }

    private void collectData() {
        recurringTransaction.setName(nameEditText.getText().toString().trim());
        recurringTransaction.setAmount(new BigDecimal(amountEditText.getText().toString().trim()));

        // 设置类型
        int checkedTypeId = typeRadioGroup.getCheckedRadioButtonId();
        if (checkedTypeId == R.id.recurring_transaction_type_income) {
            recurringTransaction.setType("INCOME");
        } else if (checkedTypeId == R.id.recurring_transaction_type_expense) {
            recurringTransaction.setType("EXPENSE");
        } else if (checkedTypeId == R.id.recurring_transaction_type_transfer) {
            recurringTransaction.setType("TRANSFER");
        }

        recurringTransaction.setRemark(remarkEditText.getText().toString().trim());
        recurringTransaction.setStartDate(selectedStartDate);
        recurringTransaction.setEndDate(selectedEndDate);

        // 设置重复类型
        int repeatTypePosition = repeatTypeSpinner.getSelectedItemPosition();
        String[] repeatTypeValues = {"DAILY", "WEEKLY", "MONTHLY", "YEARLY"};
        recurringTransaction.setRepeatType(repeatTypeValues[repeatTypePosition]);

        recurringTransaction.setRepeatInterval(Integer.parseInt(repeatIntervalEditText.getText().toString().trim()));

        // 设置下次执行时间为开始时间
        recurringTransaction.setNextExecutionDate(selectedStartDate);

        // TODO: 设置分类ID和账户ID
        // 这里需要添加分类选择和账户选择的逻辑
        recurringTransaction.setCategoryId(1); // 临时设置
        recurringTransaction.setFromAccountId("default"); // 临时设置
    }

    private void insertRecurringTransaction() {
        viewModel.insertRecurringTransaction(recurringTransaction, 
                new RecurringTransactionViewModel.RecurringTransactionOperationCallback() {
                    @Override
                    public void onSuccess() {
                        if (getActivity() != null) {
                            getActivity().runOnUiThread(() -> {
                                Toast.makeText(getContext(), "周期记账已创建", Toast.LENGTH_SHORT).show();
                                dismiss();
                            });
                        }
                    }

                    @Override
                    public void onError(String error) {
                        if (getActivity() != null) {
                            getActivity().runOnUiThread(() -> {
                                Toast.makeText(getContext(), "创建失败: " + error, Toast.LENGTH_SHORT).show();
                            });
                        }
                    }
                });
    }

    private void updateRecurringTransaction() {
        viewModel.updateRecurringTransaction(recurringTransaction, 
                new RecurringTransactionViewModel.RecurringTransactionOperationCallback() {
                    @Override
                    public void onSuccess() {
                        if (getActivity() != null) {
                            getActivity().runOnUiThread(() -> {
                                Toast.makeText(getContext(), "周期记账已更新", Toast.LENGTH_SHORT).show();
                                dismiss();
                            });
                        }
                    }

                    @Override
                    public void onError(String error) {
                        if (getActivity() != null) {
                            getActivity().runOnUiThread(() -> {
                                Toast.makeText(getContext(), "更新失败: " + error, Toast.LENGTH_SHORT).show();
                            });
                        }
                    }
                });
    }

    private void setupFullHeight(BottomSheetDialog bottomSheetDialog) {
        // 设置底部弹窗全屏高度的逻辑
        // 这里可以参考现有的设置菜单的实现
    }
}
