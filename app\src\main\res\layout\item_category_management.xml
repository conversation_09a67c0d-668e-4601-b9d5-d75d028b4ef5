<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="8dp"
    android:background="?attr/selectableItemBackground"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 主分类行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- 拖拽手柄 -->
            <ImageView
                android:id="@+id/category_item_drag_handle"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="12dp"
                android:contentDescription="拖拽排序"
                android:src="@drawable/ic_drag_handle"
                app:tint="@color/grey" />

            <!-- 分类图标 -->
            <ImageView
                android:id="@+id/category_item_icon"
                android:layout_width="32dp"
                android:layout_height="32dp"
                android:layout_marginEnd="12dp"
                android:contentDescription="@string/budget_desc_分类图标"
                app:tint="@color/HuaQing"
                tools:src="@drawable/ic_category" />

            <!-- 分类信息 -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/category_item_name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    tools:text="餐饮" />

                <TextView
                    android:id="@+id/category_item_subcategory_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="2dp"
                    android:textColor="@color/grey"
                    android:textSize="12sp"
                    android:visibility="gone"
                    tools:text="3个子分类"
                    tools:visibility="visible" />

            </LinearLayout>

            <!-- 添加子分类按钮 -->
            <ImageView
                android:id="@+id/category_item_add_subcategory"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:layout_marginEnd="8dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="添加子分类"
                android:src="@drawable/ic_add"
                app:tint="@color/HuaQing" />

            <!-- 更多操作按钮 -->
            <ImageView
                android:id="@+id/category_item_more"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:background="?attr/selectableItemBackgroundBorderless"
                android:contentDescription="更多操作"
                android:src="@drawable/ic_more_vert"
                app:tint="@color/grey" />

        </LinearLayout>

        <!-- 子分类列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/category_item_subcategory_list"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginStart="48dp"
            android:visibility="gone"
            tools:listitem="@layout/item_subcategory_management"
            tools:visibility="visible" />

    </LinearLayout>

</androidx.cardview.widget.CardView>
