package com.example.likeqianwang.adapters;

import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.DataModel.AccountTypeItem;
import com.example.likeqianwang.databinding.StyleAccountTypeItemViewBinding;

import java.util.List;

public class AccountTypeItemAdapter extends RecyclerView.Adapter<AccountTypeItemAdapter.ItemViewHolder> {
    private final List<AccountTypeItem> items;
    private final OnItemClickListener onItemClickListener;

    public interface OnItemClickListener {
        void onItemClick(AccountTypeItem item, int position);
    }

    public AccountTypeItemAdapter(List<AccountTypeItem> items, OnItemClickListener onItemClickListener) {
        this.items = items;
        this.onItemClickListener = onItemClickListener;
    }

    @NonNull
    @Override
    public ItemViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        StyleAccountTypeItemViewBinding styleAccountTypeItemViewBinding = StyleAccountTypeItemViewBinding.inflate(
                LayoutInflater.from(parent.getContext()), parent, false);
        return new ItemViewHolder(styleAccountTypeItemViewBinding);
    }

    @Override
    public void onBindViewHolder(@NonNull ItemViewHolder holder, int position) {
        AccountTypeItem item = items.get(position);
        holder.bind(item);

        holder.itemView.setOnClickListener(v -> {
            if (onItemClickListener != null) {
                onItemClickListener.onItemClick(item, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return items.size();
    }

    public static class ItemViewHolder extends RecyclerView.ViewHolder {
        private final StyleAccountTypeItemViewBinding styleAccountTypeItemViewBinding;

        public ItemViewHolder(StyleAccountTypeItemViewBinding styleAccountTypeItemViewBinding) {
            super(styleAccountTypeItemViewBinding.getRoot());
            this.styleAccountTypeItemViewBinding = styleAccountTypeItemViewBinding;
        }

        public void bind(AccountTypeItem item) {
            styleAccountTypeItemViewBinding.walletsAccountTypeItemName.setText(item.getName());
            styleAccountTypeItemViewBinding.walletsAccountTypeItemIcon.setImageResource(item.getIconResId());
        }
    }
}
