package com.example.likeqianwang;

import com.example.likeqianwang.Entity.RecurringTransaction;

import org.junit.Test;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.Date;

import static org.junit.Assert.*;

/**
 * 周期记账实体类的单元测试
 */
public class RecurringTransactionTest {

    @Test
    public void testRecurringTransactionCreation() {
        RecurringTransaction recurringTransaction = new RecurringTransaction();
        
        // 验证默认值
        assertNotNull(recurringTransaction.getRecurringId());
        assertNotNull(recurringTransaction.getCreateTime());
        assertNotNull(recurringTransaction.getUpdateTime());
        assertEquals("CNY", recurringTransaction.getCurrencySymbol());
        assertTrue(recurringTransaction.isIncludeInStats());
        assertTrue(recurringTransaction.isIncludeInBudget());
        assertTrue(recurringTransaction.isActive());
        assertEquals(1, recurringTransaction.getRepeatInterval());
        assertEquals(0, recurringTransaction.getExecutionCount());
    }

    @Test
    public void testRecurringTransactionSettersAndGetters() {
        RecurringTransaction recurringTransaction = new RecurringTransaction();
        
        // 设置基本信息
        recurringTransaction.setName("测试周期记账");
        recurringTransaction.setType("INCOME");
        recurringTransaction.setAmount(new BigDecimal("1000.00"));
        recurringTransaction.setCategoryId(1L);
        recurringTransaction.setFromAccountId("account-1");
        recurringTransaction.setRemark("测试备注");
        
        // 设置时间信息
        Date startDate = new Date();
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.add(Calendar.MONTH, 6);
        Date endDate = endCalendar.getTime();
        
        recurringTransaction.setStartDate(startDate);
        recurringTransaction.setEndDate(endDate);
        recurringTransaction.setNextExecutionDate(startDate);
        
        // 设置重复信息
        recurringTransaction.setRepeatType("MONTHLY");
        recurringTransaction.setRepeatInterval(2);
        
        // 验证设置的值
        assertEquals("测试周期记账", recurringTransaction.getName());
        assertEquals("INCOME", recurringTransaction.getType());
        assertEquals(new BigDecimal("1000.00"), recurringTransaction.getAmount());
        assertEquals(1L, recurringTransaction.getCategoryId());
        assertEquals("account-1", recurringTransaction.getFromAccountId());
        assertEquals("测试备注", recurringTransaction.getRemark());
        assertEquals(startDate, recurringTransaction.getStartDate());
        assertEquals(endDate, recurringTransaction.getEndDate());
        assertEquals(startDate, recurringTransaction.getNextExecutionDate());
        assertEquals("MONTHLY", recurringTransaction.getRepeatType());
        assertEquals(2, recurringTransaction.getRepeatInterval());
    }

    @Test
    public void testRecurringTransactionTransferType() {
        RecurringTransaction recurringTransaction = new RecurringTransaction();
        
        // 设置转账类型
        recurringTransaction.setType("TRANSFER");
        recurringTransaction.setFromAccountId("account-1");
        recurringTransaction.setToAccountId("account-2");
        
        assertEquals("TRANSFER", recurringTransaction.getType());
        assertEquals("account-1", recurringTransaction.getFromAccountId());
        assertEquals("account-2", recurringTransaction.getToAccountId());
    }

    @Test
    public void testRecurringTransactionActiveStatus() {
        RecurringTransaction recurringTransaction = new RecurringTransaction();
        
        // 默认应该是启用状态
        assertTrue(recurringTransaction.isActive());
        
        // 禁用
        recurringTransaction.setActive(false);
        assertFalse(recurringTransaction.isActive());
        
        // 重新启用
        recurringTransaction.setActive(true);
        assertTrue(recurringTransaction.isActive());
    }

    @Test
    public void testRecurringTransactionExecutionCount() {
        RecurringTransaction recurringTransaction = new RecurringTransaction();
        
        // 默认执行次数应该是0
        assertEquals(0, recurringTransaction.getExecutionCount());
        
        // 增加执行次数
        recurringTransaction.setExecutionCount(5);
        assertEquals(5, recurringTransaction.getExecutionCount());
    }

    @Test
    public void testRecurringTransactionRepeatTypes() {
        RecurringTransaction recurringTransaction = new RecurringTransaction();
        
        // 测试不同的重复类型
        String[] repeatTypes = {"DAILY", "WEEKLY", "MONTHLY", "YEARLY"};
        
        for (String repeatType : repeatTypes) {
            recurringTransaction.setRepeatType(repeatType);
            assertEquals(repeatType, recurringTransaction.getRepeatType());
        }
    }

    @Test
    public void testRecurringTransactionRepeatInterval() {
        RecurringTransaction recurringTransaction = new RecurringTransaction();
        
        // 测试不同的重复间隔
        int[] intervals = {1, 2, 3, 7, 14, 30};
        
        for (int interval : intervals) {
            recurringTransaction.setRepeatInterval(interval);
            assertEquals(interval, recurringTransaction.getRepeatInterval());
        }
    }

    @Test
    public void testRecurringTransactionDateFields() {
        RecurringTransaction recurringTransaction = new RecurringTransaction();
        
        Date now = new Date();
        Calendar future = Calendar.getInstance();
        future.add(Calendar.MONTH, 1);
        Date futureDate = future.getTime();
        
        Calendar past = Calendar.getInstance();
        past.add(Calendar.DAY_OF_MONTH, -1);
        Date pastDate = past.getTime();
        
        // 设置各种日期字段
        recurringTransaction.setStartDate(now);
        recurringTransaction.setEndDate(futureDate);
        recurringTransaction.setNextExecutionDate(now);
        recurringTransaction.setLastExecutionDate(pastDate);
        
        assertEquals(now, recurringTransaction.getStartDate());
        assertEquals(futureDate, recurringTransaction.getEndDate());
        assertEquals(now, recurringTransaction.getNextExecutionDate());
        assertEquals(pastDate, recurringTransaction.getLastExecutionDate());
    }
}
