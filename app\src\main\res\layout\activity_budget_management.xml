<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/YinBai"
    android:orientation="vertical"
    tools:context=".ui.budget_management.BudgetManagementActivity">

    <!-- 标题栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:background="@color/white"
        android:elevation="4dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="16dp">

        <ImageView
            android:id="@+id/budget_management_back"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/budget_desc_返回"
            android:src="@drawable/ic_arrow_back"
            app:tint="@color/black" />

        <TextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:text="@string/budget_management_title"
            android:textColor="@color/black"
            android:textSize="18sp"
            android:textStyle="bold" />

        <ImageView
            android:id="@+id/budget_management_add"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/budget_add_new"
            android:src="@drawable/ic_add"
            app:tint="@color/black" />

    </LinearLayout>

    <!-- 筛选栏 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:orientation="horizontal"
        android:padding="16dp">

        <!-- 周期选择 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/budget_预算周期"
                android:textColor="@color/grey"
                android:textSize="12sp" />

            <Spinner
                android:id="@+id/budget_management_period_spinner"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp" />

        </LinearLayout>

        <!-- 年月选择 -->
        <LinearLayout
            android:id="@+id/budget_management_year_month_container"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_weight="1"
            android:background="?attr/selectableItemBackground"
            android:orientation="vertical"
            android:padding="8dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/receipt_年月选择"
                android:textColor="@color/grey"
                android:textSize="12sp" />

            <TextView
                android:id="@+id/budget_management_year_month"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="2024年1月"
                android:textColor="@color/black"
                android:textSize="14sp" />

        </LinearLayout>

    </LinearLayout>

    <!-- 预算列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/budget_management_recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@color/YinBai"
        android:clipToPadding="false"
        android:padding="16dp"
        tools:listitem="@layout/item_budget_management" />

    <!-- 空状态 -->
    <LinearLayout
        android:id="@+id/budget_management_empty_state"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical"
        android:visibility="gone">

        <ImageView
            android:layout_width="64dp"
            android:layout_height="64dp"
            android:alpha="0.5"
            android:src="@drawable/ic_budget"
            app:tint="@color/grey" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:text="@string/budget_no_data"
            android:textColor="@color/grey"
            android:textSize="16sp" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="点击右上角 + 号添加预算"
            android:textColor="@color/grey"
            android:textSize="14sp" />

    </LinearLayout>

</LinearLayout>
