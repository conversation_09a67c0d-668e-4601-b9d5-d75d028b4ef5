package com.example.likeqianwang.ui.dialogs;

import android.app.Dialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ArrayAdapter;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.RadioButton;
import android.widget.RadioGroup;
import android.widget.SeekBar;
import android.widget.Spinner;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.SwitchCompat;
import androidx.lifecycle.ViewModelProvider;

import com.example.likeqianwang.Entity.Budget;
import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.R;
import com.example.likeqianwang.ViewModel.TransactionCategoryViewModel;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

public class BudgetEditBottomSheetDialogFragment extends BottomSheetDialogFragment {

    private static final String ARG_BUDGET = "budget";
    private static final String ARG_PERIOD = "period";
    private static final String ARG_YEAR = "year";
    private static final String ARG_MONTH = "month";

    // UI组件
    private TextView titleTextView;
    private ImageView closeButton;
    private RadioGroup typeRadioGroup;
    private RadioButton totalTypeRadio;
    private RadioButton categoryTypeRadio;
    private LinearLayout categoryContainer;
    private Spinner categorySpinner;
    private EditText amountEditText;
    private Spinner periodSpinner;
    private SeekBar thresholdSeekBar;
    private TextView thresholdValueTextView;
    private EditText remarkEditText;
    private SwitchCompat activeSwitch;
    private Button cancelButton;
    private Button saveButton;

    // 数据
    private Budget editingBudget;
    private String defaultPeriod;
    private int defaultYear;
    private int defaultMonth;
    private OnBudgetSaveListener listener;

    // ViewModel
    private TransactionCategoryViewModel categoryViewModel;
    private List<TransactionCategory> categories = new ArrayList<>();

    // 周期选项
    private final String[] periodOptions = {"月度", "周度", "年度"};
    private final String[] periodValues = {"MONTHLY", "WEEKLY", "YEARLY"};

    public interface OnBudgetSaveListener {
        void onBudgetSaved(Budget budget, boolean isEdit);
    }

    public static BudgetEditBottomSheetDialogFragment newInstance(Budget budget, String period, int year, int month) {
        BudgetEditBottomSheetDialogFragment fragment = new BudgetEditBottomSheetDialogFragment();
        Bundle args = new Bundle();
        args.putSerializable(ARG_BUDGET, budget);
        args.putString(ARG_PERIOD, period);
        args.putInt(ARG_YEAR, year);
        args.putInt(ARG_MONTH, month);
        fragment.setArguments(args);
        return fragment;
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            setupFullHeight(bottomSheetDialog);
        });
        return dialog;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.layout_budget_edit_bottom_sheet, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        parseArguments();
        initViews(view);
        initViewModel();
        setupSpinners();
        setupSeekBar();
        setupClickListeners();
        loadCategories();
        populateFields();
    }

    private void parseArguments() {
        if (getArguments() != null) {
            editingBudget = (Budget) getArguments().getSerializable(ARG_BUDGET);
            defaultPeriod = getArguments().getString(ARG_PERIOD, "MONTHLY");
            defaultYear = getArguments().getInt(ARG_YEAR, 2024);
            defaultMonth = getArguments().getInt(ARG_MONTH, 1);
        }
    }

    private void initViews(View view) {
        titleTextView = view.findViewById(R.id.budget_edit_title);
        closeButton = view.findViewById(R.id.budget_edit_close);
        typeRadioGroup = view.findViewById(R.id.budget_edit_type_group);
        totalTypeRadio = view.findViewById(R.id.budget_edit_type_total);
        categoryTypeRadio = view.findViewById(R.id.budget_edit_type_category);
        categoryContainer = view.findViewById(R.id.budget_edit_category_container);
        categorySpinner = view.findViewById(R.id.budget_edit_category_spinner);
        amountEditText = view.findViewById(R.id.budget_edit_amount);
        periodSpinner = view.findViewById(R.id.budget_edit_period_spinner);
        thresholdSeekBar = view.findViewById(R.id.budget_edit_threshold_seekbar);
        thresholdValueTextView = view.findViewById(R.id.budget_edit_threshold_value);
        remarkEditText = view.findViewById(R.id.budget_edit_remark);
        activeSwitch = view.findViewById(R.id.budget_edit_active_switch);
        cancelButton = view.findViewById(R.id.budget_edit_cancel);
        saveButton = view.findViewById(R.id.budget_edit_save);

        // 设置标题
        if (editingBudget != null) {
            titleTextView.setText(R.string.budget_edit);
        } else {
            titleTextView.setText(R.string.budget_add_new);
        }
    }

    private void initViewModel() {
        categoryViewModel = new ViewModelProvider(this).get(TransactionCategoryViewModel.class);
    }

    private void setupSpinners() {
        // 设置周期选择器
        ArrayAdapter<String> periodAdapter = new ArrayAdapter<>(requireContext(),
                android.R.layout.simple_spinner_item, periodOptions);
        periodAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        periodSpinner.setAdapter(periodAdapter);

        // 设置默认周期
        for (int i = 0; i < periodValues.length; i++) {
            if (periodValues[i].equals(defaultPeriod)) {
                periodSpinner.setSelection(i);
                break;
            }
        }
    }

    private void setupSeekBar() {
        thresholdSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                thresholdValueTextView.setText(progress + "%");
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {}

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {}
        });
    }

    private void setupClickListeners() {
        closeButton.setOnClickListener(v -> dismiss());
        cancelButton.setOnClickListener(v -> dismiss());
        saveButton.setOnClickListener(v -> saveBudget());

        // 预算类型选择
        typeRadioGroup.setOnCheckedChangeListener((group, checkedId) -> {
            if (checkedId == R.id.budget_edit_type_category) {
                categoryContainer.setVisibility(View.VISIBLE);
            } else {
                categoryContainer.setVisibility(View.GONE);
            }
        });
    }

    private void loadCategories() {
        categoryViewModel.getExpenseCategories().observe(this, categories -> {
            if (categories != null) {
                this.categories.clear();
                this.categories.addAll(categories);
                setupCategorySpinner();
            }
        });
    }

    private void setupCategorySpinner() {
        List<String> categoryNames = new ArrayList<>();
        for (TransactionCategory category : categories) {
            categoryNames.add(category.getCategoryName());
        }

        ArrayAdapter<String> categoryAdapter = new ArrayAdapter<>(requireContext(),
                android.R.layout.simple_spinner_item, categoryNames);
        categoryAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        categorySpinner.setAdapter(categoryAdapter);
    }

    private void populateFields() {
        if (editingBudget != null) {
            // 设置预算类型
            if ("TOTAL".equals(editingBudget.getBudgetType())) {
                totalTypeRadio.setChecked(true);
                categoryContainer.setVisibility(View.GONE);
            } else {
                categoryTypeRadio.setChecked(true);
                categoryContainer.setVisibility(View.VISIBLE);
                // TODO: 设置选中的分类
            }

            // 设置金额
            amountEditText.setText(editingBudget.getBudgetAmount().toString());

            // 设置周期
            for (int i = 0; i < periodValues.length; i++) {
                if (periodValues[i].equals(editingBudget.getBudgetPeriod())) {
                    periodSpinner.setSelection(i);
                    break;
                }
            }

            // 设置预警阈值
            int threshold = (int) (editingBudget.getAlertThreshold() * 100);
            thresholdSeekBar.setProgress(threshold);
            thresholdValueTextView.setText(threshold + "%");

            // 设置备注
            if (editingBudget.getRemark() != null) {
                remarkEditText.setText(editingBudget.getRemark());
            }

            // 设置启用状态
            activeSwitch.setChecked(editingBudget.isActive());
        }
    }

    private void saveBudget() {
        try {
            // 验证输入
            String amountStr = amountEditText.getText().toString().trim();
            if (amountStr.isEmpty()) {
                Toast.makeText(getContext(), "请输入预算金额", Toast.LENGTH_SHORT).show();
                return;
            }

            BigDecimal amount = new BigDecimal(amountStr);
            if (amount.compareTo(BigDecimal.ZERO) <= 0) {
                Toast.makeText(getContext(), "预算金额必须大于0", Toast.LENGTH_SHORT).show();
                return;
            }

            // 创建或更新预算对象
            Budget budget = editingBudget != null ? editingBudget : new Budget();
            
            // 设置预算类型
            if (totalTypeRadio.isChecked()) {
                budget.setBudgetType("TOTAL");
                budget.setCategoryId(null);
            } else {
                budget.setBudgetType("CATEGORY");
                // TODO: 设置选中的分类ID
                if (categorySpinner.getSelectedItemPosition() >= 0 && 
                    categorySpinner.getSelectedItemPosition() < categories.size()) {
                    TransactionCategory selectedCategory = categories.get(categorySpinner.getSelectedItemPosition());
                    budget.setCategoryId(selectedCategory.getCategoryId());
                }
            }

            // 设置其他属性
            budget.setBudgetAmount(amount);
            budget.setBudgetPeriod(periodValues[periodSpinner.getSelectedItemPosition()]);
            budget.setBudgetYear(defaultYear);
            budget.setBudgetMonth(defaultMonth);
            budget.setAlertThreshold(thresholdSeekBar.getProgress() / 100.0);
            budget.setRemark(remarkEditText.getText().toString().trim());
            budget.setActive(activeSwitch.isChecked());
            budget.setUpdateTime(new Date());

            if (editingBudget == null) {
                budget.setBudgetId(UUID.randomUUID().toString());
                budget.setCreateTime(new Date());
            }

            // 回调保存
            if (listener != null) {
                listener.onBudgetSaved(budget, editingBudget != null);
            }

            dismiss();

        } catch (NumberFormatException e) {
            Toast.makeText(getContext(), "请输入有效的金额", Toast.LENGTH_SHORT).show();
        } catch (Exception e) {
            Toast.makeText(getContext(), "保存失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    public void setOnBudgetSaveListener(OnBudgetSaveListener listener) {
        this.listener = listener;
    }

    private void setupFullHeight(BottomSheetDialog bottomSheetDialog) {
        try {
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                bottomSheet.getLayoutParams().height = ViewGroup.LayoutParams.WRAP_CONTENT;
            }
        } catch (Exception e) {
            // 忽略错误
        }
    }
}
