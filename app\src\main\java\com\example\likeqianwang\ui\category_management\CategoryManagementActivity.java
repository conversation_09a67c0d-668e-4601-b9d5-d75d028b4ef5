package com.example.likeqianwang.ui.category_management;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.ItemTouchHelper;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.Entity.TransactionSubcategory;
import com.example.likeqianwang.R;
import com.example.likeqianwang.ViewModel.TransactionCategoryViewModel;
import com.example.likeqianwang.adapters.CategoryManagementAdapter;
import com.example.likeqianwang.ui.dialogs.CategoryEditBottomSheetDialogFragment;
import com.google.android.material.tabs.TabLayout;

import java.util.ArrayList;
import java.util.List;

public class CategoryManagementActivity extends AppCompatActivity implements 
        CategoryManagementAdapter.OnCategoryActionListener,
        CategoryEditBottomSheetDialogFragment.OnCategorySaveListener {

    private static final String TAG = "CategoryManagement";

    // UI组件
    private ImageView backButton;
    private ImageView addButton;
    private TabLayout typeTabLayout;
    private RecyclerView recyclerView;
    private LinearLayout emptyStateLayout;

    // ViewModel
    private TransactionCategoryViewModel viewModel;

    // 适配器
    private CategoryManagementAdapter adapter;
    private List<TransactionCategory> categoryList = new ArrayList<>();

    // 当前选择的类型
    private int currentType = 0; // 0-支出, 1-收入, 2-转账

    // 类型选项
    private final String[] typeOptions = {"支出", "收入", "转账"};
    private final int[] typeValues = {0, 1, 2}; // EXPENSE, INCOME, TRANSFER

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_category_management);

        initViews();
        initViewModel();
        setupTabLayout();
        setupRecyclerView();
        setupClickListeners();
        setupItemTouchHelper();
        loadCategories();
    }

    private void initViews() {
        backButton = findViewById(R.id.category_management_back);
        addButton = findViewById(R.id.category_management_add);
        typeTabLayout = findViewById(R.id.category_management_type_tabs);
        recyclerView = findViewById(R.id.category_management_recycler_view);
        emptyStateLayout = findViewById(R.id.category_management_empty_state);
    }

    private void initViewModel() {
        viewModel = new ViewModelProvider(this).get(TransactionCategoryViewModel.class);

        // 观察操作状态
        viewModel.getOperationStatus().observe(this, status -> {
            if (status != null && !status.isEmpty()) {
                Toast.makeText(this, status, Toast.LENGTH_SHORT).show();
                loadCategories(); // 重新加载数据
            }
        });

        // 观察错误信息
        viewModel.getErrorMessage().observe(this, error -> {
            if (error != null && !error.isEmpty()) {
                Toast.makeText(this, error, Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void setupTabLayout() {
        for (String type : typeOptions) {
            typeTabLayout.addTab(typeTabLayout.newTab().setText(type));
        }

        typeTabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                currentType = typeValues[tab.getPosition()];
                loadCategories();
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {}

            @Override
            public void onTabReselected(TabLayout.Tab tab) {}
        });
    }

    private void setupRecyclerView() {
        adapter = new CategoryManagementAdapter(this, categoryList);
        adapter.setOnCategoryActionListener(this);
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(adapter);
    }

    private void setupClickListeners() {
        backButton.setOnClickListener(v -> finish());
        
        addButton.setOnClickListener(v -> showAddCategoryDialog());
    }

    private void setupItemTouchHelper() {
        ItemTouchHelper itemTouchHelper = new ItemTouchHelper(new ItemTouchHelper.SimpleCallback(
                ItemTouchHelper.UP | ItemTouchHelper.DOWN, 0) {
            
            @Override
            public boolean onMove(RecyclerView recyclerView, RecyclerView.ViewHolder viewHolder, 
                                  RecyclerView.ViewHolder target) {
                int fromPosition = viewHolder.getAdapterPosition();
                int toPosition = target.getAdapterPosition();
                
                // 交换位置
                adapter.moveItem(fromPosition, toPosition);
                
                // 更新数据库中的排序
                updateCategoryOrder();
                
                return true;
            }

            @Override
            public void onSwiped(RecyclerView.ViewHolder viewHolder, int direction) {
                // 不支持滑动删除
            }
        });
        
        itemTouchHelper.attachToRecyclerView(recyclerView);
    }

    private void loadCategories() {
        viewModel.getCategoriesByType(currentType).observe(this, categories -> {
            if (categories != null) {
                categoryList.clear();
                categoryList.addAll(categories);
                adapter.notifyDataSetChanged();
                
                // 显示/隐藏空状态
                if (categories.isEmpty()) {
                    recyclerView.setVisibility(View.GONE);
                    emptyStateLayout.setVisibility(View.VISIBLE);
                } else {
                    recyclerView.setVisibility(View.VISIBLE);
                    emptyStateLayout.setVisibility(View.GONE);
                }
            }
        });
    }

    private void showAddCategoryDialog() {
        CategoryEditBottomSheetDialogFragment dialog = 
                CategoryEditBottomSheetDialogFragment.newInstance(null, null, currentType);
        dialog.setOnCategorySaveListener(this);
        dialog.show(getSupportFragmentManager(), "CategoryEditDialog");
    }

    private void showEditCategoryDialog(TransactionCategory category) {
        CategoryEditBottomSheetDialogFragment dialog = 
                CategoryEditBottomSheetDialogFragment.newInstance(category, null, currentType);
        dialog.setOnCategorySaveListener(this);
        dialog.show(getSupportFragmentManager(), "CategoryEditDialog");
    }

    private void showAddSubcategoryDialog(TransactionCategory parentCategory) {
        CategoryEditBottomSheetDialogFragment dialog = 
                CategoryEditBottomSheetDialogFragment.newInstance(null, parentCategory, currentType);
        dialog.setOnCategorySaveListener(this);
        dialog.show(getSupportFragmentManager(), "CategoryEditDialog");
    }

    private void updateCategoryOrder() {
        // 更新所有分类的排序索引
        for (int i = 0; i < categoryList.size(); i++) {
            TransactionCategory category = categoryList.get(i);
            category.setOrderIndex(i);
            viewModel.updateCategory(category);
        }
    }

    @Override
    public void onCategoryEdit(TransactionCategory category) {
        showEditCategoryDialog(category);
    }

    @Override
    public void onCategoryDelete(TransactionCategory category) {
        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("删除分类")
                .setMessage("确定要删除分类 \"" + category.getCategoryName() + "\" 吗？\n\n删除后相关的交易记录将无法正常显示。")
                .setPositiveButton("删除", (dialog, which) -> {
                    viewModel.deleteCategory(category);
                })
                .setNegativeButton("取消", null)
                .show();
    }

    @Override
    public void onSubcategoryAdd(TransactionCategory parentCategory) {
        showAddSubcategoryDialog(parentCategory);
    }

    @Override
    public void onSubcategoryEdit(TransactionSubcategory subcategory) {
        // TODO: 实现子分类编辑
        Toast.makeText(this, "子分类编辑功能开发中...", Toast.LENGTH_SHORT).show();
    }

    @Override
    public void onSubcategoryDelete(TransactionSubcategory subcategory) {
        new androidx.appcompat.app.AlertDialog.Builder(this)
                .setTitle("删除子分类")
                .setMessage("确定要删除子分类 \"" + subcategory.getSubcategoryName() + "\" 吗？")
                .setPositiveButton("删除", (dialog, which) -> {
                    viewModel.deleteSubcategory(subcategory);
                })
                .setNegativeButton("取消", null)
                .show();
    }

    @Override
    public void onCategorySaved(TransactionCategory category, TransactionSubcategory subcategory, boolean isEdit) {
        if (subcategory != null) {
            // 保存子分类
            if (isEdit) {
                viewModel.updateSubcategory(subcategory);
            } else {
                viewModel.insertSubcategory(subcategory);
            }
        } else {
            // 保存分类
            if (isEdit) {
                viewModel.updateCategory(category);
            } else {
                viewModel.insertCategory(category);
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        loadCategories();
    }
}
