package com.example.likeqianwang.adapters;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.CheckBox;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.TransactionCategory;
import com.example.likeqianwang.R;

import java.util.ArrayList;
import java.util.List;

public class BudgetCategorySelectionAdapter extends RecyclerView.Adapter<BudgetCategorySelectionAdapter.ViewHolder> {
    
    private final List<TransactionCategory> categories;
    private final List<TransactionCategory> selectedCategories;
    private final OnCategorySelectionListener listener;
    
    public interface OnCategorySelectionListener {
        void onCategorySelected(TransactionCategory category);
    }
    
    public BudgetCategorySelectionAdapter(List<TransactionCategory> categories, 
                                         OnCategorySelectionListener listener) {
        this.categories = categories;
        this.selectedCategories = new ArrayList<>();
        this.listener = listener;
    }
    
    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_budget_category_selection, parent, false);
        return new ViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        TransactionCategory category = categories.get(position);
        
        holder.categoryIcon.setImageResource(category.getCategoryIcon());
        holder.categoryName.setText(category.getCategoryName());
        holder.checkbox.setChecked(selectedCategories.contains(category));
        
        holder.itemView.setOnClickListener(v -> {
            if (selectedCategories.contains(category)) {
                selectedCategories.remove(category);
                holder.checkbox.setChecked(false);
            } else {
                selectedCategories.add(category);
                holder.checkbox.setChecked(true);
                if (listener != null) {
                    listener.onCategorySelected(category);
                }
            }
        });
    }
    
    @Override
    public int getItemCount() {
        return categories.size();
    }
    
    public List<TransactionCategory> getSelectedCategories() {
        return new ArrayList<>(selectedCategories);
    }
    
    public static class ViewHolder extends RecyclerView.ViewHolder {
        ImageView categoryIcon;
        TextView categoryName;
        CheckBox checkbox;
        
        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            categoryIcon = itemView.findViewById(R.id.category_selection_icon);
            categoryName = itemView.findViewById(R.id.category_selection_name);
            checkbox = itemView.findViewById(R.id.category_selection_checkbox);
        }
    }
}
