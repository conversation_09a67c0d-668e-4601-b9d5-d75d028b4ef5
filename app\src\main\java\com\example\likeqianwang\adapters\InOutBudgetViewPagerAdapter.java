package com.example.likeqianwang.adapters;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.lifecycle.Lifecycle;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import java.util.List;

public class InOutBudgetViewPagerAdapter extends FragmentStateAdapter {
    List<Fragment> InOut_Budget_fragments;

    public InOutBudgetViewPagerAdapter(@NonNull FragmentManager fragmentManager, @NonNull Lifecycle lifecycle, List<Fragment> InOut_Budget_fragments) {
        super(fragmentManager, lifecycle);
        this.InOut_Budget_fragments = InOut_Budget_fragments;
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        return InOut_Budget_fragments.get(position);
    }

    @Override
    public int getItemCount() {
        return InOut_Budget_fragments.size();
    }
}
