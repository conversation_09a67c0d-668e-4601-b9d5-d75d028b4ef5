package com.example.likeqianwang.ui.wallets_bank_seleciton;

import android.app.Dialog;
import android.content.Intent;
import android.content.res.ColorStateList;
import android.graphics.Color;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.DataModel.AccountBankItem;
import com.example.likeqianwang.FillNewAccountInfoActivity;
import com.example.likeqianwang.R;
import com.example.likeqianwang.adapters.AccountBankItemAdapter;
import com.example.likeqianwang.databinding.DialogAddNewAccountBankSelectionBinding;
import com.example.likeqianwang.ui.wallets_add_new_account.WalletsAddNewAccountDialog;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetDialogFragment;
import com.google.android.material.shape.CornerFamily;
import com.google.android.material.shape.MaterialShapeDrawable;
import com.google.android.material.shape.ShapeAppearanceModel;

import java.util.ArrayList;
import java.util.List;

public class WalletsBankSelectionDialog extends BottomSheetDialogFragment implements AccountBankItemAdapter.OnBankSelectedListener{
    private DialogAddNewAccountBankSelectionBinding dialogAddNewAccountBankSelectionBinding;
    private RecyclerView rvBankList;
    private EditText etBankSearch;
    private String accountTypeId;
    private String accountTypeName;
    private String accountTypeCategoryId;
    private int accountTypeDebitCredit;
    private AccountBankItemAdapter bankAdapter;
    private List<AccountBankItem> bankList;
    private OnBankSelectedListener onBankSelectedListener;
    private boolean shouldStartNewActivity = true;

    public interface OnBankSelectedListener {
        void onBankSelected(AccountBankItem bank);
    }

    public void setOnBankSelectedListener(OnBankSelectedListener onBankSelectedListener) {
        this.onBankSelectedListener = onBankSelectedListener;
    }

    /**
     * 设置是否启动新Activity
     */
    public void setShouldStartNewActivity(boolean shouldStartNewActivity) {
        this.shouldStartNewActivity = shouldStartNewActivity;
    }

    public static WalletsBankSelectionDialog newInstance() {
        return new WalletsBankSelectionDialog();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(BottomSheetDialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialog);

        // 获取从上一个对话框传递的账户类型信息
        if (getArguments() != null) {
            accountTypeId = getArguments().getString("ACCOUNT_TYPE_ID", "");
            accountTypeName = getArguments().getString("ACCOUNT_TYPE_NAME", "");
            accountTypeCategoryId = getArguments().getString("ACCOUNT_TYPE_CATEGORY_ID", "");
            accountTypeDebitCredit = getArguments().getInt("ACCOUNT_TYPE_DEBIT_CREDIT", 1);
        }

        // 设置对话框背景半透明
        setDialogBackgroundDim();
    }

    // 设置对话框背景半透明
    private void setDialogBackgroundDim() {
        // 方法1：使用主题设置
        if (getDialog() != null) {
            getDialog().getWindow().setDimAmount(0.5f); // 设置背景暗度（0-1，0为全透明，1为全黑）
        }
    }

    @NonNull
    @Override
    public Dialog onCreateDialog(@Nullable Bundle savedInstanceState) {
        BottomSheetDialog dialog = (BottomSheetDialog) super.onCreateDialog(savedInstanceState);

        // 设置对话框显示时为展开状态
        dialog.setOnShowListener(dialogInterface -> {
            BottomSheetDialog bottomSheetDialog = (BottomSheetDialog) dialogInterface;
            View bottomSheet = bottomSheetDialog.findViewById(com.google.android.material.R.id.design_bottom_sheet);
            if (bottomSheet != null) {
                BottomSheetBehavior<View> behavior = BottomSheetBehavior.from(bottomSheet);

                // 设置固定高度为800dp
                ViewGroup.LayoutParams layoutParams = bottomSheet.getLayoutParams();
                layoutParams.height = (int) (800 * getResources().getDisplayMetrics().density);
                bottomSheet.setLayoutParams(layoutParams);

                // 禁用拖动，保持固定高度
                behavior.setDraggable(false);

                // 确保对话框紧贴屏幕底部
                behavior.setPeekHeight(layoutParams.height);
                behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
                behavior.setSkipCollapsed(true);

                // 设置圆角背景
                setupBottomSheetBackground(bottomSheet);

                // 设置导航栏颜色与对话框背景一致
                if (getDialog() != null && getDialog().getWindow() != null) {
                    getDialog().getWindow().setNavigationBarColor(Color.parseColor("#4df1f0ed"));
                }
            }
        });

        return dialog;
    }

    // 设置底部表单的圆角背景
    private void setupBottomSheetBackground(View bottomSheet) {
        // 创建圆角形状
        ShapeAppearanceModel shapeAppearanceModel = new ShapeAppearanceModel.Builder()
                .setTopRightCorner(CornerFamily.ROUNDED, dpToPx(16))
                .setTopLeftCorner(CornerFamily.ROUNDED, dpToPx(16))
                .build();

        // 创建MaterialShapeDrawable
        MaterialShapeDrawable shapeDrawable = new MaterialShapeDrawable(shapeAppearanceModel);
        shapeDrawable.setFillColor(ColorStateList.valueOf(Color.WHITE));
        shapeDrawable.setElevation(dpToPx(8));
        shapeDrawable.setShadowColor(Color.LTGRAY);

        // 设置背景
        bottomSheet.setBackground(shapeDrawable);
    }

    private float dpToPx(int dp) {
        return dp * getResources().getDisplayMetrics().density;
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        dialogAddNewAccountBankSelectionBinding = DialogAddNewAccountBankSelectionBinding.inflate(inflater, container, false);
        return dialogAddNewAccountBankSelectionBinding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        rvBankList = dialogAddNewAccountBankSelectionBinding.rvBankList;
        etBankSearch = dialogAddNewAccountBankSelectionBinding.etWalletsAccountBankSearchInput;

        // 设置返回按钮点击事件
        dialogAddNewAccountBankSelectionBinding.walletsAccountBack.setOnClickListener(v -> {
            // 关闭当前对话框
            dismiss();

            // 判断是否是从FillNewAccountInfoActivity进入的
            WalletsAddNewAccountDialog dialog = WalletsAddNewAccountDialog.newInstance();

            if (!shouldStartNewActivity) {
                // 如果是从FillNewAccountInfoActivity进入的，返回到账户类型选择对话框
                dialog.setShouldStartNewActivity(false);

                // 从FillNewAccountInfoActivity获取回调
                if (getActivity() instanceof FillNewAccountInfoActivity activity) {
                    dialog.setOnAccountTypeSelectedListener(accountType -> {
                        // 检查是否是银行卡类型
                        if (accountType.getParentAccountTypeCategoryId().equals("cat_bank")) {
                            // 如果是银行卡类型，显示银行选择对话框
                            activity.showBankSelectionDialog(
                                    accountType.getId(),
                                    accountType.getName(),
                                    accountType.getParentAccountTypeCategoryId(),
                                    accountType.getDebitOrCredit()
                            );
                        } else {
                            // 非银行卡类型，直接更新UI
                            activity.updateAccountTypeInfo(
                                    accountType.getId(),
                                    accountType.getName(),
                                    accountType.getIconResId(),
                                    accountType.getParentAccountTypeCategoryId(),
                                    accountType.getDebitOrCredit(),
                                    null, null, 0
                            );
                        }
                    });
                }

            }

            dialog.show(getParentFragmentManager(), "account_type_dialog");
        });

        // 初始化银行列表
        initBankList();

        // 设置RecyclerView
        rvBankList.setLayoutManager(new LinearLayoutManager(getContext()));
        bankAdapter = new AccountBankItemAdapter(bankList, this);
        rvBankList.setAdapter(bankAdapter);

        // 设置搜索功能
        etBankSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // 过滤列表
                bankAdapter.getFilter().filter(s);
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        // 设置对话框的样式，使其占满屏幕宽度，高度固定为800dp
        if (getDialog() != null && getDialog().getWindow() != null) {
            getDialog().getWindow().setLayout(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    (int) (800 * getResources().getDisplayMetrics().density)
            );
        }

        // 设置背景半透明
        View parent = (View) view.getParent();
        parent.setBackgroundColor(Color.TRANSPARENT);
    }

    private void initBankList() {
        bankList = new ArrayList<>();

        // 添加银行数据（示例数据，您需要根据实际情况添加）
        bankList.add(new AccountBankItem("icbc", "中国工商银行", R.drawable.icon_bank_icbc));
        bankList.add(new AccountBankItem("abc", "中国农业银行", R.drawable.icon_bank_abc));
        bankList.add(new AccountBankItem("boc", "中国银行", R.drawable.icon_bank_boc));
        bankList.add(new AccountBankItem("ccb", "中国建设银行", R.drawable.icon_bank_ccb));
        bankList.add(new AccountBankItem("cmb", "招商银行", R.drawable.icon_bank_cmb));
        bankList.add(new AccountBankItem("psbc", "中国邮政储蓄银行", R.drawable.icon_bank_psbc));
        bankList.add(new AccountBankItem("spdb", "浦发银行", R.drawable.icon_bank_spdb));
        bankList.add(new AccountBankItem("cib", "兴业银行", R.drawable.icon_bank_cib));
        bankList.add(new AccountBankItem("citic", "中信银行", R.drawable.icon_bank_citic));
        bankList.add(new AccountBankItem("cmbc", "民生银行", R.drawable.icon_bank_cmbc));
        bankList.add(new AccountBankItem("gdb", "广发银行", R.drawable.icon_bank_gdb));
        bankList.add(new AccountBankItem("hxb", "华夏银行", R.drawable.icon_bank_hxb));
        bankList.add(new AccountBankItem("bob", "北京银行", R.drawable.icon_bank_bob));
        bankList.add(new AccountBankItem("bocom", "交通银行", R.drawable.icon_bank_bocom));
    }

    @Override
    public void onBankSelected(AccountBankItem bank) {
        // 如果设置了监听器，通知监听器
        if (onBankSelectedListener != null) {
            onBankSelectedListener.onBankSelected(bank);
        }

        // 根据标志决定是否启动新Activity
        if (shouldStartNewActivity) {
            // 创建Intent跳转到FillNewAccountInfoActivity
            Intent intent = new Intent(getContext(), FillNewAccountInfoActivity.class);

            // 传递账户类型信息
            intent.putExtra("ACCOUNT_TYPE_ID", accountTypeId);
            intent.putExtra("ACCOUNT_TYPE_NAME", accountTypeName);
            intent.putExtra("ACCOUNT_TYPE_CATEGORY_ID", accountTypeCategoryId);
            intent.putExtra("ACCOUNT_TYPE_DEBIT_CREDIT", accountTypeDebitCredit);

            // 传递选中的银行信息
            intent.putExtra("BANK_ID", bank.getId());
            intent.putExtra("BANK_NAME", bank.getName());
            intent.putExtra("BANK_ICON", bank.getIconResId());

            // 组合账户名称（例如：中国工商银行储蓄卡）
            String combinedName = bank.getName() + (accountTypeDebitCredit == 0 ? "信用卡" : "储蓄卡");
            intent.putExtra("COMBINED_ACCOUNT_NAME", combinedName);

            startActivity(intent);
        }

        dismiss(); // 选择后关闭对话框
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        dialogAddNewAccountBankSelectionBinding = null;
    }
}
