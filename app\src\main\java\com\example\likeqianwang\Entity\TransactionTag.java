package com.example.likeqianwang.Entity;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.Index;
import androidx.room.PrimaryKey;

@Entity(tableName = "transaction_tags", indices = @Index(value = "tag_name", unique = true))
public class TransactionTag {
    @PrimaryKey(autoGenerate = true)
    private long tagId;

    @ColumnInfo(name = "tag_name")
    private String tagName;

    @ColumnInfo(name = "tag_color")
    private String tagColor;

    @ColumnInfo(name = "tag_category")
    private String tagCategory; // 标签分类，如"交通"、"餐饮"、"娱乐"等

    @ColumnInfo(name = "order_index")
    private int orderIndex; // 排序索引

    @ColumnInfo(name = "is_selected")
    private boolean isSelected = false; // 临时字段，用于UI选择状态

    // 构造函数
    public TransactionTag() {}

    @Ignore
    public TransactionTag(String tagName, String tagColor, String tagCategory) {
        this.tagName = tagName;
        this.tagColor = tagColor;
        this.tagCategory = tagCategory;
        this.orderIndex = 0;
    }

    public long getTagId() {
        return tagId;
    }

    public void setTagId(long tagId) {
        this.tagId = tagId;
    }

    public String getTagName() {
        return tagName;
    }

    public void setTagName(String tagName) {
        this.tagName = tagName;
    }

    public String getTagColor() {
        return tagColor;
    }

    public void setTagColor(String tagColor) {
        this.tagColor = tagColor;
    }

    public String getTagCategory() {
        return tagCategory;
    }

    public void setTagCategory(String tagCategory) {
        this.tagCategory = tagCategory;
    }

    public int getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(int orderIndex) {
        this.orderIndex = orderIndex;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }
}
