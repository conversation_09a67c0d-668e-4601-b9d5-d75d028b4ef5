# 转账功能调试检查清单

## 🎯 快速诊断步骤

### 步骤1：基础环境检查
```bash
# 清除应用数据（推荐）
adb shell pm clear com.example.likeqianwang

# 或者卸载重装
adb uninstall com.example.likeqianwang
# 然后重新安装APK
```

### 步骤2：准备测试数据
1. **创建测试账户**：
   - 账户1：现金账户，余额1000元
   - 账户2：银行卡账户，余额500元

2. **记录账户信息**：
   ```
   转出账户：[账户名] - 余额：[金额]
   转入账户：[账户名] - 余额：[金额]
   ```

### 步骤3：启用调试日志
在Android Studio中：
1. 打开Logcat
2. 设置过滤器：
   ```
   RecordingPageActivity|TransactionInputViewModel|TransactionRepository|RecordingTransferFragment
   ```
3. 选择Debug级别

### 步骤4：执行转账测试
1. 打开记账应用
2. 切换到"转账"页面
3. 选择转出账户（余额较多的）
4. 选择转入账户（不同于转出账户）
5. 输入转账金额：100
6. 点击"确定"按钮

## 📋 日志检查点

### ✅ 正常流程检查点

#### 1. 输入验证阶段
```
D/RecordingPageActivity: confirmInput called with expression: 100.00
D/RecordingPageActivity: Position: 2, Type: 转账, Amount: 100.00
```
**如果没有这些日志**：确认是否正确点击了确定按钮

#### 2. 数据提供者检查
```
D/RecordingTransferFragment: hasRequiredData: true
D/RecordingTransferFragment: fromAccount: [转出账户名]
D/RecordingTransferFragment: toAccount: [转入账户名]
```
**如果hasRequiredData为false**：账户选择有问题，重新选择账户

#### 3. Transaction创建阶段
```
D/RecordingPageActivity: Creating transaction...
D/RecordingPageActivity: createTransaction - Position: 2, Amount: 100.00
D/RecordingPageActivity: Transfer transaction - From: [账户] To: [账户]
D/RecordingPageActivity: Transaction created successfully - Type: TRANSFER
```
**如果这里出错**：检查账户选择是否正确

#### 4. 保存开始阶段
```
D/RecordingPageActivity: Transaction created successfully, saving...
D/TransactionInputViewModel: saveTransaction called
D/TransactionInputViewModel: Transaction type: TRANSFER
```
**如果没有这些日志**：Transaction创建失败

#### 5. 数据库操作阶段
```
D/TransactionRepository: addTransaction called
D/TransactionRepository: Inserting transaction...
D/TransactionRepository: Transaction inserted with ID: [数字]
```
**如果插入失败**：数据库问题，需要清除数据

#### 6. 余额更新阶段
```
D/TransactionRepository: Updating account balances...
D/TransactionRepository: FromAccount: [名称], OldBalance: [旧余额], NewBalance: [新余额]
D/TransactionRepository: ToAccount: [名称], OldBalance: [旧余额], NewBalance: [新余额]
```
**如果余额更新失败**：账户数据问题

#### 7. 完成阶段
```
D/TransactionRepository: Transaction save completed successfully
D/TransactionInputViewModel: Transaction saved successfully with ID: [数字]
D/RecordingPageActivity: Transaction saved successfully
```
**如果到达这里**：转账成功，应该返回主页面

## 🚨 常见错误模式

### 错误模式1：账户选择问题
**症状**：`hasRequiredData: false`
**检查**：
- [ ] 是否选择了转出账户？
- [ ] 是否选择了转入账户？
- [ ] 两个账户是否不同？

**解决**：重新选择账户，确保两个账户都已选择且不相同

### 错误模式2：账户不存在
**症状**：`转出账户不存在` 或 `转入账户不存在`
**检查**：
- [ ] 钱包中是否有对应账户？
- [ ] 账户数据是否正确保存？

**解决**：
1. 检查钱包页面的账户列表
2. 重新创建账户
3. 清除应用数据重新开始

### 错误模式3：数据库约束错误
**症状**：外键约束相关错误
**检查**：
- [ ] 数据库版本是否正确？
- [ ] 是否清除了旧数据？

**解决**：
1. 卸载应用重新安装
2. 或清除应用数据

### 错误模式4：金额问题
**症状**：金额相关错误
**检查**：
- [ ] 输入的金额是否大于0？
- [ ] 转出账户余额是否充足？

**解决**：
1. 使用较小的测试金额（如100）
2. 确保转出账户有足够余额

## 🔧 快速修复方法

### 方法1：重置应用数据
```bash
# 清除所有数据，重新开始
adb shell pm clear com.example.likeqianwang
```

### 方法2：重新创建账户
1. 删除所有现有账户
2. 重新创建2个简单的测试账户：
   - 现金 - 1000元
   - 银行卡 - 500元

### 方法3：使用最简单的测试用例
- 转出：现金账户
- 转入：银行卡账户
- 金额：100元
- 备注：留空

## 📊 成功验证标准

转账成功的标志：
1. ✅ 没有错误提示
2. ✅ 自动返回主页面
3. ✅ 转出账户余额减少100元
4. ✅ 转入账户余额增加100元
5. ✅ 在交易记录中能看到转账记录

## 📞 获取帮助

如果按照以上步骤仍然无法解决，请提供：

1. **完整的Logcat输出**（从点击确定到结束的所有日志）
2. **账户截图**（钱包页面的账户列表）
3. **操作录屏**（如果可能的话）
4. **具体的错误信息**（Toast提示的内容）

### 日志收集命令
```bash
# 清除旧日志
adb logcat -c

# 开始收集日志
adb logcat -s RecordingPageActivity:D TransactionInputViewModel:D TransactionRepository:D RecordingTransferFragment:D > transfer_debug.log

# 执行转账操作后，停止收集（Ctrl+C）
# 然后发送 transfer_debug.log 文件
```

通过这个详细的检查清单，应该能够快速定位并解决转账保存失败的问题。
