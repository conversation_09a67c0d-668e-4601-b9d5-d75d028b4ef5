package com.example.likeqianwang.Entity;

import androidx.room.ColumnInfo;
import androidx.room.Entity;
import androidx.room.Ignore;
import androidx.room.Index;
import androidx.room.PrimaryKey;

import java.util.ArrayList;
import java.util.List;

@Entity(tableName = "transaction_categories")
public class TransactionCategory {
    @ColumnInfo
    @PrimaryKey(autoGenerate = true)
    private long categoryId;

    @ColumnInfo
    private String categoryName;  //收支类型名称（如餐饮/交通）

    @ColumnInfo
    private int categoryIcon; // 图标名称

    @ColumnInfo
    private int categoryType; // 1-INCOME, 0-EXPENSE, 2-TRANSFER

    @ColumnInfo
    private boolean hasSubCategories; // 是否有子分类;

    @ColumnInfo
    private int orderIndex;  // 位置关系

    // 子类型列表 - 不存储在数据库中，通过关系获取
    @Ignore
    private List<TransactionSubcategory> subcategories;

    public TransactionCategory(String categoryName, int categoryIcon, int categoryType, boolean hasSubCategories, int orderIndex) {
        this.categoryName = categoryName;
        this.categoryIcon = categoryIcon;
        this.categoryType = categoryType;
        this.hasSubCategories = hasSubCategories;
        this.orderIndex = orderIndex;
        this.subcategories = new ArrayList<>();
    }

    public long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public int getCategoryIcon() {
        return categoryIcon;
    }

    public void setCategoryIcon(int categoryIcon) {
        this.categoryIcon = categoryIcon;
    }

    public int getCategoryType() {
        return categoryType;
    }

    public void setCategoryType(int categoryType) {
        this.categoryType = categoryType;
    }

    public boolean isHasSubCategories() {
        return hasSubCategories;
    }

    public void setHasSubCategories(boolean hasSubCategories) {
        this.hasSubCategories = hasSubCategories;
    }

    public int getOrderIndex() {
        return orderIndex;
    }

    public void setOrderIndex(int orderIndex) {
        this.orderIndex = orderIndex;
    }

    public List<TransactionSubcategory> getSubcategories() {
        return subcategories;
    }

    public void setSubcategories(List<TransactionSubcategory> subcategories) {
        this.subcategories = subcategories;
    }

    public void addSubtype(TransactionSubcategory subcategory) {
        if (this.subcategories == null) {
            this.subcategories = new ArrayList<>();
        }
        this.subcategories.add(subcategory);
    }
}
