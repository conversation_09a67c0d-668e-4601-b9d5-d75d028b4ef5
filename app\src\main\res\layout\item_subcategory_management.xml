<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="4dp"
    android:background="@drawable/style_tag_management_item_bg"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="12dp">

    <!-- 子分类图标 -->
    <ImageView
        android:id="@+id/subcategory_item_icon"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginEnd="8dp"
        android:contentDescription="子分类图标"
        app:tint="@color/HuaQing"
        tools:src="@drawable/ic_category" />

    <!-- 子分类名称 -->
    <TextView
        android:id="@+id/subcategory_item_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:textColor="@color/black"
        android:textSize="14sp"
        tools:text="早餐" />

    <!-- 更多操作按钮 -->
    <ImageView
        android:id="@+id/subcategory_item_more"
        android:layout_width="20dp"
        android:layout_height="20dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="更多操作"
        android:src="@drawable/ic_more_vert"
        app:tint="@color/grey" />

</LinearLayout>
