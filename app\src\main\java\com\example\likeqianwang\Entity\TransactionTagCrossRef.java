package com.example.likeqianwang.Entity;

import androidx.room.Entity;
import androidx.room.ForeignKey;
import androidx.room.Index;

@Entity(
        tableName = "transaction_tag_cross_ref",
        primaryKeys = {"transactionId", "tagId"},
        foreignKeys = {
                @ForeignKey(
                        entity = Transactions.class,
                        parentColumns = "transactionId",
                        childColumns = "transactionId",
                        onDelete = ForeignKey.CASCADE
                ),
                @ForeignKey(
                        entity = TransactionTag.class,
                        parentColumns = "tagId",
                        childColumns = "tagId",
                        onDelete = ForeignKey.CASCADE
                )
        },
        indices = {
                @Index(value = "transactionId"),
                @Index(value = "tagId")
        }
)
public class TransactionTagCrossRef {
    private long transactionId;
    private long tagId;

    public long getTagId() {
        return tagId;
    }

    public void setTagId(long tagId) {
        this.tagId = tagId;
    }

    public long getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(long transactionId) {
        this.transactionId = transactionId;
    }
}
