package com.example.likeqianwang.DataModel;

import androidx.annotation.NonNull;

import com.example.likeqianwang.Entity.Account;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class AccountCategory {
    private String id;
    private String name;
    private double totalAmount;
    private List<Account> accounts;
    private boolean expanded;

    public AccountCategory(String id, String name) {
        this.id = id;
        this.name = name;
        this.accounts = new ArrayList<>();
        this.totalAmount = 0.0;
        this.expanded = true; // 默认展开
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public double getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(double totalAmount) {
        this.totalAmount = totalAmount;
    }

    public List<Account> getAccounts() {
        if (accounts == null) {
            accounts = new ArrayList<>();
        }
        return accounts;
    }

    public void setAccounts(List<Account> accounts) {
        this.accounts = accounts != null ? accounts : new ArrayList<>();
    }

    public void addAccount(Account account) {
        if (account == null) {
            return;
        }

        // 确保账户列表已初始化
        if (accounts == null) {
            accounts = new ArrayList<>();
        }

        // 检查是否已包含该账户（避免重复）
        boolean accountExists = false;
        String accountId = account.getAccountId();
        for (Account existingAccount : accounts) {
            if (accountId.equals(existingAccount.getAccountId())) {
                return; // 已存在，直接返回
            }
        }

        // 只有当账户不存在时才添加
        if (!accountExists) {
            accounts.add(account);
        }
    }

    public boolean isExpanded() {
        return expanded;
    }

    public void setExpanded(boolean expanded) {
        this.expanded = expanded;
    }

    // 添加 equals 和 hashCode 方法，便于比较
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AccountCategory category = (AccountCategory) o;
        return Objects.equals(id, category.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @NonNull
    @Override
    public String toString() {
        return "AccountCategory{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", accountsCount=" + (accounts != null ? accounts.size() : 0) +
                ", totalAmount=" + totalAmount +
                '}';
    }
}
