package com.example.likeqianwang.adapters;

import android.content.Context;
import android.graphics.Color;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.Entity.Notebook;
import com.example.likeqianwang.R;

import java.util.List;

public class NotebookSelectionAdapter extends RecyclerView.Adapter<NotebookSelectionAdapter.NotebookViewHolder> {

    private final Context context;
    private final List<Notebook> notebookList;
    private final String currentNotebookId;
    private OnNotebookSelectionListener listener;

    public interface OnNotebookSelectionListener {
        void onNotebookSelected(Notebook notebook);
    }

    public NotebookSelectionAdapter(Context context, List<Notebook> notebookList, String currentNotebookId) {
        this.context = context;
        this.notebookList = notebookList;
        this.currentNotebookId = currentNotebookId;
    }

    public void setOnNotebookSelectionListener(OnNotebookSelectionListener listener) {
        this.listener = listener;
    }

    @NonNull
    @Override
    public NotebookViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context).inflate(R.layout.item_notebook_selection, parent, false);
        return new NotebookViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull NotebookViewHolder holder, int position) {
        Notebook notebook = notebookList.get(position);
        holder.bind(notebook);
    }

    @Override
    public int getItemCount() {
        return notebookList != null ? notebookList.size() : 0;
    }

    class NotebookViewHolder extends RecyclerView.ViewHolder {
        private final ImageView notebookIcon;
        private final TextView notebookName;
        private final TextView notebookDescription;
        private final TextView defaultBadge;
        private final ImageView selectedIndicator;

        public NotebookViewHolder(@NonNull View itemView) {
            super(itemView);
            notebookIcon = itemView.findViewById(R.id.notebook_item_icon);
            notebookName = itemView.findViewById(R.id.notebook_item_name);
            notebookDescription = itemView.findViewById(R.id.notebook_item_description);
            defaultBadge = itemView.findViewById(R.id.notebook_item_default_badge);
            selectedIndicator = itemView.findViewById(R.id.notebook_item_selected_indicator);
        }

        public void bind(Notebook notebook) {
            // 设置账本名称
            notebookName.setText(notebook.getNotebookName());

            // 设置账本图标
            if (notebook.getNotebookIcon() != 0) {
                notebookIcon.setImageResource(notebook.getNotebookIcon());
            } else {
                notebookIcon.setImageResource(R.drawable.ic_book);
            }

            // 设置图标颜色
            try {
                if (notebook.getNotebookColor() != null && !notebook.getNotebookColor().isEmpty()) {
                    int color = Color.parseColor(notebook.getNotebookColor());
                    notebookIcon.setColorFilter(color);
                } else {
                    notebookIcon.setColorFilter(context.getResources().getColor(R.color.HuaQing));
                }
            } catch (Exception e) {
                notebookIcon.setColorFilter(context.getResources().getColor(R.color.HuaQing));
            }

            // 设置账本描述
            if (notebook.getNotebookDescription() != null && !notebook.getNotebookDescription().trim().isEmpty()) {
                notebookDescription.setText(notebook.getNotebookDescription());
                notebookDescription.setVisibility(View.VISIBLE);
            } else {
                notebookDescription.setVisibility(View.GONE);
            }

            // 设置默认标签
            if (notebook.isDefault()) {
                defaultBadge.setVisibility(View.VISIBLE);
            } else {
                defaultBadge.setVisibility(View.GONE);
            }

            // 设置选中状态
            boolean isSelected = notebook.getNotebookId().equals(currentNotebookId);
            if (isSelected) {
                selectedIndicator.setVisibility(View.VISIBLE);
                itemView.setBackgroundColor(context.getResources().getColor(R.color.YinBai));
            } else {
                selectedIndicator.setVisibility(View.GONE);
                itemView.setBackground(context.getDrawable(android.R.drawable.list_selector_background));
            }

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (listener != null && !isSelected) {
                    listener.onNotebookSelected(notebook);
                }
            });
        }
    }
}
