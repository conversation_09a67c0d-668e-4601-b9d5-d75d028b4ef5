<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="40dp"
    android:height="40dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
  <path
      android:pathData="M16,4L16,4A4,4 0,0 1,20 8L20,8A4,4 0,0 1,16 12L16,12A4,4 0,0 1,12 8L12,8A4,4 0,0 1,16 4z"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#333"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M16,22L16,22A4,4 0,0 1,20 26L20,26A4,4 0,0 1,16 30L16,30A4,4 0,0 1,12 26L12,26A4,4 0,0 1,16 22z"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#333"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M16,31L16,44"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#333"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M32,4L32,4A4,4 0,0 1,36 8L36,8A4,4 0,0 1,32 12L32,12A4,4 0,0 1,28 8L28,8A4,4 0,0 1,32 4z"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#333"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M32,22L32,22A4,4 0,0 1,36 26L36,26A4,4 0,0 1,32 30L32,30A4,4 0,0 1,28 26L28,26A4,4 0,0 1,32 22z"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#333"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M32,31L32,44"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#333"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M13,17H19"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#333"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M29,17H35"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#333"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M13,36H19"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#333"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M29,36H35"
      android:strokeLineJoin="round"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#333"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M16,14L16,20"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#333"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M32,14L32,20"
      android:strokeWidth="3"
      android:fillColor="#00000000"
      android:strokeColor="#333"
      android:strokeLineCap="round"/>
</vector>
