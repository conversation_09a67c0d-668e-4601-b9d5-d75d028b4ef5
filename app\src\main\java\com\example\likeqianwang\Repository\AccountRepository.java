package com.example.likeqianwang.Repository;

import android.app.Application;
import android.os.Handler;
import android.os.Looper;

import androidx.lifecycle.LiveData;

import com.example.likeqianwang.Dao.AccountDao;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.Account;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class AccountRepository {
    private final AccountDao accountDao;
    private final ExecutorService executorService;

    public AccountRepository(Application application) {
        AppDatabase database = AppDatabase.getInstance(application);
        accountDao = database.accountDao();
        executorService = Executors.newSingleThreadExecutor();
    }

    /**
     * 异步插入账户
     */
    public void insert(Account account) {
        AppDatabase.databaseWriteExecutor.execute(() -> {
            accountDao.insert(account);
        });
    }

    /**
     * 异步更新账户
     */
    public void update(Account account) {
        AppDatabase.databaseWriteExecutor.execute(() -> {
            accountDao.update(account);
        });
    }

    /**
     * 异步删除账户
     */
    public void delete(Account account) {
        AppDatabase.databaseWriteExecutor.execute(() -> {
            accountDao.delete(account);
        });
    }

    /**
     * 根据ID获取账户
     */
    public LiveData<Account> getAccountById(String id) {
        return accountDao.getAccountById(id);
    }

    /**
     * 获取所有账户
     */
    public LiveData<List<Account>> getAllAccounts() {
        return accountDao.getAllAccounts();
    }

    /**
     * 获取所有计入总资产的账户
     */
    public LiveData<List<Account>> getIncludedAccounts() {
        return accountDao.getIncludedAccounts();
    }

    /**
     * 根据账户类型获取账户
     */
    public LiveData<List<Account>> getAccountsByCategory(String category) {
        return accountDao.getAccountsByCategoryId(category);
    }

    // 添加同步获取所有账户的方法
    public List<Account> getAllAccountsSync() {
        return accountDao.getAllAccountsSync();
    }

    /**
     * 获取所有借记卡/储蓄卡账户
     */
    public LiveData<List<Account>> getDebitAccounts() {
        return accountDao.getDebitAccounts();
    }

    /**
     * 获取所有信用卡账户
     */
    public LiveData<List<Account>> getCreditAccounts() {
        return accountDao.getCreditAccounts();
    }

    /**
     * 获取所有账户类型分类
     */
    public void getAllAccountTypeCategories(OnCategoriesLoadedListener listener) {
        AppDatabase.databaseWriteExecutor.execute(() -> {
            List<String> categoryIds = accountDao.getAllAccountTypeCategories();
            Map<String, String> categoryMap = new HashMap<>();

            for (String categoryId : categoryIds) {
                if (categoryId != null && !categoryId.isEmpty()) {
                    String categoryName = getCategoryNameById(categoryId);
                    categoryMap.put(categoryId, categoryName);
                }
            }

            if (categoryMap.isEmpty()) {
                categoryMap.put("1", "资产账户");
                categoryMap.put("2", "负债账户");
            }

            // 在主线程上返回结果
            new Handler(Looper.getMainLooper()).post(() -> {
                listener.onCategoriesLoaded(categoryMap);
            });
        });
    }

    // 回调接口
    public interface OnCategoriesLoadedListener {
        void onCategoriesLoaded(Map<String, String> categories);
    }

    // 根据分类ID获取分类名称
    private String getCategoryNameById(String categoryId) {
        // 这里可以根据您的业务逻辑来确定分类名称
        switch (categoryId) {
            case "cat_cash":
                return "现金账户";
            case "cat_bank":
                return "银行账户";
            case "cat_digital":
                return "数字账户";
            default:
                return "其他账户";
        }
    }

    /**
     * 异步删除账户并提供回调
     */
    public void deleteAccount(Account account, OnDeleteAccountListener listener) {
        AppDatabase.databaseWriteExecutor.execute(() -> {
            try {
                accountDao.delete(account);
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onDeleteAccount(true);
                    }
                });
            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (listener != null) {
                        listener.onDeleteAccount(false);
                    }
                });
            }
        });
    }

    // 删除账户回调接口
    public interface OnDeleteAccountListener {
        void onDeleteAccount(boolean success);
    }
}
