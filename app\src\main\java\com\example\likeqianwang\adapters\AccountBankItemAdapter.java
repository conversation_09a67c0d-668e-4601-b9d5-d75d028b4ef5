package com.example.likeqianwang.adapters;

import android.content.Context;
import android.util.LruCache;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.widget.Filter;
import android.widget.Filterable;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.DiffUtil;
import androidx.recyclerview.widget.RecyclerView;

import com.example.likeqianwang.DataModel.AccountBankItem;
import com.example.likeqianwang.databinding.StyleAccountBankItemViewBinding;
import com.google.android.material.imageview.ShapeableImageView;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

public class AccountBankItemAdapter extends RecyclerView.Adapter<AccountBankItemAdapter.BankViewHolder> implements Filterable {
    private final List<AccountBankItem> bankList;
    private final List<AccountBankItem> bankListFull;
    private final OnBankSelectedListener onBankSelectedListener;
    private Context context;

    // 创建图片缓存
    private LruCache<Integer, Integer> iconCache;

    // 创建过滤结果缓存
    private LruCache<String, List<AccountBankItem>> filterResultCache;

    // 使用线程池进行后台过滤操作
    private final Executor filterExecutor = Executors.newSingleThreadExecutor();

    public interface OnBankSelectedListener {
        void onBankSelected(AccountBankItem bank);
    }

    public AccountBankItemAdapter(List<AccountBankItem> bankList, OnBankSelectedListener onBankSelectedListener) {
        this.bankList = bankList;
        this.bankListFull = new ArrayList<>(bankList);
        this.onBankSelectedListener = onBankSelectedListener;

        // 初始化图标缓存 (最多缓存30个图标)
        iconCache = new LruCache<>(30);

        // 初始化过滤结果缓存 (最多缓存20个搜索结果)
        filterResultCache = new LruCache<>(20);
    }

    @NonNull
    @Override
    public AccountBankItemAdapter.BankViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (context == null) {
            context = parent.getContext();
        }
        StyleAccountBankItemViewBinding accountBankItemViewBinding = StyleAccountBankItemViewBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false);
        return new BankViewHolder(accountBankItemViewBinding);
    }

    @Override
    public void onBindViewHolder(@NonNull AccountBankItemAdapter.BankViewHolder holder, int position) {
        AccountBankItem bank = bankList.get(position);

        // 从缓存获取图标资源ID，如果没有则添加到缓存
        Integer cachedIconResId = iconCache.get(bank.getIconResId());
        if (cachedIconResId == null) {
            cachedIconResId = bank.getIconResId();
            iconCache.put(bank.getIconResId(), cachedIconResId);
        }

        holder.bankIcon.setImageResource(cachedIconResId);
        holder.bankName.setText(bank.getName());

        holder.itemView.setOnClickListener(v -> {
            if (onBankSelectedListener != null) {
                onBankSelectedListener.onBankSelected(bank);
            }
        });
    }

    @Override
    public int getItemCount() {
        return bankList.size();
    }

    /**
     * 使用DiffUtil更新列表数据
     * @param newBankList 新的银行列表
     */
    public void updateBankList(List<AccountBankItem> newBankList) {
        // 使用DiffUtil计算新旧列表的差异
        DiffUtil.DiffResult diffResult = DiffUtil.calculateDiff(new DiffUtil.Callback() {
            @Override
            public int getOldListSize() {
                return bankList.size();
            }

            @Override
            public int getNewListSize() {
                return newBankList.size();
            }

            @Override
            public boolean areItemsTheSame(int oldItemPosition, int newItemPosition) {
                return bankList.get(oldItemPosition).getId().equals(
                        newBankList.get(newItemPosition).getId());
            }

            @Override
            public boolean areContentsTheSame(int oldItemPosition, int newItemPosition) {
                AccountBankItem oldItem = bankList.get(oldItemPosition);
                AccountBankItem newItem = newBankList.get(newItemPosition);
                return oldItem.getName().equals(newItem.getName()) &&
                        oldItem.getIconResId() == newItem.getIconResId();
            }
        });

        // 更新数据并通知适配器
        bankList.clear();
        bankList.addAll(newBankList);
        diffResult.dispatchUpdatesTo(this);
    }

    @Override
    public Filter getFilter() {
        return bankFilter;
    }

    private Filter bankFilter = new Filter() {
        @Override
        protected FilterResults performFiltering(CharSequence constraint) {
            List<AccountBankItem> filteredList;

            if (constraint == null || constraint.length() == 0) {
                filteredList = new ArrayList<>(bankListFull);
            } else {
                String filterPattern = constraint.toString().toLowerCase().trim();

                // 检查缓存中是否有该搜索结果
                filteredList = filterResultCache.get(filterPattern);

                if (filteredList == null) {
                    // 如果缓存中没有，则执行过滤
                    filteredList = new ArrayList<>();
                    for (AccountBankItem item : bankListFull) {
                        if (item.getName().toLowerCase().contains(filterPattern)) {
                            filteredList.add(item);
                        }
                    }

                    // 将结果存入缓存
                    filterResultCache.put(filterPattern, new ArrayList<>(filteredList));
                }
            }

            FilterResults results = new FilterResults();
            results.values = filteredList;
            return results;
        }

        @Override
        protected void publishResults(CharSequence constraint, FilterResults results) {
            // 使用DiffUtil更新列表，而不是直接清空和添加
            updateBankList((List<AccountBankItem>) results.values);
        }
    };

    /**
     * 执行异步过滤
     * @param constraint 过滤条件
     */
    public void performAsyncFiltering(final CharSequence constraint) {
        filterExecutor.execute(() -> {
            // 在主线程更新UI
            if (context != null) {
                new android.os.Handler(context.getMainLooper()).post(() ->
                        getFilter().filter(constraint)
                );
            }
        });
    }

    /**
     * 清除所有缓存
     */
    public void clearCache() {
        iconCache.evictAll();
        filterResultCache.evictAll();
    }

    public static class BankViewHolder extends RecyclerView.ViewHolder {
        ShapeableImageView bankIcon;
        TextView bankName;

        public BankViewHolder(@NonNull StyleAccountBankItemViewBinding accountBankItemViewBinding) {
            super(accountBankItemViewBinding.getRoot());
            bankIcon = accountBankItemViewBinding.ivWalletsAccountBankIcon;
            bankName = accountBankItemViewBinding.tvWalletsAccountBankName;
        }
    }
}
