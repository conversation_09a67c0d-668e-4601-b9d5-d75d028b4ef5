package com.example.likeqianwang.ViewModel;

import android.app.Application;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;

import com.example.likeqianwang.Dao.TransactionDao;
import com.example.likeqianwang.Database.AppDatabase;
import com.example.likeqianwang.Entity.Transactions;
import com.example.likeqianwang.Repository.TransactionRepository;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

public class ReceiptViewModel extends AndroidViewModel {
    private final TransactionRepository repository;
    private final TransactionDao transactionDao;

    // 当前选择的年月
    private final MutableLiveData<Date> currentYearMonth = new MutableLiveData<>();

    // 当前月份的交易数据
    private final LiveData<List<Transactions>> monthlyTransactions;

    // 按日期分组的交易数据
    private final MutableLiveData<Map<String, List<Transactions>>> groupedTransactions = new MutableLiveData<>();

    // 月度收支汇总数据
    private final MutableLiveData<TransactionDao.TransactionSummary> monthlyTransactionSummary = new MutableLiveData<>();

    // 日期格式化器
    private final SimpleDateFormat dateKeyFormatter = new SimpleDateFormat("yyyy-MM-dd", Locale.getDefault());
    private final SimpleDateFormat displayDateFormatter = new SimpleDateFormat("MM/dd EEEE", Locale.getDefault());
    private final SimpleDateFormat yearMonthFormatter = new SimpleDateFormat("yyyy年MM月", Locale.getDefault());

    public ReceiptViewModel(@NonNull Application application) {
        super(application);
        AppDatabase database = AppDatabase.getInstance(application);
        this.repository = new TransactionRepository(database);
        this.transactionDao = database.transactionDao();

        // 初始化当前年月为本月
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        currentYearMonth.setValue(calendar.getTime());

        // 监听年月变化，获取对应的交易数据
        monthlyTransactions = Transformations.switchMap(currentYearMonth, yearMonth -> {
            if (yearMonth != null) {
                Calendar startCal = Calendar.getInstance();
                startCal.setTime(yearMonth);
                startCal.set(Calendar.DAY_OF_MONTH, 1);
                startCal.set(Calendar.HOUR_OF_DAY, 0);
                startCal.set(Calendar.MINUTE, 0);
                startCal.set(Calendar.SECOND, 0);
                startCal.set(Calendar.MILLISECOND, 0);

                Calendar endCal = Calendar.getInstance();
                endCal.setTime(yearMonth);
                endCal.set(Calendar.DAY_OF_MONTH, endCal.getActualMaximum(Calendar.DAY_OF_MONTH));
                endCal.set(Calendar.HOUR_OF_DAY, 23);
                endCal.set(Calendar.MINUTE, 59);
                endCal.set(Calendar.SECOND, 59);
                endCal.set(Calendar.MILLISECOND, 999);

                return transactionDao.getTransactionsByDateRange(startCal.getTime(), endCal.getTime());
            }
            return new MutableLiveData<>(new ArrayList<>());
        });

        // 监听交易数据变化，进行分组处理
        monthlyTransactions.observeForever(this::groupTransactionsByDate);

        // 监听月度交易数据变化，计算汇总信息
        monthlyTransactions.observeForever(this::calculateMonthlySummary);
    }

    /**
     * 获取当前年月
     */
    public LiveData<Date> getCurrentYearMonth() {
        return currentYearMonth;
    }

    /**
     * 获取当前年月的显示文本
     */
    public LiveData<String> getCurrentYearMonthText() {
        return Transformations.map(currentYearMonth, date -> {
            if (date != null) {
                return yearMonthFormatter.format(date);
            }
            return "";
        });
    }

    /**
     * 获取当前月份的交易数据
     */
    public LiveData<List<Transactions>> getMonthlyTransactions() {
        return monthlyTransactions;
    }

    /**
     * 获取按日期分组的交易数据
     */
    public LiveData<Map<String, List<Transactions>>> getGroupedTransactions() {
        return groupedTransactions;
    }

    /**
     * 设置当前年月
     */
    public void setCurrentYearMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        currentYearMonth.setValue(calendar.getTime());
    }

    /**
     * 切换到上一个月
     */
    public void previousMonth() {
        Date current = currentYearMonth.getValue();
        if (current != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(current);
            calendar.add(Calendar.MONTH, -1);
            currentYearMonth.setValue(calendar.getTime());
        }
    }

    /**
     * 切换到下一个月
     */
    public void nextMonth() {
        Date current = currentYearMonth.getValue();
        if (current != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(current);
            calendar.add(Calendar.MONTH, 1);
            currentYearMonth.setValue(calendar.getTime());
        }
    }

    /**
     * 将交易数据按日期分组
     */
    private void groupTransactionsByDate(List<Transactions> transactions) {
        Map<String, List<Transactions>> grouped = new HashMap<>();

        if (transactions != null) {
            for (Transactions transaction : transactions) {
                String dateKey = dateKeyFormatter.format(transaction.getTransactionDate());

                if (!grouped.containsKey(dateKey)) {
                    grouped.put(dateKey, new ArrayList<>());
                }
                grouped.get(dateKey).add(transaction);
            }
        }

        groupedTransactions.setValue(grouped);
    }

    /**
     * 格式化日期显示
     */
    public String formatDateForDisplay(String dateKey) {
        try {
            Date date = dateKeyFormatter.parse(dateKey);
            if (date != null) {
                return displayDateFormatter.format(date);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return dateKey;
    }

    /**
     * 获取指定日期的交易统计
     */
    public TransactionDao.TransactionSummary getDailyTransactionSummary(String dateKey) {
        try {
            Date date = dateKeyFormatter.parse(dateKey);
            if (date != null) {
                return transactionDao.getDailyTransactionSummary(date);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new TransactionDao.TransactionSummary();
    }

    /**
     * 设置年月
     */
    public void setYearMonth(int year, int month) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.YEAR, year);
        calendar.set(Calendar.MONTH, month - 1); // Calendar.MONTH is 0-based
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);

        currentYearMonth.setValue(calendar.getTime());
    }

    /**
     * 获取当前选择的年份
     */
    public int getCurrentYear() {
        Date current = currentYearMonth.getValue();
        if (current != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(current);
            return calendar.get(Calendar.YEAR);
        }
        return Calendar.getInstance().get(Calendar.YEAR);
    }

    /**
     * 获取当前选择的月份
     */
    public int getCurrentMonth() {
        Date current = currentYearMonth.getValue();
        if (current != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(current);
            return calendar.get(Calendar.MONTH) + 1; // Calendar.MONTH is 0-based
        }
        return Calendar.getInstance().get(Calendar.MONTH) + 1;
    }

    /**
     * 插入交易
     */
    public void insertTransaction(Transactions transaction, TransactionOperationCallback callback) {
        repository.insertTransaction(transaction, new TransactionRepository.TransactionOperationCallback() {
            @Override
            public void onSuccess() {
                // 刷新数据
                refreshData();
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onError(String error) {
                if (callback != null) {
                    callback.onError(error);
                }
            }
        });
    }

    /**
     * 删除交易
     */
    public void deleteTransaction(Transactions transaction, TransactionOperationCallback callback) {
        repository.deleteTransaction(transaction, new TransactionRepository.TransactionOperationCallback() {
            @Override
            public void onSuccess() {
                // 刷新数据
                refreshData();
                if (callback != null) {
                    callback.onSuccess();
                }
            }

            @Override
            public void onError(String error) {
                if (callback != null) {
                    callback.onError(error);
                }
            }
        });
    }

    /**
     * 强制刷新数据
     */
    public void refreshData() {
        Date current = currentYearMonth.getValue();
        if (current != null) {
            // 重新设置当前月份，触发数据重新加载
            currentYearMonth.setValue(current);
        }
    }

    // 回调接口
    public interface TransactionOperationCallback {
        void onSuccess();
        void onError(String error);
    }

    /**
     * 计算月度收支汇总数据
     */
    private void calculateMonthlySummary(List<Transactions> transactions) {
        if (transactions == null || transactions.isEmpty()) {
            monthlyTransactionSummary.setValue(new TransactionDao.TransactionSummary());
            return;
        }

        BigDecimal totalIncome = BigDecimal.ZERO;
        BigDecimal totalExpense = BigDecimal.ZERO;

        for (Transactions transaction : transactions) {
            if (transaction.isIncludeInStats()) {
                switch (transaction.getType()) {
                    case "INCOME":
                        totalIncome = totalIncome.add(transaction.getAmount());
                        break;
                    case "EXPENSE":
                        totalExpense = totalExpense.add(transaction.getAmount());
                        break;
                    // TRANSFER 不计入收支统计
                }
            }
        }

        TransactionDao.TransactionSummary summary = new TransactionDao.TransactionSummary();
        summary.totalIncome = totalIncome;
        summary.totalExpense = totalExpense;

        monthlyTransactionSummary.setValue(summary);
    }

    /**
     * 获取月度收支汇总数据
     */
    public LiveData<TransactionDao.TransactionSummary> getMonthlyTransactionSummary() {
        return monthlyTransactionSummary;
    }

}
