package com.example.likeqianwang.Dao;

import androidx.lifecycle.LiveData;
import androidx.room.Dao;
import androidx.room.Delete;
import androidx.room.Insert;
import androidx.room.OnConflictStrategy;
import androidx.room.Query;
import androidx.room.Update;

import com.example.likeqianwang.Entity.TransactionTag;

import java.util.List;

@Dao
public interface TransactionTagDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    long insert(TransactionTag transactionTag);

    @Update
    void update(TransactionTag transactionTag);

    @Delete
    void delete(TransactionTag transactionTag);

    @Query("SELECT * FROM transaction_tags ORDER BY order_index ASC, tag_name ASC")
    LiveData<List<TransactionTag>> getAllTags();

    @Query("SELECT * FROM transaction_tags ORDER BY order_index ASC, tag_name ASC")
    List<TransactionTag> getAllTagsSync();

    @Query("SELECT * FROM transaction_tags WHERE tag_category = :category ORDER BY order_index ASC, tag_name ASC")
    LiveData<List<TransactionTag>> getTagsByCategory(String category);

    @Query("SELECT * FROM transaction_tags WHERE tag_category = :category ORDER BY order_index ASC, tag_name ASC")
    List<TransactionTag> getTagsByCategorySync(String category);

    @Query("SELECT DISTINCT tag_category FROM transaction_tags WHERE tag_category IS NOT NULL ORDER BY tag_category ASC")
    LiveData<List<String>> getAllCategories();

    @Query("SELECT DISTINCT tag_category FROM transaction_tags WHERE tag_category IS NOT NULL ORDER BY tag_category ASC")
    List<String> getAllCategoriesSync();

    @Query("SELECT * FROM transaction_tags WHERE tagId = :tagId")
    TransactionTag getTagById(long tagId);

    @Query("SELECT * FROM transaction_tags WHERE tagId IN (:tagIds)")
    List<TransactionTag> getTagsByIds(List<Long> tagIds);

    @Query("DELETE FROM transaction_tags WHERE tagId = :tagId")
    void deleteById(long tagId);

    @Query("SELECT COUNT(*) FROM transaction_tags")
    int getTagsCount();

    // 分类管理相关方法
    @Query("UPDATE transaction_tags SET tag_category = :newCategory WHERE tag_category = :oldCategory")
    void updateCategoryForAllTags(String oldCategory, String newCategory);

    @Query("DELETE FROM transaction_tags WHERE tag_category = :category")
    void deleteTagsByCategory(String category);
}
